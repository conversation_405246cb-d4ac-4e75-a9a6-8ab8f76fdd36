<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>trantor-org</artifactId>
        <groupId>io.terminus.trantor</groupId>
        <version>1.0.1.JASOLAR.DEV-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>trantor-org-spi</artifactId>

    <dependencies>
        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-common-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-doc-engine-runtime-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.erp</groupId>
            <artifactId>erp-framework-spi-strategy</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor2</groupId>
            <artifactId>trantor-service-runtime-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-openfeign</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-common-runtime</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.terminus.trantor2</groupId>
                    <artifactId>trantor-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-spring-boot-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.erp</groupId>
            <artifactId>erp-framework-runtime</artifactId>
        </dependency>
    </dependencies>
</project>

Org.biz.name.is.exist=ä¸å¡ç±»ååç§°éå¤
Org.biz.code.is.exist=ä¸å¡ç±»åç¼ç éå¤
Org.biz.type.is.not.exist=ä¸å¡ç±»åä¸å­å¨
Org.biz.type.has.bind.org.can.not.delete=è¯¥ä¸å¡ç±»åç»å®äºç»ç»ååä¸è½å é¤
Org.employee.code.is.repeat=åå·¥ç¼ç å·²ç»å­å¨
Org.employee.mobile.is.repeat=åå·¥ææºå·ç éå¤
Org.employee.email.is.repeat=åå·¥é®ç®±å·²ç»å­å¨
Org.create.user.fail=åå»ºç¨æ·å¤±è´¥
Org.this.user.has.bind.other.employee=æ¹ç¨ç»å®äºå¶ä»çåå·¥è´¦å·
Org.employee.id.is.not.null=åå·¥IDä¸è½ä¸ºç©º
Org.employee.is.not.exist=åå·¥ä¸å­å¨
Org.user.delete.fail=å é¤ç¨æ·å¤±è´¥
Org.org.unit.name.is.repeat=ç»ç»åååç§°éå¤
Org.org.unit.code.is.repeat=ç»ç»ååç¼ç éå¤
Org.org.unit.parent.is.not.exist=ç»ç»ååä¸çº§ååä¸å­å¨
Org.org.unit.parent.status.is.not.enable=ç»ç»ååä¸çº§ååæªå¯ç¨
Org.org.unit.is.not.exist=ç»ç»ååä¸å­å¨
Org.org.parent.id.is.illegal=ä¸çº§ååä¸è½æ¯èªå·±æ¬èº«
Org.org.unit.status.is.not.draft.can.not.delete=ç»ç»ååéèç¨¿æï¼ä¸è½å é¤
Org.org.unit.status.is.not.draft.or.disable=ç»ç»ååç¶æä¸æ­£ç¡®ï¼ä¸è½å¯ç¨
Org.org.unit.status.is.not.enable=ç»ç»ååç¶æä¸æ­£ç¡®ï¼ä¸è½ç¦ç¨
Org.org.unit.has.enable.children.can.not.disable=æ¹ç»ç»ååå­å¨å¯ç¨çä¸çº§ååï¼ä¸è½ç¦ç¨
Org.create.biz.type.extra.fail=åå»ºç»ç»ååæ©å±æ°æ®å¤±è´¥
Org.delete.biz.type.extra.fail=å é¤ç»ç»ååæ©å±æ°æ®å¤±è´¥
Org.update.biz.type.extra.fail=è·çç»ç»ååæ©å±æ°æ®å¤±è´¥
Inventory.org.not.config.user.group.please.update.org=ç»ç»ååæªéç½®ç¨æ·ç»ï¼è¯·æ´æ°ç»ç»åå
Org.dimension.cf.code.is.null=ç»ç»ç»´åº¦ä»£ç ä¸ºç©º
Org.dimension.cf.is.null=ç»ç»ç»´åº¦ä¸ºç©º
Org.code.is.exist=ç»ç»codeå·²å­å¨ï¼è¯·ä¿®æ¹ååä¿å­
Org.struct.is.disable=æ¨ä¸è½å¨åç¨ç»ç»ä¸æ°å¢ç»ç»
Org.struct.is.enable.date.is.before.parent=å¯å¨æ¶é´ä¸è½æ©äºç¶ç»ç»å¯å¨æ¶é´
Org.parent.id.is.null=ç»ç»ç»´åº¦ä»£ç ä¸ºç©º
Org.parent.is.not.enable=è¯·åå°è¯¥ç»ç»çä¸çº§ç»ç»å¯ç¨ï¼åå¯ç¨è¯¥ç»ç»
Org.struct.member.is.repeat=ç»ç»æåä¿¡æ¯å­å¨éå¤çæ°æ®
Org.struct.member.is.exist=æåä¿¡æ¯å·²ç»å­å¨
Org.struct.parent.error=ä¸è½éæ©å½åèç¹ä»¥åå½åèç¹ä¸çº§èç¹ä½ä¸ºç¶ç»ç»
origin.org.id.required=å½åç»å½äººçç»ç»ä¸ºç©º
Org.switch.model.is.exist=ç»ç»åæ¢æ¨¡åå·²å­å¨
Org.current.org.dimension.not.support.multi.root=å½åç»´åº¦ä¸æ¯æåå»ºå¤ä¸ªæ ¹èç¹
Org.struct.type.rule.fail=ç»ç»ç±»åä¸å¹é
Org.relation.rule.cf.is.null=å³èè§åæªè®¾ç½®ï¼æ æ³ç¡®è®¤
Org.relation.rule.check=è¯¥ç»ç»åªè½å³èä¸ä¸ªç¸åç±»åçç»ç»
Org.relation.rule.cf.is.exist=å³èè§åå·²è®¾ç½®
Org.relation.rule.cf.check.fail=å³èè§åæªè®¾ç½®ï¼æ æ³å¯ç¨
Org.current.com.org.is.not.correct=å½åçå¬å¸ç»ç»éå½åèç¹ä¸çº§å¯¹åºçå¬å¸ç»ç»
Org.please.choose.dimension.first=è¯·åéæ©ç»ç»ç»´åº¦
Org.relation.is.exist=ç»ç»å³èå³ç³»å·²å­å¨
Org.org.import.please.choose.dimension=è¯·åéæ©ç»ç»ç»´åº¦
Org.notice.scene.is.not.unique=å­å¨éå¤çéç¥åºæ¯
Org.employee.phone.number.is.not.valid=ææºå·ç ä¸åæ³
Org.biz.name.is.exist=Business type name duplication
Org.biz.code.is.exist=Business type code duplication
Org.biz.type.is.not.exist=Business type does not exist
Org.biz.type.has.bind.org.can.not.delete=This business type is bound to an organizational unit and cannot be deleted
Org.employee.code.is.repeat=Employee code already exists
Org.employee.mobile.is.repeat=Employee phone number duplication
Org.employee.email.is.repeat=Employee email already exists
Org.create.user.fail=Failed to create user
Org.this.user.has.bind.other.employee=Changed to bind another employee account
Org.employee.id.is.not.null=Employee ID cannot be empty
Org.employee.is.not.exist=Employee does not exist
Org.user.delete.fail=Failed to delete user
Org.org.unit.name.is.repeat=Organizational unit name duplication
Org.org.unit.code.is.repeat=Organizational unit code duplication
Org.org.unit.parent.is.not.exist=Higher-level unit does not exist
Org.org.unit.parent.status.is.not.enable=Higher-level unit is not enabled
Org.org.unit.is.not.exist=Organizational unit does not exist
Org.org.parent.id.is.illegal=Higher-level unit cannot be itself
Org.org.unit.status.is.not.draft.can.not.delete=Organizational unit is not in draft state and cannot be deleted
Org.org.unit.status.is.not.draft.or.disable=Organizational unit status is incorrect and cannot be enabled
Org.org.unit.status.is.not.enable=Organizational unit status is incorrect and cannot be disabled
Org.org.unit.has.enable.children.can.not.disable=Cannot change organizational unit because there are enabled lower-level units, cannot be disabled
Org.create.biz.type.extra.fail=Failed to create organizational unit extension data
Org.delete.biz.type.extra.fail=Failed to delete organizational unit extension data
Org.update.biz.type.extra.fail=Follow organizational unit extension data failed
Inventory.org.not.config.user.group.please.update.org=Organizational unit is not assigned to a user group, please update the organizational unit
Org.dimension.cf.code.is.null=Organizational dimension code is empty
Org.dimension.cf.is.null=Organizational dimension is empty
Org.code.is.exist=Organizational code already exists, please modify and save again
Org.struct.is.disable=You cannot create a new organization under a suspended organization
Org.struct.is.enable.date.is.before.parent=Startup time cannot be earlier than the parent organization startup time
Org.parent.id.is.null=Organizational dimension code is empty
Org.parent.is.not.enable=Please enable the higher-level organization of this organization before enabling this organization
Org.struct.member.is.repeat=There are duplicate member information in the organization
Org.struct.member.is.exist=Member information already exists
Org.struct.parent.error=Cannot select the current node and the sub-node of the current node as the parent organization
origin.org.id.required=The organization of the current logged-in person is empty
Org.switch.model.is.exist=Organization switch model already exists
Org.current.org.dimension.not.support.multi.root=The current dimension does not support creating multiple root nodes
Org.struct.type.rule.fail=Organization type does not match
Org.relation.rule.cf.is.null=Association rule is not set, cannot confirm
Org.relation.rule.check=This organization can only associate with one organization of the same type
Org.relation.rule.cf.is.exist=Association rule is set
Org.relation.rule.cf.check.fail=Association rule is not set, cannot enable.
Org.current.com.org.is.not.correct=The current company organization is not the company organization corresponding to the parent of the current node
Org.please.choose.dimension.first=Please select the organizational dimension first
Org.relation.is.exist=Organizational association relationship already exists
Org.org.import.please.choose.dimension=Please select the organizational dimension first
Org.notice.scene.is.not.unique=There are duplicate notification scenes
Org.employee.phone.number.is.not.valid=Phone number is not valid

package io.terminus.trantor.org.spi.model.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (OrgRankCf)传输模型
 *
 * <AUTHOR>
 * @since 2023-10-23 16:39:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgRankCfDTO extends BaseModel {
    private static final long serialVersionUID = -59934869011695313L;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("状态")
    private String status;

}

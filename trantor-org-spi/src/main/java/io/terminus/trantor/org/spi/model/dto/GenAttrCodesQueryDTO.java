package io.terminus.trantor.org.spi.model.dto;

import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: 张博
 * @date: 2024-05-23 19:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GenAttrCodesQueryDTO extends BaseModel {
    private static final long serialVersionUID = 616263550117433254L;

    private List<String> codes;
}

package io.terminus.trantor.org.spi.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织单元表(Unit)实体类
 *
 * <AUTHOR>
 * @since 2023-07-11 20:42:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_org_unit_cf", excludeProperty = {"extra"})
public class OrgUnitPO extends BaseModel {
    private static final long serialVersionUID = 946186917749780988L;

    @ApiModelProperty("组织编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("组织名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("父ID")
    @TableField("`pid`")
    private Long pid;

    @ApiModelProperty("是否有下级节点")
    @TableField("`has_child`")
    private Boolean hasChild;

    @ApiModelProperty("组织状态")
    @TableField("`status`")
    private String status;

    @ApiModelProperty("路径")
    @TableField("`path`")
    private String path;

    @ApiModelProperty("用户组")
    @TableField("`user_group`")
    private Long userGroup;
}

package io.terminus.trantor.org.spi.dict;

/**
 * @Author：王超
 * @Date: 2023-11-17 09:36
 */

public interface AttrDataTypeDict {

    /**
     * 文本
     */
    String CHAR = "CHAR";
    /**
     * 布尔
     */
    String BOOLEAN = "BOOLEAN";
    /**
     * 对象
     */
    String OBJECT = "OBJECT";
    /**
     * 字典
     */
    String DICTIONARY = "DICTIONARY";
    /**
     * 日期
     */
    String DATE = "DATE";
    /**
     * 时间
     */
    String TIME = "TIME";
    /**
     * 附件
     */
    String ANNEX = "ANNEX";
    /**
     * 数字
     */
    String NUMBER = "NUMBER";
    /**
     * 多行文本
     */
    String MULTICHAR = "MULTICHAR";
}

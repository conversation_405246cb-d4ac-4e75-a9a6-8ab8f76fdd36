package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import io.terminus.common.api.util.AssertUtils;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: 张博
 * @date: 2023-02-27 19:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EmployeeSaveDTO extends AbstractRequest {
    private static final long serialVersionUID = -2777906848245838748L;

    @ApiModelProperty("员工编码")
    private String code;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("组织")
    @MetaModelField
    private Long orgStructId;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("手机号码")
    private String mobile;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("地址")
    private Long addressId;

    @ApiModelProperty("身份证号")
    private String idCard;

    @ApiModelProperty("详细地址")
    private String addressDetail;

    @ApiModelProperty("入职日期")
    private LocalDateTime entryAt;

    @ApiModelProperty("离职日期")
    private LocalDateTime resignationAt;

    @ApiModelProperty("钉钉通知")
    private String dingTalkCode;

    @ApiModelProperty("关联的组织集合")
    private List<EmployeeOrgLinkDTO> employeeOrgLinkList;

    @ApiModelProperty("员工角色关联信息")
    private List<EmployeeRoleLinkDTO> employeeRoleLinkList;

    @ApiModelProperty("员工消息通知关联信息")
    private List<EmployeeNoticeSceneLinkDTO> noticeSceneList;

    @ApiModelProperty("是否处理角色")
    private Boolean isHandleRole = true;

    @Override
    public void checkParam() {
        super.checkParam();
        AssertUtils.notEmpty(name, "Org.employee.name.is.empty");
        AssertUtils.notEmpty(type, "Org.employee.type.is.empty");
        AssertUtils.notEmpty(code, "Org.employee.code.is.empty");
        AssertUtils.notEmpty(mobile, "Org.employee.mobile.is.empty");
    }
}

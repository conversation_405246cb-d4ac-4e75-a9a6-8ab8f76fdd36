package io.terminus.trantor.org.spi.model.dto.dict;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：王超
 * @Date: 2023-11-22 21:44
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class DictHeadPageQueryDTO extends AbstractPageRequest {

    private static final long serialVersionUID = -30213943375288599L;

    @ApiModelProperty("字典类别编码")
    private String code;

    @ApiModelProperty("字典类别名称")
    private String name;

    @ApiModelProperty("字典类别状态")
    private String status;
}

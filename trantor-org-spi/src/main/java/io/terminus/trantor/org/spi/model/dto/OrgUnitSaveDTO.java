package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import io.terminus.common.api.util.AssertUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-02-27 19:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgUnitSaveDTO extends AbstractRequest {
    private static final long serialVersionUID = -2777906848245838748L;

    @ApiModelProperty("组织编码")
    private String code;

    @ApiModelProperty("组织名称")
    private String name;

    @ApiModelProperty("父ID")
    private Long pid;

    @ApiModelProperty("业务类型数据集合")
    private List<BizTypeParamDTO> params;

    @ApiModelProperty("组织角色关联信息")
    private List<OrgRoleLinkDTO> orgRoleLinkList;

    @Override
    public void checkParam() {
        super.checkParam();
        AssertUtils.notEmpty(name, "Org.org.unit.name.is.empty");
        AssertUtils.notEmpty(code, "Org.org.unit.code.is.empty");
    }
}

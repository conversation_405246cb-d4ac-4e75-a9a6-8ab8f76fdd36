package io.terminus.trantor.org.spi.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织业务类型关联表(BizTypeLink)实体类
 *
 * <AUTHOR>
 * @since 2023-02-27 20:42:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_org_biz_type_link_cf", excludeProperty = {"extra"})
public class OrgBizTypeLinkPO extends BaseModel {
    private static final long serialVersionUID = -64861973079643264L;

    @ApiModelProperty("组织单元ID")
    @TableField("`org_unit_id`")
    private Long orgUnitId;

    @ApiModelProperty("业务类型ID")
    @TableField("`biz_type_id`")
    private Long bizTypeId;

    @TableField(exist = false)
    private Integer total;
}

package io.terminus.trantor.org.spi.model.po;


import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 历史组织版本表(OrgStructEditionMd)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-02 14:31:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_struct_edition_md")
public class OrgStructEditionMdPO extends BaseModel {
    private static final long serialVersionUID = -25045944440513136L;

    @ApiModelProperty("行政组织ID")
    @TableField("`org_struct_id`")
    private Long orgStructId;

    @ApiModelProperty("父组织ID")
    @TableField("`org_edition_parent_id`")
    private Long orgEditionParentId;

    @ApiModelProperty("状态")
    @TableField("`org_edition_status`")
    private String orgEditionStatus;

    @ApiModelProperty("版本日期")
    @TableField("`org_edition_date`")
    private LocalDateTime orgEditionDate;

    @ApiModelProperty("组织维度id")
    @TableField("`org_dimension_id`")
    private Long orgDimensionId;

    @ApiModelProperty("组织维度名称")
    @TableField("`org_his_struct_name`")
    private String orgHisStructName;
}

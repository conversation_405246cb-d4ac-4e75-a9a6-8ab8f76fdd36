package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * @author: 张博
 * @date: 2023-08-25 11:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgUnitQueryDTO extends AbstractRequest {
    private static final long serialVersionUID = 8209281955219148356L;

    @ApiModelProperty("业务类型编码")
    private Set<String> bizTypeCodes;
}

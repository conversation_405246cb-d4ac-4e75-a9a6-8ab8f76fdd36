package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-11-28 19:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LegalComDTO extends BaseModel {
    private static final long serialVersionUID = -4863052257091848715L;

    @ApiModelProperty("组织")
    private Long orgStructId;

    @ApiModelProperty("公司")
    private Long comId;
}

package io.terminus.trantor.org.spi.model.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.trantor.org.spi.model.dto.attr.GenAttrAndGroupDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructMdDetailWithTypeDTO extends OrgDimensionDTO {
    private static final long serialVersionUID = 3899680558809091886L;

    @ApiModelProperty("类型配置属性定义集合")
    private List<GenAttrAndGroupDTO> genAttrAndGroupList;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("父组织ID")
    private Long orgParentId;

    @ApiModelProperty("父组织名称")
    private String orgParentName;

    @ApiModelProperty("父组织Code")
    private String orgParentCode;

    @ApiModelProperty("状态")
    private String orgStatus;

    @ApiModelProperty("启用日期")
    private LocalDateTime orgEnableDate;

    @ApiModelProperty("业务类型ID")
    private List<Long> orgBusinessTypeIds;

    @ApiModelProperty("业务类型编码")
    private List<String> orgBusinessTypeCodes;

    @ApiModelProperty("业务类型名称map")
    private Map<String, String> orgBusinessTypeNameMap;

    @ApiModelProperty("公司组织")
    private Long comOrgId;

    @ApiModelProperty("公司组织名称")
    private String comOrgName;

    @ApiModelProperty("合作伙伴名称")
    private String partnerName;

    @ApiModelProperty("合作伙伴ID")
    private Long partnerId;

    @ApiModelProperty("排序")
    private Integer orgSort;

    @ApiModelProperty("成员信息")
    private List<EmployeeOrgLinkDTO> employeeOrgLinkList;

    @ApiModelProperty("预留字段1")
    private Object def1;

    @ApiModelProperty("预留字段2")
    private Object def2;

    @ApiModelProperty("预留字段3")
    private Object def3;

    @ApiModelProperty("预留字段4")
    private Object def4;

    @ApiModelProperty("预留字段5")
    private Object def5;

    @ApiModelProperty("预留字段6")
    private Object def6;

    @ApiModelProperty("预留字段7")
    private Object def7;

    @ApiModelProperty("预留字段8")
    private Object def8;

    @ApiModelProperty("预留字段9")
    private Object def9;

    @ApiModelProperty("预留字段10")
    private Object def10;

    @ApiModelProperty("预留字段11")
    private Object def11;

    @ApiModelProperty("预留字段12")
    private Object def12;

    @ApiModelProperty("预留字段13")
    private Object def13;

    @ApiModelProperty("预留字段14")
    private Object def14;

    @ApiModelProperty("预留字段15")
    private Object def15;

    @ApiModelProperty("预留字段16")
    private Object def16;

    @ApiModelProperty("预留字段17")
    private Object def17;

    @ApiModelProperty("预留字段18")
    private Object def18;

    @ApiModelProperty("预留字段19")
    private Object def19;

    @ApiModelProperty("预留字段20")
    private Object def20;

    @ApiModelProperty("预留字段21")
    private Object def21;

    @ApiModelProperty("预留字段22")
    private Object def22;

    @ApiModelProperty("预留字段23")
    private Object def23;

    @ApiModelProperty("预留字段24")
    private Object def24;

    @ApiModelProperty("预留字段25")
    private Object def25;

    @ApiModelProperty("预留字段26")
    private Object def26;

    @ApiModelProperty("预留字段27")
    private Object def27;

    @ApiModelProperty("预留字段28")
    private Object def28;

    @ApiModelProperty("预留字段29")
    private Object def29;

    @ApiModelProperty("预留字段30")
    private Object def30;

    @ApiModelProperty("预留字段31")
    private Object def31;

    @ApiModelProperty("预留字段32")
    private Object def32;

    @ApiModelProperty("预留字段33")
    private Object def33;

    @ApiModelProperty("预留字段34")
    private Object def34;

    @ApiModelProperty("预留字段35")
    private Object def35;

    @ApiModelProperty("预留字段36")
    private Object def36;

    @ApiModelProperty("预留字段37")
    private Object def37;

    @ApiModelProperty("预留字段38")
    private Object def38;

    @ApiModelProperty("预留字段39")
    private Object def39;

    @ApiModelProperty("预留字段40")
    private Object def40;

    @ApiModelProperty("附件预留字段1")
    private Object attachment1;

    @ApiModelProperty("附件预留字段2")
    private Object attachment2;

    @ApiModelProperty("创建者名称")
    private String createUserName;

    @ApiModelProperty("更新者名称")
    private String updateUserName;
}

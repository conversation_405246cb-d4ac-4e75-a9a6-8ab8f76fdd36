package io.terminus.trantor.org.spi.model.req;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import io.terminus.common.api.util.AssertUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrgQueryByIdReq extends AbstractRequest {
    private static final long serialVersionUID = 625298116934761748L;

    @ApiModelProperty("模型标识")
    private String modelKey;

    @Override
    public void checkParam() {
        AssertUtils.notEmpty(modelKey, "ORG::model.key.required");
        AssertUtils.nonNull(getId(), "ORG::model.id.required");
    }
}

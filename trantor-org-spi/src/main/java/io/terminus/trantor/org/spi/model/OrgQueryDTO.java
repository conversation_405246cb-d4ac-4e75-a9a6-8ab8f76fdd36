package io.terminus.trantor.org.spi.model;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgQueryDTO extends AbstractRequest {
    private static final long serialVersionUID = -3217465289770532091L;

    @ApiModelProperty("状态")
    private Set<String> status;

    @ApiModelProperty("业务类型编码")
    private Set<String> bizTypeCodes;
}

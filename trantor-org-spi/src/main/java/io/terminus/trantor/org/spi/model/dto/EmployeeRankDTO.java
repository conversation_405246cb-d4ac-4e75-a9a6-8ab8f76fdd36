package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-12-12 19:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EmployeeRankDTO extends BaseModel {
    private static final long serialVersionUID = -5824399255691695613L;

    @ApiModelProperty("员工信息")
    private EmployeeDTO employeeDTO;

    @ApiModelProperty("组织编码")
    private String unitCode;

    @ApiModelProperty("角色编码")
    private String rankCode;
}

package io.terminus.trantor.org.spi.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2024-06-13 16:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_struct_business_type_link")
public class OrgStructBusinessTypeLinkPO extends BaseModel {

    @ApiModelProperty("组织单元")
    @TableField("`org_struct_id`")
    private Long orgStructId;

    @ApiModelProperty("组织业务类型")
    @TableField("`org_business_type_id`")
    private Long orgBusinessTypeId;
}

package io.terminus.trantor.org.spi.model.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据字典项(HrCodeItemCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-02 14:30:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "hr_code_item_cf")
public class HrCodeItemCfPO extends BaseModel {
    private static final long serialVersionUID = -33780812460651693L;

    @ApiModelProperty("项目编码")
    @TableField("`hr_item_code`")
    private String hrItemCode;

    @ApiModelProperty("项目值")
    @TableField("`hr_item_value`")
    private String hrItemValue;

    @ApiModelProperty("序号")
    @TableField("`hr_item_sort`")
    private Integer hrItemSort;

    @ApiModelProperty("是否系统项目")
    @TableField("`hr_is_system`")
    private Boolean hrIsSystem;

    @ApiModelProperty("状态")
    @TableField("`hr_code_item_status`")
    private String hrCodeItemStatus;

    @ApiModelProperty("字典类别ID")
    @TableField("`hr_code_type_cf_id`")
    private Long hrCodeTypeCfId;

}

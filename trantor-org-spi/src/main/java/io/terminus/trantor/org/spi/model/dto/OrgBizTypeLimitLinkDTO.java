package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * <AUTHOR>
 * @since 2023-07-17 20:42:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgBizTypeLimitLinkDTO extends BaseModel {
    private static final long serialVersionUID = -70486868528085223L;


    @ApiModelProperty("下级业务类型ID")
    private Long childBizTypeId;

    @ApiModelProperty("业务类型ID")
    private Long bizTypeId;
}

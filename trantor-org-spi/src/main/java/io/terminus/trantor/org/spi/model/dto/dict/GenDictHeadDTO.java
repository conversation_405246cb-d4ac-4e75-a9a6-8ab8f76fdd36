package io.terminus.trantor.org.spi.model.dto.dict;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据字典类别(GenDictHeadCf)传输模型
 *
 * <AUTHOR>
 * @since 2023-11-22 14:16:27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GenDictHeadDTO extends BaseModel {
    private static final long serialVersionUID = -93186195786390532L;

    @ApiModelProperty("类别编码")
    private String code;

    @ApiModelProperty("类别名称")
    private String name;

    @ApiModelProperty("是否系统类别")
    private Boolean system;

    @ApiModelProperty("状态")
    private String status;

}

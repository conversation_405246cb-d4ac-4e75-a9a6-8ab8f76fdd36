package io.terminus.trantor.org.spi.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructMdTreePathDTO extends BaseModel {
    private static final long serialVersionUID = 5038389808809091886L;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("父组织ID")
    private Long orgParentId;

    @ApiModelProperty("状态")
    private String orgStatus;

    @ApiModelProperty("是否叶子节点")
    private Boolean leaf;

    @ApiModelProperty("路径id集合")
    List<Long> pathIdList;

    @ApiModelProperty("路径名称集合")
    List<String> pathNameList;


}

package io.terminus.trantor.org.spi.model.dto;

import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * @author: 张博
 * @date: 2023-09-07 11:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PageDTO extends AbstractRequest {
    private static final long serialVersionUID = -1882799382259518310L;

    private Map<String, Object> pageable;
}

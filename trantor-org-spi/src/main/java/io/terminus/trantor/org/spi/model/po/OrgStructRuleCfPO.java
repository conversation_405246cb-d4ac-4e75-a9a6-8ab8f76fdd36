package io.terminus.trantor.org.spi.model.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织架构规则设置(OrgStructRuleCf)存储模型
 *
 * <AUTHOR>
 * @since  2024-03-19 10:50:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_struct_rule_cf")
public class OrgStructRuleCfPO extends BaseModel {
    private static final long serialVersionUID = 767191435265575710L;

    @ApiModelProperty("组织维度")
    @TableField("`org_dimension_id`")
    private Long orgDimensionId;

    @ApiModelProperty("组织类型")
    @TableField("`org_dimension_business_link_id`")
    private Long orgDimensionBusinessLinkId;

    @ApiModelProperty("下级组织类型")
    @TableField("`org_child_dimension_business_link_id`")
    private Long orgChildDimensionBusinessLinkId;

    @ApiModelProperty("所属组织")
    @TableField("`origin_org_id`")
    private Long originOrgId;

}

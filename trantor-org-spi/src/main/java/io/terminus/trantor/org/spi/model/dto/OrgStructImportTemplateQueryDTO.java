package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-11-30 19:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructImportTemplateQueryDTO extends AbstractRequest {

    private static final long serialVersionUID = 5792744969859464963L;

    @ApiModelProperty("维度编码")
    private String orgDimensionCode;

    @ApiModelProperty("模版类型")
    private String templateType;
}

package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * @author: 张博
 * @date: 2023-10-23 19:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgRankEmployeeQueryDTO extends BaseModel {
    private static final long serialVersionUID = -8819434823268124395L;

    @ApiModelProperty("组织单元")
    private Long orgUnitId;

    @ApiModelProperty("职级")
    private Set<Long> rankIdSet;

    @ApiModelProperty("是否递归往上查找")
    private Boolean isRecursion;
}

package io.terminus.trantor.org.spi.model.dto;

import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-12-11 16:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class JudgeUserInOrgDTO extends BaseModel {
    private static final long serialVersionUID = -280486307853747930L;

    private Long teamId;

    private Long originOrgId;

    private Long currentUserId;
}

package io.terminus.trantor.org.spi.model.dto;

import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-10-27 13:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizTypeHideFileDTO extends BaseModel {
    private static final long serialVersionUID = -6097196077654622384L;

    private List<String> hideFiledList;
}

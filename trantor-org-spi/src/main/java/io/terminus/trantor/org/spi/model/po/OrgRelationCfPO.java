package io.terminus.trantor.org.spi.model.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * (OrgRelationCf)存储模型
 *
 * <AUTHOR>
 * @since  2024-03-19 15:00:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_relation_cf")
public class OrgRelationCfPO extends BaseModel {
    private static final long serialVersionUID = -41431106405543464L;

    @ApiModelProperty("源组织单元")
    @TableField("`org_head_unit_id`")
    private Long orgHeadUnitId;

    @ApiModelProperty("源组织维度")
    @TableField("`org_head_dimension_id`")
    private Long orgHeadDimensionId;

    @ApiModelProperty("关联组织单元")
    @TableField("`org_relation_unit_id`")
    private Long orgRelationUnitId;

    @ApiModelProperty("关联组织维度")
    @TableField("`org_relation_dimension_id`")
    private Long orgRelationDimensionId;

    @ApiModelProperty("生效时间")
    @TableField("`org_relation_enabled_time`")
    private LocalDateTime orgRelationEnabledTime;

    @ApiModelProperty("失效时间")
    @TableField("`org_relation_disabled_time`")
    private LocalDateTime orgRelationDisabledTime;

    @ApiModelProperty("状态")
    @TableField("`org_relation_status`")
    private String orgRelationStatus;

    @ApiModelProperty("展示路径备注1")
    @TableField("`reamk1`")
    private String reamk1;

    @ApiModelProperty("展示路径备注2")
    @TableField("`reamk2`")
    private String reamk2;

    @ApiModelProperty("源组织单元类型")
    @TableField("`org_head_unit_type_id`")
    private Long orgHeadUnitTypeId;

    @ApiModelProperty("关联组织单元类型")
    @TableField("`org_relation_unit_type_id`")
    private Long orgRelationUnitTypeId;

    @ApiModelProperty("所属组织")
    @TableField("`origin_org_id`")
    private Long originOrgId;

}

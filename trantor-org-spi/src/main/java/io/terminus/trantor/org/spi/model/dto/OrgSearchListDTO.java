package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgSearchListDTO extends BaseModel {

    private static final long serialVersionUID = 5088680558827491886L;



    @ApiModelProperty("组织名称")
    private String name;


    @ApiModelProperty("路径")
    private String path;

    @ApiModelProperty("路径id集合")
    List<Long> pathIdList;

    @ApiModelProperty("路径名称集合")
    List<String> pathNameList;


}

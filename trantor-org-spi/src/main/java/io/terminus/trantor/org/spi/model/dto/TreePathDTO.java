package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
public class TreePathDTO extends BaseModel {

    private static final long serialVersionUID = 5088680558827491956L;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("路径")
    private String path;

}

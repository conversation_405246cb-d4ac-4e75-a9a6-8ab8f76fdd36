package io.terminus.trantor.org.spi.model.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


@EqualsAndHashCode(callSuper = true)
@Data
public class OrgRelationSaveDTO extends BaseModel {
    private static final long serialVersionUID = -41433106405543464L;

    @ApiModelProperty("源组织单元")
    private Long orgHeadUnitId;

    @ApiModelProperty("源组织维度")
    private Long orgHeadDimensionId;

    @ApiModelProperty("关联组织单元")
    private Long orgRelationUnitId;

    @ApiModelProperty("关联组织维度")
    private Long orgRelationDimensionId;

    @ApiModelProperty("生效时间")
    private LocalDateTime orgRelationEnabledTime;

    @ApiModelProperty("失效时间")
    private LocalDateTime orgRelationDisabledTime;

    @ApiModelProperty("状态")
    private String orgRelationStatus;

    @ApiModelProperty("源组织单元类型")
    private Long orgHeadUnitTypeId;

    @ApiModelProperty("关联组织单元类型")
    private Long orgRelationUnitTypeId;

    @ApiModelProperty("所属组织")
    private Long originOrgId;

}

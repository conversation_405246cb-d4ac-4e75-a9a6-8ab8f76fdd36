package io.terminus.trantor.org.spi.model.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 法人公司关联关系表(OrgLegalPersonRelationCf)存储模型
 *
 * <AUTHOR>
 * @since 2023-11-28 19:43:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_legal_person_relation_cf")
public class OrgLegalPersonRelationCfPO extends BaseModel {
    private static final long serialVersionUID = 912997578858498653L;

    @ApiModelProperty("法人公司id")
    @TableField("`gen_com_type_id`")
    private Long genComTypeId;

    @ApiModelProperty("关联组织单元id")
    @TableField("`org_relation_unit_id`")
    private Long orgRelationUnitId;

    @ApiModelProperty("关联组织维度id")
    @TableField("`org_legal_dimension_id`")
    private Long orgLegalDimensionId;

    @ApiModelProperty("关系类型")
    @TableField("`org_legal_type`")
    private Long orgLegalType;

    @ApiModelProperty("生效时间")
    @TableField("`org_legal_enabled_time`")
    private LocalDateTime orgLegalEnabledTime;

    @ApiModelProperty("失效时间")
    @TableField("`org_legal_disenabled_time`")
    private LocalDateTime orgLegalDisEnabledTime;

    @ApiModelProperty("状态")
    @TableField("`org_legal_status`")
    private String orgLegalStatus;

    @ApiModelProperty("关联组织单元类型")
    @TableField("`org_legal_unit_type_id`")
    private Long orgLegalUnitTypeId;

    @ApiModelProperty("路径展示作用")
    @TableField("`remark`")
    private String remark;

}

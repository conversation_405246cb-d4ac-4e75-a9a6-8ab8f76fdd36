package io.terminus.trantor.org.spi.model.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructPageQueryDTO extends AbstractPageRequest {
    private static final long serialVersionUID = 5088680558809091886L;

    @ApiModelProperty("组织id集合")
    private Set<Long> ids;

    @ApiModelProperty("排除的ID集合")
    private Set<Long> excludeIds;

    @ApiModelProperty("维度编码")
    private String orgDimensionCode;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("父组织ID")
    private Long orgParentId;

    @ApiModelProperty("状态")
    private Set<String> orgStatusSet;

    @ApiModelProperty("是否叶子节点")
    private Boolean leaf;

    @ApiModelProperty("组织维度id")
    private Long orgDimensionId;

    @ApiModelProperty("组织业务类型id")
    private Long orgBusinessTypeId;

    @ApiModelProperty("组织业务类型编码")
    private String orgBusinessTypeCode;

    @ApiModelProperty("预留字段1")
    private String def1;

    @ApiModelProperty("预留字段2")
    private String def2;

    @ApiModelProperty("预留字段3")
    private String def3;

    @ApiModelProperty("预留字段4")
    private String def4;

    @ApiModelProperty("预留字段5")
    private String def5;

    @ApiModelProperty("预留字段6")
    private String def6;

    @ApiModelProperty("预留字段7")
    private String def7;

    @ApiModelProperty("预留字段8")
    private String def8;

    @ApiModelProperty("预留字段9")
    private String def9;

    @ApiModelProperty("预留字段10")
    private String def10;

    @ApiModelProperty("预留字段11")
    private String def11;

    @ApiModelProperty("预留字段12")
    private String def12;

    @ApiModelProperty("预留字段13")
    private String def13;

    @ApiModelProperty("预留字段14")
    private String def14;

    @ApiModelProperty("预留字段15")
    private String def15;

    @ApiModelProperty("预留字段16")
    private String def16;

    @ApiModelProperty("预留字段17")
    private String def17;

    @ApiModelProperty("预留字段18")
    private String def18;

    @ApiModelProperty("预留字段19")
    private String def19;

    @ApiModelProperty("预留字段20")
    private String def20;

    @ApiModelProperty("扩展字段查询参数")
    private Map<String, Object> queryParams;
}

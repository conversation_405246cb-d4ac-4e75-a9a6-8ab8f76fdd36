package io.terminus.trantor.org.spi.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * <AUTHOR>
 * @since 2023-07-17 20:42:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_biz_type_limit_link_cf")
public class OrgBizTypeLimitLinkPO extends BaseModel {
    private static final long serialVersionUID = -70486868528085223L;

    @ApiModelProperty("下级业务类型ID")
    @TableField("`child_biz_type_id`")
    private Long childBizTypeId;

    @ApiModelProperty("业务类型ID")
    @TableField("`biz_type_id`")
    private Long bizTypeId;
}

package io.terminus.trantor.org.spi.model.dto;

import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * @author: 张博
 * @date: 2023-12-04 14:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgImportData extends BaseModel {
    private static final long serialVersionUID = -1645171064966448384L;

    /**
     * 业务类型
     */
    private String orgType;
    /**
     * 业务类型对应的表头
     */
    private Map<String, String> header;
    /**
     * 业务类型数据
     */
    private List<Map<String, String>> data;
}

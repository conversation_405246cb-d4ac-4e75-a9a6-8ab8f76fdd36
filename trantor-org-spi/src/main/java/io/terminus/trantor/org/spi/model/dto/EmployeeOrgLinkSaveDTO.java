package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-11-24 10:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EmployeeOrgLinkSaveDTO extends AbstractRequest {
    private static final long serialVersionUID = -3736879094845392556L;

    @ApiModelProperty("组织ID")
    private Long orgStructId;

    @ApiModelProperty("成员信息")
    private EmployeeOrgLinkDTO employeeOrgLinkDTO;
}

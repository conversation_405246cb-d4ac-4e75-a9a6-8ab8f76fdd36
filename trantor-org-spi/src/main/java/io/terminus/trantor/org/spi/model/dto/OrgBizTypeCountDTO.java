package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-02-28 15:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgBizTypeCountDTO extends BaseModel {
    private static final long serialVersionUID = 1675815289269246264L;

    @ApiModelProperty("业务类型ID")
    private Long id;

    @ApiModelProperty("业务类型名称")
    private String name;

    @ApiModelProperty("组织单元数量")
    private Integer count;
}

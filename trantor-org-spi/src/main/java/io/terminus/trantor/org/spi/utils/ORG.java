package io.terminus.trantor.org.spi.utils;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.request.AbstractRequest;
import io.terminus.common.runtime.helper.SpringContextHelper;
import io.terminus.erp.runtime.rpc.TServiceTemplate;
import io.terminus.trantor.org.spi.dict.OrgActionKeyDict;
import io.terminus.trantor.org.spi.model.req.OrgQueryByIdReq;
import io.terminus.trantor.org.spi.model.req.OrgQueryByIdsReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @author: 张博
 * @date: 2023-07-11 13:39
 */
public class ORG {

    private static TServiceTemplate template;
    private static TypeFactory typeFactory;

    @ApiOperation("根据Id查询指定组织数据模型数据")
    public static <Model> Model queryById(Long id, Class<Model> modelClass) {
        OrgQueryByIdReq req = new OrgQueryByIdReq();
        req.setModelKey(getModelKey(modelClass));
        req.setId(id);
        return cli().execute(OrgActionKeyDict.QUERY_BY_ID, req, modelClass);
    }

    @ApiOperation("根据Id集合查询指定组织数据模型数据")
    public static <Model> List<Model> queryByIds(Collection<Long> ids, Class<Model> modelClass) {
        OrgQueryByIdsReq req = new OrgQueryByIdsReq();
        req.setModelKey(getModelKey(modelClass));
        req.setIds(ids);
        return cli().execute(OrgActionKeyDict.QUERY_BY_IDS, req, tf().constructCollectionType(ArrayList.class, modelClass));
    }

    @ApiOperation("根据条件查询指定组织数据模型单条数据")
    public static <Model> Model queryByUk(Model model, Class<Model> modelClass) {
        OrgQueryByUkReq req = new OrgQueryByUkReq();
        req.setModelKey(getModelKey(modelClass));
        req.setFields(model);
        return cli().execute(OrgActionKeyDict.QUERY_BY_UK, req, modelClass);
    }

    @ApiOperation("根据条件查询指定组织数据模型多条数据")
    public static <Model> List<Model> queryList(Model model, Class<Model> modelClass) {
        OrgQueryByUkReq req = new OrgQueryByUkReq();
        req.setModelKey(getModelKey(modelClass));
        req.setFields(model);
        return cli().execute(OrgActionKeyDict.QUERY_LIST, req, tf().constructCollectionType(ArrayList.class, modelClass));
    }

    private static <Model> String getModelKey(Class<Model> modelClass) {
        TableName tn = modelClass.getDeclaredAnnotation(TableName.class);
        if (null == tn) {
            throw new BusinessException("ORG::model.table.name.required");
        }
        return tn.value();
    }

    private static TServiceTemplate cli() {
        if (null == template) {
            template = SpringContextHelper.getBean(TServiceTemplate.class);
        }
        return template;
    }

    private static TypeFactory tf() {
        if (null == typeFactory) {
            typeFactory = SpringContextHelper.getBean(ObjectMapper.class).getTypeFactory();
        }
        return typeFactory;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    private static class OrgQueryByUkReq extends AbstractRequest {
        private static final long serialVersionUID = -173571853310638437L;
        private String modelKey;
        private Object fields;
    }

    private ORG() {
    }

}

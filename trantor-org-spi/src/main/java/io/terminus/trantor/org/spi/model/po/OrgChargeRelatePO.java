package io.terminus.trantor.org.spi.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @time 2025/8/26 16:52
 * 组织关联主管临时表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_charge_relate_cf")
public class OrgChargeRelatePO extends BaseModel {

    @ApiModelProperty("组织Code")
    @TableField("`org_code`")
    private String orgCode;

    @ApiModelProperty("员工Code")
    @TableField("`emp_code`")
    private String empCode;
}

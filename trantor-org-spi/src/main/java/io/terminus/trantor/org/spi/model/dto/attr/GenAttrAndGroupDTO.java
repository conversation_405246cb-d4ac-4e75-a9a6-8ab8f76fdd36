package io.terminus.trantor.org.spi.model.dto.attr;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-11-21 18:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GenAttrAndGroupDTO extends BaseModel {
    private static final long serialVersionUID = 7944963395116332564L;

    @ApiModelProperty("属性分组id")
    private Long attrGroupId;

    @ApiModelProperty("属性分组编码")
    private String code;

    @ApiModelProperty("属性分组名称")
    private String name;

    @ApiModelProperty("属性分组类类别")
    private String attrClass;

    @ApiModelProperty("属性分组状态")
    private String status;

    @ApiModelProperty("属性分组备注")
    private String remark;

    @ApiModelProperty("属性定义表")
    private List<GenAttrDTO> genAttrCfDTOS;
}

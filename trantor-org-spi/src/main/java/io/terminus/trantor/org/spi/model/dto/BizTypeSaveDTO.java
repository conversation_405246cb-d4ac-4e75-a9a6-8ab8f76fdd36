package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-02-27 19:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizTypeSaveDTO extends AbstractRequest {
    private static final long serialVersionUID = -2777906848245838748L;

    @ApiModelProperty("类型名称")
    private String name;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("元数据模型标识")
    private String modelKey;

    @ApiModelProperty("限制业务类型")
    private List<OrgBizTypeLimitLinkDTO> parentLimitBizTypeList;
}

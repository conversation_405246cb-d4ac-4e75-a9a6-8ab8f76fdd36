package io.terminus.trantor.org.spi.model.req;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import io.terminus.common.api.util.AssertUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrgQueryByUkReq extends AbstractRequest {
    private static final long serialVersionUID = 625298116934761748L;

    @ApiModelProperty("模型标识")
    private String modelKey;

    @ApiModelProperty("字段条件")
    private Map<String, Object> fields;

    @Override
    public void checkParam() {
        AssertUtils.notEmpty(modelKey, "ORG::model.key.required");
        AssertUtils.nonNull(fields, "ORG::model.fields.required");
    }
}

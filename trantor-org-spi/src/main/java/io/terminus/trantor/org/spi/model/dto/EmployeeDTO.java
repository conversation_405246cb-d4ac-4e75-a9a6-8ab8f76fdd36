package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: 张博
 * @date: 2023-06-14 10:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EmployeeDTO extends BaseModel {
    private static final long serialVersionUID = -8223888913364708160L;

    @MetaModelField
    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("组织")
    @MetaModelField
    private Long orgStructId;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("手机号码")
    private String mobile;

    @MetaModelField
    @ApiModelProperty("工作地点ID")
    private Long addressId;

    @ApiModelProperty("详细地址")
    private String addressDetail;

    @ApiModelProperty("入职日期")
    private LocalDateTime entryAt;

    @ApiModelProperty("离职日期")
    private LocalDateTime resignationAt;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("身份证号")
    private String idCard;

    @ApiModelProperty("钉钉通知")
    private String dingTalkCode;

    @ApiModelProperty("关联的组织集合")
    private List<EmployeeOrgLinkDTO> employeeOrgLinkList;

    @ApiModelProperty("员工角色关联信息")
    private List<EmployeeRoleLinkDTO> employeeRoleLinkList;

    @ApiModelProperty("员工消息通知关联信息")
    private List<EmployeeNoticeSceneLinkDTO> noticeSceneList;
}

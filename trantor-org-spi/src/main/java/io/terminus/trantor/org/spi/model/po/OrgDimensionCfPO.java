package io.terminus.trantor.org.spi.model.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织维度表(OrgDimensionCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-10 17:37:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_dimension_cf")
public class OrgDimensionCfPO extends BaseModel {
    private static final long serialVersionUID = 558200307573860843L;

    @ApiModelProperty("维度编码")
    @TableField("`org_dimension_code`")
    private String orgDimensionCode;

    @ApiModelProperty("维度名称")
    @TableField("`org_dimension_name`")
    private String orgDimensionName;

    @ApiModelProperty("维度说明")
    @TableField("`org_dimension_describe`")
    private String orgDimensionDescribe;

    @ApiModelProperty("维度状态")
    @TableField("`status`")
    private String orgDimensionStatus;

    @ApiModelProperty("是否支持多根节点")
    @TableField("`is_sup_multi_root`")
    private Boolean isSupMultiRoot;

}

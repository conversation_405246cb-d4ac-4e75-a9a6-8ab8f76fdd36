package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-07-19 15:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizTypeDTO extends BaseModel {

    private static final long serialVersionUID = 5088680558827491956L;

    @ApiModelProperty("类型名称")
    private String name;

    @ApiModelProperty("类型名称")
    private String code;

    @ApiModelProperty("元数据模型标识")
    private String modelKey;
}

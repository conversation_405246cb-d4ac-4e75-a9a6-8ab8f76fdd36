package io.terminus.trantor.org.spi.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_biz_type_cf", excludeProperty = "extra")
public class BizTypePO extends BaseModel {
    private static final long serialVersionUID = -2373203324231074661L;

    @ApiModelProperty("类型名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("元数据模型标识")
    @TableField("`model_key`")
    private String modelKey;
}

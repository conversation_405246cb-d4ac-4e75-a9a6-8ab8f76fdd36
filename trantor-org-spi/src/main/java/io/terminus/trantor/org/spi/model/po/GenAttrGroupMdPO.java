package io.terminus.trantor.org.spi.model.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 特征类定义表(GenCharaClassMd)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-09 19:15:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "gen_attr_group_md")
public class GenAttrGroupMdPO extends BaseModel {
    private static final long serialVersionUID = -76168798766877861L;

    @ApiModelProperty("编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("类类别")
    @TableField("`chara_class_type`")
    private String charaClassType;

    @ApiModelProperty("名称 ")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

    @ApiModelProperty("备注")
    @TableField("`remark`")
    private String remark;

}

package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-09-19 17:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RoleDTO extends BaseModel {
    private static final long serialVersionUID = -5871738484554025607L;

    @ApiModelProperty("标识")
    private String key;

    @ApiModelProperty("角色ID")
    private Long roleId;

    @ApiModelProperty("名称")
    private String roleName;

    @ApiModelProperty("描述")
    private String desc;

    @ApiModelProperty("是否启用")
    private Boolean enabled;
}

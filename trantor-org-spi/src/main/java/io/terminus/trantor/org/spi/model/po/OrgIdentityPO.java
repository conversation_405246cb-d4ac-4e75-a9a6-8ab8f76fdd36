package io.terminus.trantor.org.spi.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023-11-14 14:48:14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_identity_cf")
public class OrgIdentityPO extends BaseModel {

    private static final long serialVersionUID = 9063193225053450352L;

    @ApiModelProperty("编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

}

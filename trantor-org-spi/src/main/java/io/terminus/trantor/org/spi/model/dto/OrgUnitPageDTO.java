package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * @author: 张博
 * @date: 2023-03-01 09:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgUnitPageDTO extends AbstractPageRequest {
    private static final long serialVersionUID = -3117465289770532051L;

    @ApiModelProperty("组织编码")
    private String code;

    @ApiModelProperty("组织名称")
    private String name;

    @ApiModelProperty("父ID")
    private Long pid;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("业务类型ID")
    private Long bizTypeId;

    @ApiModelProperty("业务类型编码")
    private Set<String> bizTypeCodes;
}

package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * @author: 张博
 * @date: 2024-01-10 15:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructBuildTreeQueryDTO extends BaseModel {
    private static final long serialVersionUID = -180677944987097691L;

    @ApiModelProperty("维度编码")
    private String orgDimensionCode;

    @ApiModelProperty("维度ID")
    private Long orgDimensionId;

    @ApiModelProperty("状态")
    private Set<String> orgStatusSet;
}

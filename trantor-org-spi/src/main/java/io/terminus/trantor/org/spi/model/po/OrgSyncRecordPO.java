package io.terminus.trantor.org.spi.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织同步记录表
 *
 * <AUTHOR>
 * @since 2023-02-27 20:42:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_sync_record_md")
public class OrgSyncRecordPO extends BaseModel {
    private static final long serialVersionUID = -70486868528085223L;

    @ApiModelProperty("渠道编码")
    @TableField("`channel_code`")
    private String channelCode;

    @ApiModelProperty("描述")
    @TableField("`desc`")
    private String desc;

    @ApiModelProperty("请求")
    @TableField("`request`")
    private String request;

    @ApiModelProperty("返回")
    @TableField("`response`")
    private String response;

    @ApiModelProperty("错误信息")
    @TableField("`error_msg`")
    private String errorMsg;
}

package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructMdTreeDTO  extends BaseModel {
    private static final long serialVersionUID = 5038380558809091886L;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("父组织ID")
    private Long orgParentId;

    @ApiModelProperty("是否叶子节点")
    private Boolean leaf;

    @ApiModelProperty("状态")
    private String orgStatus;

}

package io.terminus.trantor.org.spi.model.dto.dict;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据字典项(GenDictItemCf)传输模型
 *
 * <AUTHOR>
 * @since 2023-11-22 15:38:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GenDictItemDTO extends BaseModel {
    private static final long serialVersionUID = 521080493328051226L;

    @ApiModelProperty("项目编码")
    private String code;

    @ApiModelProperty("项目值")
    private String name;

    @ApiModelProperty("序号")
    private Integer sort;

    @ApiModelProperty("是否系统项目")
    private Boolean system;

    @ApiModelProperty("状态")
    private String status;

    @MetaModelField
    @ApiModelProperty("数据字典类别id")
    private Long dictHeadId;

    @ApiModelProperty("字典类别编码")
    private String dictHeadCode;

}

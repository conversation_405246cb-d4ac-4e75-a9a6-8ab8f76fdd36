package io.terminus.trantor.org.spi.model.req;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.common.api.util.AssertUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrgQueryByIdsReq extends IdsRequest {
    private static final long serialVersionUID = 4800289079451920618L;

    @ApiModelProperty("模型标识")
    private String modelKey;

    @Override
    public void checkParam() {
        AssertUtils.notEmpty(modelKey, "ORG::model.key.required");
        super.checkParam();
    }
}

package io.terminus.trantor.org.spi.utils;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: 张博
 * @date: 2023-11-17 18:49
 */
public class OrgFiledUtils {

    public static Object getFieldValue(Object obj, String fieldName) throws Exception {
        Class<?> clazz = obj.getClass();
        BeanInfo beanInfo = Introspector.getBeanInfo(clazz);
        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();

        PropertyDescriptor targetPropertyDescriptor = null;
        for (PropertyDescriptor pd : propertyDescriptors) {
            if (pd.getName().equals(fieldName)) {
                targetPropertyDescriptor = pd;
                break;
            }
        }

        if (targetPropertyDescriptor != null) {
            Method getter = targetPropertyDescriptor.getReadMethod();
            return getter.invoke(obj);
        }

        return null;
    }

    /**
     * 获取本类及其父类的字段属性
     *
     * @param clazz 当前类对象
     * @return 字段数组
     */
    public static Field[] getAllFields(Class<?> clazz) {
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null) {
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        Field[] fields = new Field[fieldList.size()];
        return fieldList.toArray(fields);
    }
}

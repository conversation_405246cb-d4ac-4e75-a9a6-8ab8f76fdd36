package io.terminus.trantor.org.spi.model.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据字典类别(HrCodeTypeCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-02 14:30:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "hr_code_type_cf")
public class HrCodeTypeCfPO extends BaseModel {
    private static final long serialVersionUID = -12556414097891437L;

    @ApiModelProperty("类别编码")
    @TableField("`hr_type_code`")
    private String hrTypeCode;

    @ApiModelProperty("类别名称")
    @TableField("`hr_type_name`")
    private String hrTypeName;

    @ApiModelProperty("是否系统类别")
    @TableField("`hr_is_system`")
    private Boolean hrIsSystem;

    @ApiModelProperty("状态")
    @TableField("`hr_code_type_status`")
    private String hrCodeTypeStatus;

}

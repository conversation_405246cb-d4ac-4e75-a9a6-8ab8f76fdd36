package io.terminus.trantor.org.spi.model.dto;

import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-05-12 11:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UserOrgQueryDTO extends AbstractRequest {
    private static final long serialVersionUID = -7330102933834571828L;

    private Long userId;
}

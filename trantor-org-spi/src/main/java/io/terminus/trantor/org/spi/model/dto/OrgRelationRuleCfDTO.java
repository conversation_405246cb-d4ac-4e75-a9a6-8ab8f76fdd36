package io.terminus.trantor.org.spi.model.dto;




import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class OrgRelationRuleCfDTO extends BaseModel {
    private static final long serialVersionUID = -82985958418566841L;

    @ApiModelProperty("源组织维度")
    private Long orgHeadDimensionId;

    @ApiModelProperty("源组织类型")
    private Long orgHeadUnitTypeId;

    @ApiModelProperty("关联组织维度")
    private Long orgRelationDimensionId;

    @ApiModelProperty("关联组织类型")
    private Long orgRelationUnitTypeId;

    @ApiModelProperty("关联类型")
    private String orgRelationRuleType;

    @ApiModelProperty("所属组织")
    private Long originOrgId;

}

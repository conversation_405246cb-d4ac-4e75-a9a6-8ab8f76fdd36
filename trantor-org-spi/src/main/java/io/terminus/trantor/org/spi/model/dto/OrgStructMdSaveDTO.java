package io.terminus.trantor.org.spi.model.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructMdSaveDTO  extends OrgDimensionDTO {
    private static final long serialVersionUID = 3899680558809091886L;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("父组织ID")
    private Long orgParentId;

    @ApiModelProperty("状态")
    private String orgStatus;

    @ApiModelProperty("启用日期")
    private LocalDateTime orgEnableDate;

    @ApiModelProperty("是否叶子节点")
    private Boolean leaf;

    @ApiModelProperty("组织维度id")
    private Long orgDimensionId;

    @ApiModelProperty("公司组织")
    private Long comOrgId;

    @ApiModelProperty("合作伙伴ID")
    private Long partnerId;

//    @ApiModelProperty("组织业务类型id")
//    private Long orgBusinessTypeId;

    @ApiModelProperty("业务类型ID")
    private List<Long> orgBusinessTypeIds;

    @ApiModelProperty("业务类型编码")
    private List<String> orgBusinessTypeCodes;

    @ApiModelProperty("排序")
    private Integer orgSort;

    @ApiModelProperty("成员信息")
    private List<EmployeeOrgLinkDTO> employeeOrgLinkList;

    @ApiModelProperty("预留字段1")
    private String def1;

    @ApiModelProperty("预留字段2")
    private String def2;

    @ApiModelProperty("预留字段3")
    private String def3;

    @ApiModelProperty("预留字段4")
    private String def4;

    @ApiModelProperty("预留字段5")
    private String def5;

    @ApiModelProperty("预留字段6")
    private String def6;

    @ApiModelProperty("预留字段7")
    private String def7;

    @ApiModelProperty("预留字段8")
    private String def8;

    @ApiModelProperty("预留字段9")
    private String def9;

    @ApiModelProperty("预留字段10")
    private String def10;

    @ApiModelProperty("预留字段11")
    private String def11;

    @ApiModelProperty("预留字段12")
    private String def12;

    @ApiModelProperty("预留字段13")
    private String def13;

    @ApiModelProperty("预留字段14")
    private String def14;

    @ApiModelProperty("预留字段15")
    private String def15;

    @ApiModelProperty("预留字段16")
    private String def16;

    @ApiModelProperty("预留字段17")
    private String def17;

    @ApiModelProperty("预留字段18")
    private String def18;

    @ApiModelProperty("预留字段19")
    private String def19;

    @ApiModelProperty("预留字段20")
    private String def20;

    @ApiModelProperty("预留字段21")
    private String def21;

    @ApiModelProperty("预留字段22")
    private String def22;

    @ApiModelProperty("预留字段23")
    private String def23;

    @ApiModelProperty("预留字段24")
    private String def24;

    @ApiModelProperty("预留字段25")
    private String def25;

    @ApiModelProperty("预留字段26")
    private String def26;

    @ApiModelProperty("预留字段27")
    private String def27;

    @ApiModelProperty("预留字段28")
    private String def28;

    @ApiModelProperty("预留字段29")
    private String def29;

    @ApiModelProperty("预留字段30")
    private String def30;

    @ApiModelProperty("预留字段31")
    private String def31;

    @ApiModelProperty("预留字段32")
    private String def32;

    @ApiModelProperty("预留字段33")
    private String def33;

    @ApiModelProperty("预留字段34")
    private String def34;

    @ApiModelProperty("预留字段35")
    private String def35;

    @ApiModelProperty("预留字段36")
    private String def36;

    @ApiModelProperty("预留字段37")
    private String def37;

    @ApiModelProperty("预留字段38")
    private String def38;

    @ApiModelProperty("预留字段39")
    private String def39;

    @ApiModelProperty("预留字段40")
    private String def40;
}

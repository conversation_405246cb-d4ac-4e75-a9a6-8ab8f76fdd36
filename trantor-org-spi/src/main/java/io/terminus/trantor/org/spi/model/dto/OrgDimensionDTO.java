package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgDimensionDTO  extends BaseModel {

    private static final long serialVersionUID = 7878680558809091886L;

    @ApiModelProperty("维度编码")
    private String orgDimensionCode;

    @ApiModelProperty("维度名称")
    private String orgDimensionName;

    @ApiModelProperty("维度ID")
    private Long orgDimensionId;
}

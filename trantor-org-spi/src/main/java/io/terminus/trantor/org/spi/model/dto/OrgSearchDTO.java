package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgSearchDTO extends AbstractPageRequest {
    private static final long serialVersionUID = -3217465289770532091L;
    @ApiModelProperty("组织编码")
    private String code;
    @ApiModelProperty("组织名称")
    private String name;
    @ApiModelProperty("业务类型编码")
    private Set<String> bizTypeCodes;
}

package io.terminus.trantor.org.spi.model.po;


import java.time.LocalDateTime;
import java.time.LocalTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织架构变更记录表(OrgStructAdjustHisMd)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-02 14:31:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_struct_adjust_his_md")
public class OrgStructAdjustHisMdPO extends BaseModel {
    private static final long serialVersionUID = 673195992612853636L;

    @ApiModelProperty("行政组织ID")
    @TableField("`org_struct_id`")
    private Long orgStructId;

    @ApiModelProperty("调整前父组织ID")
    @TableField("`org_former_adjust_parent_id`")
    private Long orgFormerAdjustParentId;

    @ApiModelProperty("调整后父组织ID")
    @TableField("`org_adjust_parent_id`")
    private Long orgAdjustParentId;

    @ApiModelProperty("调整前状态")
    @TableField("`org_former_adjust_status`")
    private String orgFormerAdjustStatus;

    @ApiModelProperty("调整后状态")
    @TableField("`org_adjust_status`")
    private String orgAdjustStatus;

    @ApiModelProperty("生效日期")
    @TableField("`org_his_adjust_enable_date`")
    private LocalDateTime orgHisAdjustEnableDate;

}

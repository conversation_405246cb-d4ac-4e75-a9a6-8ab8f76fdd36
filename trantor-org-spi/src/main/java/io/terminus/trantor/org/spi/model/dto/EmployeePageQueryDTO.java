package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;
import java.util.Set;

/**
 * @author: 张博
 * @date: 2023-02-28 11:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EmployeePageQueryDTO extends AbstractPageRequest {
    private static final long serialVersionUID = 4363404158671744950L;

    @ApiModelProperty("需要排除的id集合")
    private Set<Long> excludeIds;

    private Map<String, Object> pageable;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("手机号码")
    private String mobile;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("组织单元ID")
    private Long orgUnitId;
}

package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
public class OrgUnitCodeQueryDto extends BaseModel {
    private static final long serialVersionUID = -591288018205012072L;

    @ApiModelProperty("组织编码")
    private String code;
}

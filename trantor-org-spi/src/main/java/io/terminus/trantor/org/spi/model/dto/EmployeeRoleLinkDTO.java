package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-09-19 13:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EmployeeRoleLinkDTO extends BaseModel {
    private static final long serialVersionUID = 2337560290949818237L;

    @ApiModelProperty("角色ID")
    private Long roleId;

    @ApiModelProperty("角色ID")
    private String roleName;
}

package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
public class OrgParentQueryDTO extends BaseModel {

    private static final long serialVersionUID = 5088980558827491956L;

    @ApiModelProperty("业务类型编码")
    private String orgBusinessTypeCode;

}

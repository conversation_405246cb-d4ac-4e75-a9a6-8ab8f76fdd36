package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@Data
@EqualsAndHashCode(callSuper = false)
public class OrgQueryNamesDTO extends BaseModel {

    private static final long serialVersionUID = 5088980558827491956L;

    @ApiModelProperty("维度编码")
    private String orgDimensionCode;

    @ApiModelProperty("组织编码")
    private List<String> orgNames;

}

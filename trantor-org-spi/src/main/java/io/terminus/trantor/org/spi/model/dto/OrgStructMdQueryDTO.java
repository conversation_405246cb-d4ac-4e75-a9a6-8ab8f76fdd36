package io.terminus.trantor.org.spi.model.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructMdQueryDTO  extends OrgDimensionDTO {
    private static final long serialVersionUID = 5088680558809091886L;

    @ApiModelProperty("父组织ID")
    private Long orgParentId;

    @ApiModelProperty("状态")
    private List<String> orgStatus;
}

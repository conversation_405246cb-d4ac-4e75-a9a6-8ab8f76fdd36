package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-09-19 17:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RolePageDTO extends AbstractPageRequest {
    private static final long serialVersionUID = 1922336471169851412L;

    @ApiModelProperty("标识")
    private String key;

    @ApiModelProperty("名称")
    private String roleName;

    @ApiModelProperty("角色ID")
    private String roleId;

    @ApiModelProperty("是否启用")
    private Boolean enabled;
}

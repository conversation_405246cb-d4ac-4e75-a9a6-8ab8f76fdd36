package io.terminus.trantor.org.spi.model;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
public class GenAddrTypeCfDTO extends BaseModel {

    private static final long serialVersionUID = -9118104985684499855L;

    @ApiModelProperty("地址名称")
    private String addrName;

    @ApiModelProperty("地址库编码")
    private String addrCode;

    @ApiModelProperty("邮政编码")
    @TableField("`post_code`")
    private String postCode;

    @ApiModelProperty("国家")
    private Long counId;

    @ApiModelProperty("是否叶子节点")
    private Boolean leaf;

    @ApiModelProperty("父节点")
    private Long addrParentId;


    @ApiModelProperty("地址类型")
    private String addrType;
}

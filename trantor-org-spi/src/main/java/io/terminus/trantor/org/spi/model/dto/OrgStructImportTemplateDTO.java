package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-11-30 19:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructImportTemplateDTO extends BaseModel {
    private static final long serialVersionUID = -8600624779707623214L;

    @ApiModelProperty("导入模版地址")
    private String templateUrl;

    @ApiModelProperty("导入模版头")
    private List<OrgImportHeaderContextDTO> importHeaderContextList;
}

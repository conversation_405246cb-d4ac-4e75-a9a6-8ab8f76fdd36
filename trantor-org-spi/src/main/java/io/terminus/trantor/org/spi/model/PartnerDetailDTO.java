package io.terminus.trantor.org.spi.model;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractPageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
public class PartnerDetailDTO extends AbstractPageRequest {
    private static final long serialVersionUID = -1434406494997739662L;

    @ApiModelProperty("公司编码")
    private String code;

    @ApiModelProperty("公司名称")
    private String name;

    @ApiModelProperty("伙伴身份")
    private List<String> partnerIdentity;

    @ApiModelProperty("公司组织名称")
    private Long comOrgName;

    @ApiModelProperty("伙伴类型")
    private Long partnerTypeId;

    @ApiModelProperty("伙伴类型名称")
    private String partnerTypeName;

    @ApiModelProperty("伙伴类别")
    private String partnerTypeClass;

    @ApiModelProperty("身份证号")
    private String idCard;

    @ApiModelProperty("手机号")
    private String personPhone;

    @ApiModelProperty("法定代表人")
    private String comCorporation;

    @ApiModelProperty("营业执照号")
    private String bizLicenseNo;

    @ApiModelProperty("企业类型")
    private String enterpriseType;

    @ApiModelProperty("注册资本")
    private String registeredCapital;

    @ApiModelProperty("统一社会信用代码")
    private String socialcreditCode;

    @ApiModelProperty("纳税人识别号")
    private String taxpayersNum;

    @ApiModelProperty("经营范围")
    private String bizScope;

    @ApiModelProperty("简介")
    private String intro;
}

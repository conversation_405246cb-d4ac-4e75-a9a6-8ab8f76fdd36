package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructMdHistoryTreeByNameDTO extends OrgDimensionDTO {
    private static final long serialVersionUID = 5038380098809091886L;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("查询时间")
    private LocalDateTime queryDate;

}

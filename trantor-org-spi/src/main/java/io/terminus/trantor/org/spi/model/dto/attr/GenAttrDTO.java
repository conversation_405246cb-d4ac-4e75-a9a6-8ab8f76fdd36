package io.terminus.trantor.org.spi.model.dto.attr;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-11-21 18:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GenAttrDTO extends BaseModel {
    private static final long serialVersionUID = 7603427027277819847L;

    @ApiModelProperty("组织属性编码")
    private String attrCode;

    @ApiModelProperty("组织属性名称")
    private String attrName;

    @ApiModelProperty("组织属性数据类型")
    private String attrDataType;

    @ApiModelProperty("是否必填")
    private Boolean attrIsRequire;

    @ApiModelProperty("长度")
    private Long attrLength;

    @ApiModelProperty("对象基本信息")
    private String objectKey;

    @ApiModelProperty("对象查询条件")
    private String objectMeta;

    @ApiModelProperty("对应组织表的预留列")
    private String attrField;

    @ApiModelProperty("是否多选")
    private Boolean attrIsMulti;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("类别")
    private String attrClass;

    @ApiModelProperty("字典类别")
    private Long dictHeadId;
}

package io.terminus.trantor.org.spi.model.dto;


import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 法人公司关联关系表(OrgLegalPersonRelationCf)传输模型
 *
 * <AUTHOR>
 * @since 2023-11-28 19:43:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgLegalPersonRelationCfDTO extends BaseModel {
    private static final long serialVersionUID = -79626223385338726L;

    @MetaModelField
    @ApiModelProperty("法人公司id")
    private Long genComTypeId;

    @MetaModelField
    @ApiModelProperty("关联组织单元id")
    private Long orgRelationUnitId;

    @MetaModelField
    @ApiModelProperty("关联组织维度id")
    private Long orgLegalDimensionId;

    @MetaModelField
    @ApiModelProperty("关系类型")
    private Long orgLegalType;

    @ApiModelProperty("生效时间")
    private LocalDateTime orgLegalEnabledTime;

    @ApiModelProperty("失效时间")
    private LocalDateTime orgLegalDisenabledTime;

    @ApiModelProperty("状态")
    private String orgLegalStatus;

    @MetaModelField
    @ApiModelProperty("关联组织单元类型")
    private Long orgLegalUnitTypeId;

    @ApiModelProperty("路径展示作用")
    private String remark;

}

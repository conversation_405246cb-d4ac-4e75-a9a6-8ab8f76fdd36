package io.terminus.trantor.org.spi.model.po;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.common.mybatis.typehandler.ListLongTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 组织架构表(OrgStructMd)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-02 14:29:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_struct_md", autoResultMap = true)
public class OrgStructMdPO extends BaseModel {
    private static final long serialVersionUID = -28160749770374100L;

    @ApiModelProperty("组织编码")
    @TableField("`org_code`")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @TableField("`org_name`")
    private String orgName;

    @ApiModelProperty("父组织ID")
    @TableField("`org_parent_id`")
    private Long orgParentId;

    @ApiModelProperty("父组织Code")
    @TableField("`org_parent_code`")
    private String orgParentCode;

    @ApiModelProperty("状态")
    @TableField("`org_status`")
    private String orgStatus;

    @ApiModelProperty("排序")
    @TableField("`org_sort`")
    private Integer orgSort;

    @ApiModelProperty("启用日期")
    @TableField("`org_enable_date`")
    private LocalDateTime orgEnableDate;

    @ApiModelProperty("是否叶子节点")
    @TableField("`is_leaf`")
    private Boolean leaf;

    @ApiModelProperty("组织维度id")
    @TableField("`org_dimension_id`")
    private Long orgDimensionId;

    @ApiModelProperty("组织维度编码")
    @TableField("`org_dimension_code`")
    private String orgDimensionCode;

//    @ApiModelProperty("业务类型ID")
//    @TableField("`org_business_type_id`")
//    private Long orgBusinessTypeId;

    @ApiModelProperty("业务类型ID")
    @TableField(value = "`org_business_type_ids`", typeHandler = ListLongTypeHandler.class)
    private List<Long> orgBusinessTypeIds;

//    @ApiModelProperty("业务类型编码")
//    @TableField("`org_business_type_code`")
//    private String orgBusinessTypeCode;

    @ApiModelProperty("业务类型编码")
    @TableField(value = "`org_business_type_codes`", typeHandler = JacksonTypeHandler.class)
    private List<String> orgBusinessTypeCodes;

    @ApiModelProperty("组织路径")
    @TableField(value = "`path`", typeHandler = ListLongTypeHandler.class)
    private List<Long> path;

    @ApiModelProperty("公司组织")
    @TableField("`com_org_id`")
    private Long comOrgId;

    @ApiModelProperty("合作伙伴ID")
    @TableField(value = "`partner_id`", updateStrategy = FieldStrategy.IGNORED)
    private Long partnerId;

    @ApiModelProperty("预留字段1")
    @TableField("`def1`")
    private String def1;

    @ApiModelProperty("预留字段2")
    @TableField("`def2`")
    private String def2;

    @ApiModelProperty("预留字段3")
    @TableField("`def3`")
    private String def3;

    @ApiModelProperty("预留字段4")
    @TableField("`def4`")
    private String def4;

    @ApiModelProperty("预留字段5")
    @TableField("`def5`")
    private String def5;

    @ApiModelProperty("预留字段6")
    @TableField("`def6`")
    private String def6;

    @ApiModelProperty("预留字段7")
    @TableField("`def7`")
    private String def7;

    @ApiModelProperty("预留字段8")
    @TableField("`def8`")
    private String def8;

    @ApiModelProperty("预留字段9")
    @TableField("`def9`")
    private String def9;

    @ApiModelProperty("预留字段10")
    @TableField("`def10`")
    private String def10;

    @ApiModelProperty("预留字段11")
    @TableField("`def11`")
    private String def11;

    @ApiModelProperty("预留字段12")
    @TableField("`def12`")
    private String def12;

    @ApiModelProperty("预留字段13")
    @TableField("`def13`")
    private String def13;

    @ApiModelProperty("预留字段14")
    @TableField("`def14`")
    private String def14;

    @ApiModelProperty("预留字段15")
    @TableField("`def15`")
    private String def15;

    @ApiModelProperty("预留字段16")
    @TableField("`def16`")
    private String def16;

    @ApiModelProperty("预留字段17")
    @TableField("`def17`")
    private String def17;

    @ApiModelProperty("预留字段18")
    @TableField("`def18`")
    private String def18;

    @ApiModelProperty("预留字段19")
    @TableField("`def19`")
    private String def19;

    @ApiModelProperty("预留字段20")
    @TableField("`def20`")
    private String def20;

    @ApiModelProperty("附件预留字段1")
    @TableField("`attachment1`")
    private String attachment1;

    @ApiModelProperty("附件预留字段2")
    @TableField("`attachment2`")
    private String attachment2;

    @ApiModelProperty("预留字段21")
    @TableField("`def21`")
    private String def21;

    @ApiModelProperty("预留字段22")
    @TableField("`def22`")
    private String def22;

    @ApiModelProperty("预留字段23")
    @TableField("`def23`")
    private String def23;

    @ApiModelProperty("预留字段24")
    @TableField("`def24`")
    private String def24;

    @ApiModelProperty("预留字段25")
    @TableField("`def25`")
    private String def25;

    @ApiModelProperty("预留字段26")
    @TableField("`def26`")
    private String def26;

    @ApiModelProperty("预留字段27")
    @TableField("`def27`")
    private String def27;

    @ApiModelProperty("预留字段28")
    @TableField("`def28`")
    private String def28;

    @ApiModelProperty("预留字段29")
    @TableField("`def29`")
    private String def29;

    @ApiModelProperty("预留字段30")
    @TableField("`def30`")
    private String def30;

    @ApiModelProperty("预留字段31")
    @TableField("`def31`")
    private String def31;

    @ApiModelProperty("预留字段32")
    @TableField("`def32`")
    private String def32;

    @ApiModelProperty("预留字段33")
    @TableField("`def33`")
    private String def33;

    @ApiModelProperty("预留字段34")
    @TableField("`def34`")
    private String def34;

    @ApiModelProperty("预留字段35")
    @TableField("`def35`")
    private String def35;

    @ApiModelProperty("预留字段36")
    @TableField("`def36`")
    private String def36;

    @ApiModelProperty("预留字段37")
    @TableField("`def37`")
    private String def37;

    @ApiModelProperty("预留字段38")
    @TableField("`def38`")
    private String def38;

    @ApiModelProperty("预留字段39")
    @TableField("`def39`")
    private String def39;

    @ApiModelProperty("预留字段40")
    @TableField("`def40`")
    private String def40;
}

package io.terminus.trantor.org.spi.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.util.List;

/**
 * 组织机构同步DTO
 *
 * <AUTHOR> Generated
 */
@Data
@FieldNameConstants
public class OrgSyncDTO {

    @ApiModelProperty("组织机构的名称")
    private String organization;

    @ApiModelProperty("所属父级组织机构的uuid或外部ID")
    private String parentUuid;

    @ApiModelProperty("是否是根节点")
    private Boolean rootNode;

    @ApiModelProperty("本组织机构的uuid或外部ID")
    private String organizationUuid;

    @ApiModelProperty("管理者可为空")
    private List<ManagerDTO> manager;

    @ApiModelProperty("扩展字段")
    private List<ExtendFieldsDTO> extendFields;

    /**
     * 管理者DTO
     */
    @Data
    @FieldNameConstants
    public static class ManagerDTO {

        @ApiModelProperty("管理者用户名")
        private String displayName;

        @ApiModelProperty("管理者账户的外部ID")
        private String value;
    }

    /**
     * 扩展字段DTO
     */
    @Data
    @FieldNameConstants
    public static class ExtendFieldsDTO {

        @ApiModelProperty("公司编码")
        private String companycode;

        @ApiModelProperty("公司名称")
        private String companyname;

        @ApiModelProperty("成本组织名称")
        private String costname;

        @ApiModelProperty("传【组织负责人】对应员工编码")
        private String branchheadid;

        @ApiModelProperty("传【部门负责人】对应员工编码（当部门负责人不存在时，传【组织负责人】）")
        private String orgheadid;

        @ApiModelProperty("部门分管领导员工编号")
        private String orgfgid;

        @ApiModelProperty("分管领导上级的员工编号")
        private String fgsjid;

        @ApiModelProperty("部门属性")
        private String bmsx;

        @ApiModelProperty("组织全路径")
        private String orgfullpath;
    }
}

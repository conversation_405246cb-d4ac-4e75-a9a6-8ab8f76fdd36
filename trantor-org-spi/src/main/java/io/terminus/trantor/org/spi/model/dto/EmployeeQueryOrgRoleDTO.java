package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import io.terminus.common.api.util.AssertUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-06-14 10:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EmployeeQueryOrgRoleDTO extends AbstractRequest {
    private static final long serialVersionUID = -591253018205012072L;

    @ApiModelProperty("组织编码")
    private String unitCode;

    @ApiModelProperty("角色编码")
    private String rankCode;

    @ApiModelProperty("是否向上查找")
    private Boolean flag;

    @ApiModelProperty("是否向上查找是否包含当前组织")
    private Boolean  include;

    @ApiModelProperty("维度编码")
    private String orgDimensionCode;

    @Override
    public void checkParam() {
        super.checkParam();
        AssertUtils.notEmpty(unitCode, "Org.unit.code.is.not.empty");
        AssertUtils.notEmpty(rankCode, "Org.rank.code.is.not.empty");
    }
}

package io.terminus.trantor.org.spi.convert.ff;

import io.terminus.trantor.org.spi.model.dto.OrgLegalPersonRelationCfDTO;
import io.terminus.trantor.org.spi.model.po.OrgLegalPersonRelationCfPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 法人公司关联关系表(OrgLegalPersonRelationCf)结构映射器
 *
 * <AUTHOR>
 * @since 2023-11-28 19:43:09
 */
@Mapper(componentModel = "spring")
public interface OrgLegalPersonRelationCfConverter {

    OrgLegalPersonRelationCfDTO po2Dto(OrgLegalPersonRelationCfPO req);

    List<OrgLegalPersonRelationCfDTO> po2DtoList(List<OrgLegalPersonRelationCfPO> poList);

    OrgLegalPersonRelationCfPO dto2Po(OrgLegalPersonRelationCfDTO req);

    List<OrgLegalPersonRelationCfPO> dto2PoList(List<OrgLegalPersonRelationCfDTO> dtoList);
}

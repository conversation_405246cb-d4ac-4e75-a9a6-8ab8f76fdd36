package io.terminus.trantor.org.spi.model.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 切换公司列表(OrgSwitchListCf)存储模型
 *
 * <AUTHOR>
 * @since 2023-12-11 11:30:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_switch_list_cf")
public class OrgSwitchListCfPO extends BaseModel {
    private static final long serialVersionUID = -54162775350044240L;

    @ApiModelProperty("组织id")
    @TableField("`switch_org_id`")
    private Long switchOrgId;

    @ApiModelProperty("是否总部")
    @TableField("`switch_headquarters`")
    private Boolean switchHeadquarters;

    @ApiModelProperty("状态")
    @TableField("`switch_status`")
    private String switchStatus;

    @ApiModelProperty("说明")
    @TableField("`switch_describe`")
    private String switchDescribe;

    @ApiModelProperty("显示名称")
    @TableField("`switch_name`")
    private String switchName;

}

package io.terminus.trantor.org.spi.msg;

/**
 * @author: 张博
 * @date: 2023-10-09 10:33
 */
public interface OrgMsg {
    // 业务类型名称重复
    String ORG_BIZ_NAME_IS_EXIST = "Org.biz.name.is.exist";
    // 业务类型编码为空
    String ORG_BIZ_CODE_IS_EXIST = "Org.biz.code.is.exist";
    // 业务类型不存在
    String ORG_BIZ_TYPE_IS_NOT_EXIST = "Org.biz.type.is.not.exist";
    // 该业务类型绑定了组织单元不能删除
    String ORG_BIZ_TYPE_HAS_BIND_ORG_CAN_NOT_DELETE = "Org.biz.type.has.bind.org.can.not.delete";
    // 员工编码已经存在
    String ORG_EMPLOYEE_CODE_IS_REPEAT = "Org.employee.code.is.repeat";
    // 员工手机号码重复
    String ORG_EMPLOYEE_MOBILE_IS_REPEAT = "Org.employee.mobile.is.repeat";
    // 员工邮箱已经存在
    String ORG_EMPLOYEE_EMAIL_IS_REPEAT = "Org.employee.email.is.repeat";
    // 创建用户失败
    String ORG_CREATE_USER_FAIL = "Org.create.user.fail";
    // 改用绑定了其他的员工账号
    String ORG_THIS_USER_HAS_BIND_OTHER_EMPLOYEE = "Org.this.user.has.bind.other.employee";
    // 员工ID不能为空
    String ORG_EMPLOYEE_ID_IS_NOT_NULL = "Org.employee.id.is.not.null";
    // 员工不存在
    String ORG_EMPLOYEE_IS_NOT_EXIST = "Org.employee.is.not.exist";
    // 删除用户失败
    String ORG_USER_DELETE_FAIL = "Org.user.delete.fail";
    // 组织单元名称重复
    String ORG_ORG_UNIT_NAME_IS_REPEAT = "Org.org.unit.name.is.repeat";
    // 组织单元编码重复
    String ORG_ORG_UNIT_CODE_IS_REPEAT = "Org.org.unit.code.is.repeat";
    // 组织单元上级单元不存在
    String ORG_ORG_UNIT_PARENT_IS_NOT_EXIST = "Org.org.unit.parent.is.not.exist";
    // 组织单元上级单元未启用
    String ORG_ORG_UNIT_PARENT_STATUS_IS_NOT_ENABLE = "Org.org.unit.parent.status.is.not.enable";
    // 组织单元不存在
    String ORG_ORG_UNIT_IS_NOT_EXIST = "Org.org.unit.is.not.exist";
    // 上级单元不能是自己本身
    String ORG_ORG_PARENT_ID_IS_ILLEGAL = "Org.org.parent.id.is.illegal";
    // 组织单元非草稿态，不能删除
    String ORG_ORG_UNIT_STATUS_IS_NOT_DRAFT_CAN_NOT_DELETE = "Org.org.unit.status.is.not.draft.can.not.delete";
    // 组织单元状态不正确，不能启用
    String ORG_ORG_UNIT_STATUS_IS_NOT_DRAFT_OR_DISABLE = "Org.org.unit.status.is.not.draft.or.disable";
    // 组织单元状态不正确，不能禁用
    String ORG_ORG_UNIT_STATUS_IS_NOT_ENABLE = "Org.org.unit.status.is.not.enable";
    // 改组织单元存在启用的下级单元，不能禁用
    String ORG_ORG_UNIT_HAS_ENABLE_CHILDREN_CAN_NOT_DISABLE = "Org.org.unit.has.enable.children.can.not.disable";
    // 创建组织单元扩展数据失败
    String ORG_CREATE_BIZ_TYPE_EXTRA_FAIL = "Org.create.biz.type.extra.fail";
    // 删除组织单元扩展数据失败
    String ORG_DELETE_BIZ_TYPE_EXTRA_FAIL = "Org.delete.biz.type.extra.fail";
    // 跟着组织单元扩展数据失败
    String ORG_UPDATE_BIZ_TYPE_EXTRA_FAIL = "Org.update.biz.type.extra.fail";
    //组织维度代码为空
    String ORG_DIMENSION_CF_CODE_IS_NULL = "Org.dimension.cf.code.is.null";
    //请先选择一个组织维度
    String ORG_PLEASE_CHOOSE_DIMENSION_FIRST = "Org.please.choose.dimension.first";
    //组织维度为空
    String ORG_DIMENSION_CF_IS_NULL = "Org.dimension.cf.is.null";
    //组织code已存在，请修改后再保存
    String ORG_CODE_IS_EXIST = "Org.code.is.exist";
    //您不能在停用组织下新增组织
    String ORG_STRUCT_IS_DISABLED = "Org.struct.is.disable";
    //启动时间不能早于父组织启动时间
    String ORG_STRUCT_ENABLE_DATE_IS_BEFORE_PARENT = "Org.struct.is.enable.date.is.before.parent";
    //组织维度代码为空
    String ORG_PARENT_ID_IS_NULL = "Org.parent.id.is.null";
    //请先将该组织的上级组织启用，再启用该组织
    String ORG_PARENT_IS_NOT_ENABLE = "Org.parent.is.not.enable";
    // 存在重复的成员信息
    String ORG_STRUCT_MEMBER_REPEAT_RECORD = "Org.struct.member.is.repeat";
    // 成员信息已经存在
    String ORG_STRUCT_MEMBER_IS_EXIST = "Org.struct.member.is.exist";
    //不能选择当前节点以及当前节点下级节点作为父组织
    String ORG_STRUCT_PARENT_ERROR = "Org.struct.parent.error";
    // 组织切换模型已经存在
    String ORG_SWiTCH_MODEL_IS_EXIST = "Org.switch.model.is.exist";
    // 当前维度不支持多个根节点
    String ORG_STRUCT_CURRENT_NOT_SUP_MULTI_ROOT = "Org.current.org.dimension.not.support.multi.root";
    //组织类型不匹配
    String ORG_STRUCT_TYPE_RULE_FAIL = "Org.struct.type.rule.fail";
    //关联规则未设置，无法确认
    String ORG_RELATION_RULE_CF_IS_NULL = "Org.relation.rule.cf.is.null";
    // 关联关系已经存在
    String ORG_RELATION_IS_EXIST = "Org.relation.is.exist";
    //该组织只能关联一个相同类型的组织
    String ORG_RELATION_RULE_CHECK = "Org.relation.rule.check";
    //关联规则已设置
    String ORG_RELATION_RULE_CF_IS_EXIST = "Org.relation.rule.cf.is.exist";
    //关联规则未设置，无法启用
    String ORG_RELATION_RULE_CF_CHECK_FAIL = "Org.relation.rule.cf.check.fail";

    String ORG_BUSINESS_TYPE_IS_NULL = "Org.business.type.is.not.null";
    // 当前的公司组织非当前节点上级对应的公司组织
    String ORG_CURRENT_COM_ORG_IS_NOT_CORRECT = "Org.current.com.org.is.not.correct";
    // 存在重复的通知场景
    String ORG_NOTICE_SCENE_IS_NOT_UNIQUE = "Org.notice.scene.is.not.unique";
    // 手机号码不合法
    String ORG_EMPLOYEE_PHONE_NUMBER_IS_NOT_VALID = "Org.employee.phone.number.is.not.valid";
}
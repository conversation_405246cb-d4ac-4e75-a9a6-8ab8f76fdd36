package io.terminus.trantor.org.spi.model.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructMdHistoryQueryDTO extends OrgDimensionDTO {
    private static final long serialVersionUID = 5088680558809091886L;

    @ApiModelProperty("父组织ID")
    private Long orgParentId;
    @ApiModelProperty("查询时间")
    private LocalDateTime orgEditionDate;
}

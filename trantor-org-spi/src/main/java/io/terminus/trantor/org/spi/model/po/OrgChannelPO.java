package io.terminus.trantor.org.spi.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织同步记录表
 *
 * <AUTHOR>
 * @since 2023-02-27 20:42:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_channel_cf")
public class OrgChannelPO extends BaseModel {
    private static final long serialVersionUID = -70486868528085223L;

    @ApiModelProperty("渠道编码")
    @TableField("`channel_code`")
    private String channelCode;

    @ApiModelProperty("渠道名称")
    @TableField("`channel_name`")
    private String channelName;

    @ApiModelProperty("渠道配置")
    @TableField("`channel_config`")
    private String channelConfig;

    @ApiModelProperty("组织业务类型")
    @TableField("`org_business_type_code`")
    private String orgBusinessTypeCode;

    @ApiModelProperty("组织维度编码")
    @TableField("`org_dimension_code`")
    private String orgDimensionCode;
}

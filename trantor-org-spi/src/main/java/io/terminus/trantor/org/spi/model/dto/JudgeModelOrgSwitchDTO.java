package io.terminus.trantor.org.spi.model.dto;

import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-12-11 16:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class JudgeModelOrgSwitchDTO extends BaseModel {
    private static final long serialVersionUID = -8140655533400142340L;

    private Long teamId;

    private String modelKey;
}

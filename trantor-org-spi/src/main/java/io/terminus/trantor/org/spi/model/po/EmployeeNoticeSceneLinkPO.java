package io.terminus.trantor.org.spi.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织员工关联表(EmployeeLink)实体类
 *
 * <AUTHOR>
 * @since 2023-02-27 20:42:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_employee_scene_link")
public class EmployeeNoticeSceneLinkPO extends BaseModel {
    private static final long serialVersionUID = -70486868528085223L;

    @ApiModelProperty("员工ID")
    @TableField("`employee_id`")
    private Long employeeId;

    @ApiModelProperty("消息通知场景key")
    @TableField("`notice_scene_key`")
    private String noticeSceneKey;

    @ApiModelProperty("消息通知场景id")
    @TableField("`notice_scene_id`")
    private Long noticeSceneId;
}

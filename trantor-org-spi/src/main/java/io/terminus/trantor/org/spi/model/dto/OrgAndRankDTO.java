package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;



@Data
@EqualsAndHashCode(callSuper = false)
public class OrgAndRankDTO extends BaseModel {
    private static final long serialVersionUID = -8223888913111708160L;
    @ApiModelProperty("组织信息")
    private OrgUnitDTO orgUnitDTO;
    @ApiModelProperty("职级信息")
    private OrgRankCfDTO rankCfDTO;

}

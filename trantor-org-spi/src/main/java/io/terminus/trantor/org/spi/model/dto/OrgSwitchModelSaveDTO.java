package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-12-29 15:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgSwitchModelSaveDTO extends BaseModel {

    private static final long serialVersionUID = 7684615124169014285L;

    @ApiModelProperty("模型表名")
    private String modelKey;

    @ApiModelProperty("模型表中文名")
    private String modelName;

    @ApiModelProperty("模型是否开关")
    private Boolean isOpen;

    @ApiModelProperty("模型说明")
    private String describe;

    @ApiModelProperty("菜单名称")
    private String menu;

    @ApiModelProperty("菜单功能")
    private String menuFunction;
}

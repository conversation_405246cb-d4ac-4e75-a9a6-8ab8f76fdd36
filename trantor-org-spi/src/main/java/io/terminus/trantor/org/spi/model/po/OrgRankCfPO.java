package io.terminus.trantor.org.spi.model.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * (OrgRankCf)存储模型
 *
 * <AUTHOR>
 * @since 2023-10-23 16:39:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_rank_cf")
public class OrgRankCfPO extends BaseModel {
    private static final long serialVersionUID = 193596632326260258L;

    @ApiModelProperty("编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

}

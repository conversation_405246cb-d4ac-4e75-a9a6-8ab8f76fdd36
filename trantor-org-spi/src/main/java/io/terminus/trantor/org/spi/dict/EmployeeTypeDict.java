package io.terminus.trantor.org.spi.dict;

/**
 * @author: 张博
 * @date: 2023-02-27 20:06
 * 员工类型
 */
public interface EmployeeTypeDict {
    /**
     * 正式
     */
    String FORMAL = "FORMAL";
    /**
     * 试用
     */
    String PROBATION = "PROBATION";
    /**
     * 临时
     */
    String TEMPORARY = "TEMPORARY";
    /**
     * 外包
     */
    String EPIBOLY = "EPIBOLY";
    /**
     * 实习
     */
    String PRACTICE = "PRACTICE";
}

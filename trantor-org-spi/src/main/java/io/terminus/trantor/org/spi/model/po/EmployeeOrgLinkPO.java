package io.terminus.trantor.org.spi.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织员工关联表(EmployeeLink)实体类
 *
 * <AUTHOR>
 * @since 2023-02-27 20:42:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_employee_org_link_cf", excludeProperty = {"extra"})
public class EmployeeOrgLinkPO extends BaseModel {
    private static final long serialVersionUID = -70486868528085223L;

    @ApiModelProperty("员工ID")
    @TableField("`employee_id`")
    private Long employeeId;

    @ApiModelProperty("员工Code")
    @TableField("`employee_code`")
    private String employeeCode;

    @ApiModelProperty("组织单元ID")
    @TableField("`org_unit_id`")
    private Long orgUnitId;

    @ApiModelProperty("组织单元Code")
    @TableField("`org_unit_code`")
    private String orgUnitCode;

    @ApiModelProperty("身份ID")
    @TableField("`identity_id`")
    private Long identityId;

    @ApiModelProperty("组织单元ID")
    @TableField("`is_main_org`")
    private Boolean isMainOrg;
}

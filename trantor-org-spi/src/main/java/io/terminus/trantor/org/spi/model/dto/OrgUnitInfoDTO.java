package io.terminus.trantor.org.spi.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织单元表(Unit)实体类
 *
 * <AUTHOR>
 * @since 2023-07-11 20:42:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrgUnitInfoDTO extends BaseModel {
    private static final long serialVersionUID = 946186919999780988L;

    @ApiModelProperty("组织编码")
    private String code;

    @ApiModelProperty("组织名称")
    private String name;

    @ApiModelProperty("父ID")
    private Long pid;

    @ApiModelProperty("是否有下级节点")
    private Boolean hasChild;

    @ApiModelProperty("组织状态")
    private String status;

    @ApiModelProperty("路径")
    private String path;

    @ApiModelProperty("用户组")
    private Long userGroup;
}

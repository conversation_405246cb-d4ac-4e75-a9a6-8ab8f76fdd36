package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-12-11 16:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgSwitchUserComDTO extends BaseModel {
    private static final long serialVersionUID = 1817836645100807553L;

    @ApiModelProperty("公司名称")
    private String name;

    @ApiModelProperty("是否总部")
    private Boolean isHeadquarters;
}

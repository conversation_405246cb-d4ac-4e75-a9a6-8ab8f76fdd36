package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-02-28 11:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgUnitDTO extends BaseModel {

    private static final long serialVersionUID = -3407247983836586729L;

    @ApiModelProperty("组织编码")
    private String code;

    @ApiModelProperty("组织名称")
    private String name;

    @ApiModelProperty("父ID")
    private Long pid;

    @ApiModelProperty("父节点信息")
    private OrgUnitDTO parent;

    @ApiModelProperty("是否有下级节点")
    private Boolean hasChild;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("职级ID")
    private Long rankId;

    @ApiModelProperty("组织单元ID")
    private Boolean isMainOrg;

    @ApiModelProperty("业务类型")
    private List<OrgBizTypeLinkDTO> bizTypes;

    @ApiModelProperty("组织角色关联信息")
    private List<OrgRoleLinkDTO> orgRoleLinkList;

    @ApiModelProperty("下级节点")
    private List<OrgUnitDTO> child;
}

package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * @author: 张博
 * @date: 2023-09-21 11:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizTypeQueryDTO extends AbstractRequest {
    private static final long serialVersionUID = -607968627706273792L;

    @ApiModelProperty("业务类型编码集合")
    private Set<String> codes;

    @ApiModelProperty("父ID")
    private Long pid;
}

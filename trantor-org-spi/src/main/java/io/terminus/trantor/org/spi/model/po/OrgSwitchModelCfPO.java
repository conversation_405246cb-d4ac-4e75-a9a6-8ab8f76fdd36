package io.terminus.trantor.org.spi.model.po;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 切换公司模型表(OrgSwitchModelCf)存储模型
 *
 * <AUTHOR>
 * @since 2023-12-11 11:30:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_switch_model_cf")
public class OrgSwitchModelCfPO extends BaseModel {
    private static final long serialVersionUID = 746735700289949401L;

    @ApiModelProperty("模型表名")
    @TableField("`model_key`")
    private String modelKey;

    @ApiModelProperty("模型表中文名")
    @TableField("`model_name`")
    private String modelName;

    @ApiModelProperty("模型是否开关")
    @TableField("`is_open`")
    private Boolean isOpen;

    @ApiModelProperty("模型说明")
    @TableField("`describe`")
    private String describe;

    @ApiModelProperty("菜单名称")
    @TableField("`menu`")
    private String menu;

    @ApiModelProperty("菜单功能")
    @TableField("`menu_function`")
    private String menuFunction;

}

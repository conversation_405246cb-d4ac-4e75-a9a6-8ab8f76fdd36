package io.terminus.trantor.org.spi.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 员工表(Employee)实体类
 *
 * <AUTHOR>
 * @since 2023-02-27 20:38:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_employee_md", excludeProperty = {"extra"})
public class EmployeePO extends BaseModel {
    private static final long serialVersionUID = 620787566296328862L;

    @ApiModelProperty("用户ID")
    @TableField("`user_id`")
    private Long userId;

    @ApiModelProperty("用户名")
    @TableField("`user_name`")
    private String userName;

    @ApiModelProperty("编码")
    @TableField("`code`")
    private String code;

    @ApiModelProperty("类型")
    @TableField("`type`")
    private String type;

    @ApiModelProperty("组织")
    @TableField("`org_struct_id`")
    private Long orgStructId;

    @ApiModelProperty("名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("状态")
    @TableField("`status`")
    private String status;

    @ApiModelProperty("邮箱")
    @TableField("`email`")
    private String email;

    @ApiModelProperty("手机号码")
    @TableField("`mobile`")
    private String mobile;

    @ApiModelProperty("身份证号")
    @TableField("`id_card`")
    private String idCard;

    @ApiModelProperty("工作地点ID")
    @TableField("`address_id`")
    private Long addressId;

    @ApiModelProperty("详细地址")
    @TableField("`address_detail`")
    private String addressDetail;

    @ApiModelProperty("入职日期")
    @TableField("`entry_at`")
    private LocalDateTime entryAt;

    @ApiModelProperty("离职日期")
    @TableField("`resignation_at`")
    private LocalDateTime resignationAt;

    @ApiModelProperty("钉钉通知")
    @TableField("`ding_talk_code`")
    private String dingTalkCode;
}

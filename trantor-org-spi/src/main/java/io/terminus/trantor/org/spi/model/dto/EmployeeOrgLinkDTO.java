package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织员工关联表(EmployeeLink)实体类
 *
 * <AUTHOR>
 * @since 2023-02-27 20:42:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EmployeeOrgLinkDTO extends BaseModel {
    private static final long serialVersionUID = -70486868528085223L;

    @ApiModelProperty("员工")
    @MetaModelField
    private Long employeeId;

    @ApiModelProperty("员工名称")
    private String employeeName;

    @ApiModelProperty("组织单元")
    @MetaModelField
    private Long orgUnitId;

    @ApiModelProperty("身份")
    @MetaModelField
    private Long identityId;

    @ApiModelProperty("是否主组织")
    private Boolean isMainOrg;

    @ApiModelProperty("身份名称")
    private String identityName;
}

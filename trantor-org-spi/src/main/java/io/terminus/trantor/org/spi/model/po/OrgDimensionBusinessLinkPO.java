package io.terminus.trantor.org.spi.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023-11-14 14:48:14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_dimension_business_link")
public class OrgDimensionBusinessLinkPO extends BaseModel {
    private static final long serialVersionUID = 288932006438015895L;

    @ApiModelProperty("组织维度id")
    @TableField("`org_dimension_id`")
    private Long orgDimensionId;

    @ApiModelProperty("组织业务类型ID")
    @TableField("`org_business_type_id`")
    private Long orgBusinessTypeId;

}

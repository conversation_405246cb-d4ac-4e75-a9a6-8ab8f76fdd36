package io.terminus.trantor.org.spi.model.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织属性表(GenAttrCf)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-10 11:09:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "gen_attr_cf")
public class GenAttrCfPO extends BaseModel {
    private static final long serialVersionUID = 820415025661085430L;

    @ApiModelProperty("组织属性编码")
    @TableField("`attr_code`")
    private String attrCode;

    @ApiModelProperty("组织属性名称")
    @TableField("`attr_name`")
    private String attrName;

    @ApiModelProperty("组织属性数据类型")
    @TableField("`attr_data_type`")
    private String attrDataType;

    @ApiModelProperty("是否必填")
    @TableField("`attr_is_require`")
    private Boolean attrIsRequire;

    @ApiModelProperty("长度")
    @TableField("`attr_length`")
    private Long attrLength;

    @ApiModelProperty("对象基本信息")
    @TableField("`object_key`")
    private String objectKey;

    @ApiModelProperty("对象查询条件")
    @TableField("`object_meta`")
    private String objectMeta;

    @ApiModelProperty("对应组织表的预留列")
    @TableField("`struct_db`")
    private String structDb;

    @ApiModelProperty("是否多选")
    @TableField("`attr_is_multi`")
    private Boolean attrIsMulti;

    @ApiModelProperty("状态")
    @TableField("`attr_status`")
    private String attrStatus;

    @ApiModelProperty("类别")
    @TableField("`attr_class`")
    private String attrClass;

}

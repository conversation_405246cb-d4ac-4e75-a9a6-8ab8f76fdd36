package io.terminus.trantor.org.spi.model.po;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织关联规则设置(OrgRelationRuleCf)存储模型
 *
 * <AUTHOR>
 * @since  2024-03-19 10:51:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "org_relation_rule_cf")
public class OrgRelationRuleCfPO extends BaseModel {
    private static final long serialVersionUID = -12985958418566841L;

    @ApiModelProperty("源组织维度")
    @TableField("`org_head_dimension_id`")
    private Long orgHeadDimensionId;

    @ApiModelProperty("源组织类型")
    @TableField("`org_head_unit_type_id`")
    private Long orgHeadUnitTypeId;

    @ApiModelProperty("关联组织维度")
    @TableField("`org_relation_dimension_id`")
    private Long orgRelationDimensionId;

    @ApiModelProperty("关联组织类型")
    @TableField("`org_relation_unit_type_id`")
    private Long orgRelationUnitTypeId;

    @ApiModelProperty("关联类型")
    @TableField("`org_relation_rule_type`")
    private String orgRelationRuleType;

    @ApiModelProperty("所属组织")
    @TableField("`origin_org_id`")
    private Long originOrgId;

}

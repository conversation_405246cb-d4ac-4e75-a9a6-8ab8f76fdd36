package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.request.AbstractRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-06-14 10:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EmployeeQueryDTO extends AbstractRequest {
    private static final long serialVersionUID = -591253018205012072L;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("员工编码")
    private String code;
}

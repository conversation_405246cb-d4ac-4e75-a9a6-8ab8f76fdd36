package io.terminus.trantor.org.spi.model.dto.event;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-07-26 10:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgUnitEnableEventDTO extends BaseModel {
    private static final long serialVersionUID = -5457133710966976000L;

    public final static String TAG = "ORG_ORG_UNIT_ENABLE_TAG";

    @ApiModelProperty("组织名称")
    private String name;

    @ApiModelProperty("组织编码")
    private String code;

    @ApiModelProperty("组织modelKey")
    private String modelKey;
}

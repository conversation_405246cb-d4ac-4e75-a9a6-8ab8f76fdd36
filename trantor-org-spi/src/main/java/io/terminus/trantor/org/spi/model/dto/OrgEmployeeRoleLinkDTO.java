package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 张博
 * @date: 2023-11-17 13:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrgEmployeeRoleLinkDTO extends BaseModel {
    private static final long serialVersionUID = -5266325092077338855L;

    @ApiModelProperty("员工ID")
    private Long employeeId;

    @ApiModelProperty("组织ID")
    private Long orgStructId;
}

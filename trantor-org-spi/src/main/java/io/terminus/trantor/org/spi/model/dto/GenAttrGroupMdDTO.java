package io.terminus.trantor.org.spi.model.dto;



import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 特征类定义表(GenCharaClassMd)存储模型
 *
 * <AUTHOR>
 * @since  2023-11-09 19:15:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GenAttrGroupMdDTO extends BaseModel {
    private static final long serialVersionUID = 123168798766877861L;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("类类别")
    private String attrClass;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("类型配置属性定义集合")
    private List<GenAttrCfDTO> attrList;
}

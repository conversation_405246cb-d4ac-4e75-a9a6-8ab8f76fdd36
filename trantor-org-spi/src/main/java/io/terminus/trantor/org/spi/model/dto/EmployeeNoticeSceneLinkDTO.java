package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import io.terminus.trantor2.doc.annotation.MetaModelField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织员工关联表(EmployeeLink)实体类
 *
 * <AUTHOR>
 * @since 2023-02-27 20:42:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EmployeeNoticeSceneLinkDTO extends BaseModel {
    private static final long serialVersionUID = -70486868528085223L;

    @ApiModelProperty("员工ID")
    private Long employeeId;

    @ApiModelProperty("消息通知场景key")
    private String noticeSceneKey;

    @ApiModelProperty("消息通知场景id")
    @MetaModelField
    private Long noticeSceneId;
}

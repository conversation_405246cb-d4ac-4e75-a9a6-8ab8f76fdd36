package io.terminus.trantor.org.spi.model.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructMdHistoryDetialDTO {
    private static final long serialVersionUID = 5088680558809331886L;

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("是否历史记录查询")
    private Long historyId;
}

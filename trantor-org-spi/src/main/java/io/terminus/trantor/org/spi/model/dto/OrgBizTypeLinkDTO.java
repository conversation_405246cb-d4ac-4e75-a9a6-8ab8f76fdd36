package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @author: 张博
 * @date: 2023-03-03 10:10
 */
@Data
public class OrgBizTypeLinkDTO implements Serializable {
    private static final long serialVersionUID = -5592300139137127462L;

    @ApiModelProperty("业务类型ID")
    private Long bizTypeId;

    @ApiModelProperty("业务类型标识")
    private String bizTypeKey;

    @ApiModelProperty("模型key")
    private String modelKey;

    @ApiModelProperty("业务类型名称")
    private String bizTypeName;

    @ApiModelProperty("业务类型扩展数据")
    private Map<String, Object> extra;
}

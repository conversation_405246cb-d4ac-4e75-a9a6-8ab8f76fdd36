package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@Data
@EqualsAndHashCode(callSuper = false)
public class EmployeeQueryCodes extends BaseModel {
    private static final long serialVersionUID = -591288998205012072L;

    @ApiModelProperty("员工编码")
    private List<String> codes;

}

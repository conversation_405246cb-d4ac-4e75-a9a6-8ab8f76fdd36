package io.terminus.trantor.org.spi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructMdQueryTreeDTO  extends OrgDimensionDTO {

    private static final long serialVersionUID = 5038380590809091886L;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("状态")
    private List<String> orgStatus;

    @ApiModelProperty("查询时间")
    private LocalDateTime orgEditionDate;
}

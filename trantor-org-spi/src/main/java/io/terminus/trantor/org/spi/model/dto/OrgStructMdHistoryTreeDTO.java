package io.terminus.trantor.org.spi.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import io.terminus.common.api.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
public class OrgStructMdHistoryTreeDTO extends BaseModel {
    private static final long serialVersionUID = 5038389958809091886L;

    @ApiModelProperty("当前记录id")
    private Long historyId;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("组织名称")
    private String orgName;

    @ApiModelProperty("父组织ID")
    private Long orgParentId;

    @ApiModelProperty("是否叶子节点")
    private Boolean leaf;

    @ApiModelProperty("状态")
    private String orgStatus;

    @ApiModelProperty("行政组织ID")
    private Long id;

    @ApiModelProperty("版本日期")
    private LocalDateTime orgEditionDate;

}

package io.terminus.trantor.org.adapter.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.trantor.org.app.service.OrgOpenSyncService;
import io.terminus.trantor.org.spi.model.req.OrgSyncDTO;
import io.terminus.trantor2.common.dto.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @time 2025/8/26 11:02
 */
@Api(tags = "供应商同步晶澳数据")
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/api/tsrm/md/org/sync")
public class OrgSyncController {

    private final OrgOpenSyncService orgOpenSyncService;

    @ApiOperation("VEND-同步新增组织")
    @PostMapping(value = "addOrg")
    public Response<Void> addOrg(@RequestBody OrgSyncDTO orgSyncDTO) {
        try {
            log.info("接收到新增组织同步请求: {}", orgSyncDTO);
            orgOpenSyncService.addOrUpdateOrg(orgSyncDTO);
            return Response.ok();
        } catch (BusinessException e) {
            log.error("组织新增同步失败: {}", e.getMessage(), e);
            return Response.error("500", e.getMessage());
        } catch (Exception e) {
            log.error("组织新增同步失败: {}", e.getMessage(), e);
            return Response.error("500", "组织新增同步失败");
        }
    }

    @ApiOperation("VEND-同步更新组织")
    @PostMapping(value = "updateOrg")
    public Response<Void> updateOrg(@RequestBody OrgSyncDTO orgSyncDTO) {
        try {
            log.info("接收到更新组织同步请求: {}", orgSyncDTO);
            orgOpenSyncService.addOrUpdateOrg(orgSyncDTO);
            return Response.ok();
        } catch (BusinessException e) {
            log.error("组织更新同步失败: {}", e.getMessage(), e);
            return Response.error("500", e.getMessage());
        } catch (Exception e) {
            log.error("组织更新同步失败: {}", e.getMessage(), e);
            return Response.error("500", "组织更新同步失败");
        }
    }

    @ApiOperation("VEND-同步删除组织")
    @PostMapping(value = "deleteOrg")
    public Response<Void> deleteOrg(@RequestBody OrgSyncDTO orgSyncDTO) {
        try {
            log.info("接收到删除组织同步请求: {}", orgSyncDTO);
            orgOpenSyncService.deleteOrg(orgSyncDTO);
            return Response.ok();
        } catch (BusinessException e) {
            log.error("组织删除同步失败: {}", e.getMessage(), e);
            return Response.error("500", e.getMessage());
        } catch (Exception e) {
            log.error("组织删除同步失败: {}", e.getMessage(), e);
            return Response.error("500", "组织删除同步失败");
        }
    }
}

package io.terminus.trantor.org.adapter.facade;

import com.google.common.collect.Sets;
import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.api.facade.EmployeeReadFacade;
import io.terminus.trantor.org.app.service.EmployeeAppService;
import io.terminus.trantor.org.spi.model.dto.*;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-07-11 11:36
 */
@RestController
@RequiredArgsConstructor
public class EmployeeReadFacadeImpl implements EmployeeReadFacade {

    private final EmployeeAppService employeeAppService;

    @Override
    public Response<List<Long>> queryEmployeeOrg(UserOrgQueryDTO request) {
        return Response.ok(employeeAppService.queryEmployeeOrg(request));
    }

    @Override
    public Response<EmployeeDTO> queryEmployeeByCode(EmployeeQueryDTO request) {
        return Response.ok(employeeAppService.queryEmployeeByCode(request.getCode()));
    }

    @Override
    public Response<EmployeeDTO> queryEmployeeId(IdRequest request) {
        return Response.ok(employeeAppService.queryEmployeeById(request.getId()));
    }

    @Override
    public Response<List<EmployeeDTO>> queryEmployeeIds(IdsRequest request) {
        return Response.ok(employeeAppService.queryEmployeeByIds(Sets.newHashSet(request.getIds())));
    }

    @Override
    public Response<EmployeeDTO> queryEmployeeByUserId(EmployeeQueryDTO request) {
        return Response.ok(employeeAppService.queryEmployeeByUserId(request.getUserId()));
    }

    @Override
    public Response<Paging<EmployeeDTO>> paging(EmployeePageQueryDTO request) {
        Paging<EmployeeDTO> paging = employeeAppService.paging(request);
        if (!CollectionUtils.isEmpty(paging.getData())) {
            for (EmployeeDTO datum : paging.getData()) {
                if (StringUtils.hasText(datum.getMobile())) {
                    datum.setMobile(datum.getMobile().replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
                }
            }
        }
        return Response.ok(paging);
    }

    @Override
    public Response<List<EmployeeDTO>> queryOrgUnitAppointRankEmployee(OrgRankEmployeeQueryDTO request) {
        return Response.ok(employeeAppService.queryOrgUnitAppointRankEmployee(request));
    }

    @Override
    public Response<OrgAndRankDTO> queryOrgUnitRoleByCode(EmployeeQueryDTO request) {
        return Response.ok(employeeAppService.queryOrgUnitRoleByCode(request.getCode()));
    }

    @Override
    public Response<List<EmployeeRankDTO>> queryEmployeeByOrgRankNew(List<EmployeeQueryOrgRoleDTO> request) {
        return Response.ok(employeeAppService.queryEmployeeByOrgRankNew(request));
    }

    @Override
    public Response<List<EmployeeDTO>> queryEmployeeByOrgRank(List<EmployeeQueryOrgRoleDTO> request) {
        return Response.ok(employeeAppService.queryEmployeeByOrgRank(request));
    }

    @Override
    public Response<OrgAndRankDTO> queryOrgUnitsupRankByCode(EmployeeSupQueryDTO request) {
        return Response.ok(employeeAppService.queryOrgUnitSupRankByCode(request));
    }

    @Override
    public Response<List<EmployeeDTO>> queryEmployeeByCodes(EmployeeQueryCodes request) {
        return Response.ok(employeeAppService.queryEmployeeByCodes(request));
    }

    @Override
    public Response<List<EmployeeDTO>> queryEmployeeByOrgUnitCode(OrgUnitCodeQueryDto request) {
        return Response.ok(employeeAppService.queryEmployeeByOrgUnitCode(request));
    }

    @Override
    public Response<List<Long>> queryCurrentUserOrgAndAllChildOrg() {
        return Response.ok(employeeAppService.queryCurrentUserOrgAndAllChildOrg());
    }

    @Override
    public Response<List<Long>> queryCurrentUserAllChildOrg() {
        return Response.ok(employeeAppService.queryCurrentUserAllChildOrg());
    }

    @Override
    public Response<List<Long>> queryCurrentUserAllChildUser() {
        return Response.ok(employeeAppService.queryCurrentUserAllChildUser());
    }

    @Override
    public Response<String> queryCurrentEmployeeForIam() {
        return Response.ok(employeeAppService.queryCurrentEmployeeForIam());
    }
}

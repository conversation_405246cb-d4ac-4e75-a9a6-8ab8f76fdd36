package io.terminus.trantor.org.adapter.admin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.domain.service.OrgSwitchService;
import io.terminus.trantor.org.spi.model.dto.*;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "组织切换")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/org-switch")
public class OrgSwitchAction {

    private final OrgSwitchService orgSwitchService;

    @ApiOperation("当前登陆人是否在当当前组织内")
    @PostMapping("/judge-user-is-exist-current-org")
    @Action(name = "当前登陆人是否在当当前组织内", value = "SPI_JUDGE_LOGGING_USER_IN_CURRENT_ORG")
    public Response<Boolean> judgeUserIsExistCurrentOrg(@RequestBody JudgeUserInOrgDTO request) {
        return Response.ok(orgSwitchService.judgeUserIsExistCurrentOrg(request));
    }

    @ApiOperation("模型是否开启了组织切换")
    @PostMapping("/judge-model-open-org-switch")
    @Action(name = "模型是否开启了组织切换", value = "SPI_JUDGE_MODEL_ORG_SWITCH_ENABLE")
    public Response<Boolean> judgeModelOpenOrgSwitch(@RequestBody JudgeModelOrgSwitchDTO request) {
        return Response.ok(orgSwitchService.judgeModelOpenOrgSwitch(request));
    }

    @ApiOperation("查询用户所在的公司")
    @PostMapping("/query-user-com")
    @Action(name = "查询用户所在的公司", value = "ORG_SWITCH_QUERY_USER_COM_ACTION")
    public Response<List<OrgSwitchUserComDTO>> queryUserComList(@RequestBody IdDTO request) {
        return Response.ok(orgSwitchService.queryUserComList());
    }

    @ApiOperation("保存组织切换模型")
    @PostMapping("/org-switch-model-save")
    @Action(name = "保存组织切换模型", value = "ORG_SWITCH_MODEL_SAVE_ACTION")
    public Response<OrgSwitchModelSaveDTO> switchModelSave(@RequestBody OrgSwitchModelSaveDTO request) {
        return Response.ok(orgSwitchService.switchModelSave(request));
    }

    @ApiOperation("删除组织切换模型")
    @PostMapping("/org-switch-model-delete")
    @Action(name = "删除组织切换模型", value = "ORG_SWITCH_MODEL_DELETE_ACTION")
    public Response<Void> switchModelDelete(@RequestBody IdRequest request) {
        orgSwitchService.switchModelDelete(request);
        return Response.ok();
    }
}

package io.terminus.trantor.org.adapter.api;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.domain.service.LegalComService;
import io.terminus.trantor.org.spi.model.dto.LegalComDTO;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-11-28 19:13
 */
@Api(tags = "法人公司")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/service/trantor/org/legal-com")
public class LegalComApiAction {

    private final LegalComService legalComService;

    @ApiOperation("组织批量查询法人公司")
    @PostMapping("/query-by-org-ids")
    @Action(name = "组织批量查询法人公司", value = "ORG_API_QUERY_LEGAL_COM_BY_STRUCT_IDS_ACTION")
    public Response<List<LegalComDTO>> queryLegalComByOrgStructIds(@RequestBody IdsRequest request) {
        return Response.ok(legalComService.queryLegalComByOrgStructIds(request));
    }

}

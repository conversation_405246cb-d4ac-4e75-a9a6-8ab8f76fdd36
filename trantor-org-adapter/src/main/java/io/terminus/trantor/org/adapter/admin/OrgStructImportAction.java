package io.terminus.trantor.org.adapter.admin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.response.Response;
import io.terminus.gei.service.api.entity.ImportResult;
import io.terminus.gei.service.api.entity.ImportSliceData;
import io.terminus.trantor.org.domain.service.impt.OrgCreateImportService;
import io.terminus.trantor.org.domain.service.impt.OrgDisableImportService;
import io.terminus.trantor.org.domain.service.impt.OrgStructImportService;
import io.terminus.trantor.org.domain.service.impt.OrgUpdateImportService;
import io.terminus.trantor.org.spi.model.dto.OrgStructImportTemplateDTO;
import io.terminus.trantor.org.spi.model.dto.OrgStructImportTemplateQueryDTO;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-11-30 19:50
 */
@Api(tags = "行政组织架构管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/org-struct-import")
public class OrgStructImportAction {

    private final OrgStructImportService orgStructImportService;
    private final OrgCreateImportService orgCreateImportService;
    private final OrgUpdateImportService orgUpdateImportService;
    private final OrgDisableImportService orgDisableImportService;

    @ApiOperation("获取组织导入的模版")
    @PostMapping("/query-template")
    @Action(name = "获取组织导入的模版", value = "ORG_STRUCT_FIND_IMPORT_TEMPLATE_ACTION")
    public Response<OrgStructImportTemplateDTO> findImportTemplate(@RequestBody OrgStructImportTemplateQueryDTO request) {
        return Response.ok(orgStructImportService.findImportTemplate(request));
    }

    @ApiOperation("组织新增导入")
    @PostMapping("/org-create-import")
    @Action(name = "组织新增导入", value = "ORG_STRUCT_CREATE_IMPORT_ACTION")
    public Response<List<ImportResult>> createImport(@RequestBody ImportSliceData request) {
        return Response.ok(orgCreateImportService.importData(request));
    }

    @ApiOperation("组织调整导入")
    @PostMapping("/org-update-import")
    @Action(name = "组织调整导入", value = "ORG_STRUCT_UPDATE_IMPORT_ACTION")
    public Response<List<ImportResult>> updateImport(@RequestBody ImportSliceData request) {
        return Response.ok(orgUpdateImportService.importData(request));
    }

    @ApiOperation("组织禁用导入")
    @PostMapping("/org-disable-import")
    @Action(name = "组织禁用导入", value = "ORG_STRUCT_DISABLE_IMPORT_ACTION")
    public Response<List<ImportResult>> disableImport(@RequestBody ImportSliceData request) {
        return Response.ok(orgDisableImportService.importData(request));
    }
}

package io.terminus.trantor.org.adapter.api;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.domain.service.OrgStructMdService;
import io.terminus.trantor.org.spi.model.dto.*;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Api(tags = "行政组织架构管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/service/trantor/org/org-struct-md")
public class OrgStructMdApiAction {

    private final OrgStructMdService orgStructMdService;

    @ApiOperation("新版组织分页查询")
    @PostMapping("/page")
    @Action(name = "新版组织分页查询", value = "ORG_API_STRUCT_MD_PAGE_ACTION")
    public Response<Paging<OrgStructMdDetailDTO>> paging(@RequestBody OrgStructPageQueryDTO request) {
        return Response.ok(orgStructMdService.paging(request));
    }

    @ApiOperation("新版组织分页查询(Map)")
    @PostMapping("/page-map")
    @Action(name = "新版组织分页查询(Map)", value = "ORG_API_STRUCT_MD_PAGE_MAP_ACTION")
    public Response<Paging<Map<String, Object>>> pagingMap(@RequestBody OrgStructPageQueryDTO request) {
        return Response.ok(orgStructMdService.pagingMap(request));
    }

    @ApiOperation("新版组织查询详情")
    @PostMapping("/detail")
    @Action(name = "新版组织查询详情", value = "ORG_API_STRUCT_MD_QUERY_DETAIL_ACTION")
    public Response<OrgStructMdDetailDTO> queryById(@RequestBody IdRequest request) {
        return Response.ok(orgStructMdService.queryById(request));
    }

    @ApiOperation("新版组织查询详情(Map)")
    @PostMapping("/detail-map")
    @Action(name = "新版组织查询详情(Map)", value = "ORG_API_STRUCT_MD_QUERY_MAP_DETAIL_ACTION")
    public Response<Map<String, Object>> queryDetailMapById(@RequestBody IdRequest request) {
        return Response.ok(orgStructMdService.queryDetailMapById(request));
    }

    @ApiOperation("新版组织查询路径")
    @PostMapping("/path")
    @Action(name = "新版组织查询路径", value = "ORG_API_STRUCT_MD_QUERY_PATH_ACTION")
    public Response<List<OrgStructMdDetailDTO>> queryPath(@RequestBody IdRequest request) {
        return Response.ok(orgStructMdService.queryPath(request));
    }

    @ApiOperation("新版组织查询详情(Map)")
    @PostMapping("/detail-map-by-ids")
    @Action(name = "新版组织批量查询详情(Map)", value = "ORG_API_STRUCT_MD_QUERY_MAP_DETAIL_BY_IDS_ACTION")
    public Response<List<Map<String, Object>>> queryDetailMapByIds(@RequestBody IdsRequest request) {
        return Response.ok(orgStructMdService.queryDetailMapByIds(request));
    }

    @ApiOperation("根据用户ID查询用户所在的组织")
    @PostMapping("/query-user-org-by-user-id")
    @Action(name = "根据用户ID查询用户所在的组织", value = "ORG_API_STRUCT_MD_QUERY_USER_ORG_BY_USER_ID_ACTION")
    public Response<List<OrgStructMdDetailDTO>> queryUserOrgByUserId(@RequestBody IdRequest request) {
        return Response.ok(orgStructMdService.queryUserOrgByUserId(request));
    }

    @ApiOperation("查询当前用户所负责的组织(临时方案)")
    @PostMapping("/query-user-charge-org")
    @Action(name = "查询当前用户所负责的组织(临时方案)", value = "ORG_API_STRUCT_MD_QUERY_USER_CHARGE_ORG_ACTION")
    public Response<List<Long>> queryUserChargeOrg(@RequestBody IdDTO request) {
        return Response.ok(orgStructMdService.queryUserChargeOrg());
    }

    @ApiOperation("根据组织id集合查询组织全路径")
    @PostMapping("/query-path-by-ids")
    @Action(name = "根据组织id集合查询组织全路径", value = "ORG_API_STRUCT_MD_QUERY_PATH_BY_IDS_ACTION")
    public Response<List<TreePathDTO>> queryPathByIds(@RequestBody IdsRequest request) {
        return Response.ok(orgStructMdService.queryPathByIds(request));
    }

    @ApiOperation("根据维度编码组织编码查询组织信息")
    @PostMapping("/query-by-cods")
    @Action(name = "根据维度编码组织编码查询组织信息", value = "ORG_API_STRUCT_MD_QUERY_BY_CODES_ACTION")
    public Response<List<OrgStructMdDetailDTO>> queryStructByCodes(@RequestBody OrgQueryCodesDTO request) {
        return Response.ok(orgStructMdService.queryStructByCodes(request));
    }

    @ApiOperation("根据维度编码组织名称查询组织信息")
    @PostMapping("/query-by-names")
    @Action(name = "根据维度编码组织名称查询组织信息", value = "ORG_API_STRUCT_MD_QUERY_BY_NAMES_ACTION")
    public Response<List<OrgStructMdDetailDTO>> queryStructByNames(@RequestBody OrgQueryNamesDTO request) {
        return Response.ok(orgStructMdService.queryStructByNames(request));
    }

    @ApiOperation("查询当前组织某种类型的上级组织")
    @PostMapping("/query-parent-by-type")
    @Action(name = "查询当前组织某种类型的上级组织", value = "ORG_API_STRUCT_MD_QUERY_PARENT_BY_TYPE_ACTION")
    public Response<OrgStructMdDetailDTO> queryParentOrgByType(@RequestBody OrgParentQueryDTO request) {
        return Response.ok(orgStructMdService.queryParentOrgByType(request));
    }

    @ApiOperation("新版组织查询详情(Map)")
    @PostMapping("/detail-map-by-codes")
    @Action(name = "新版组织根据codes查询详情(Map)", value = "ORG_API_STRUCT_MD_QUERY_MAP_DETAIL_BY_CODES_ACTION")
    public Response<List<Map<String, Object>>> queryDetailMapByCodes(@RequestBody OrgQueryCodesDTO request) {
        return Response.ok(orgStructMdService.queryDetailMapByCodes(request));
    }

    @ApiOperation("查询当前登录用户某种类型的组织")
    @PostMapping("/query-current-org-by-type")
    @Action(name = "查询当前登录用户某种类型的组织", value = "ORG_API_QUERY_CURRENT_USER_ORG_BY_TYPE_ACTION")
    public Response<OrgStructMdDetailDTO> queryCurrentUserOrgByType(@RequestBody OrgParentQueryDTO request) {
        return Response.ok(orgStructMdService.queryCurrentUserOrgByType(request));
    }

    @ApiOperation("根据仓库批量查询库存地点")
    @PostMapping("/query-inv-loc-by-wh-num-ids")
    @Action(name = "根据仓库批量查询库存地点", value = "ORG_API_QUERY_INV_LOC_BY_WH_NUM_IDS_ACTION")
    public Response<List<OrgStructMdDetailDTO>> queryInvLocByWhNumIds(@RequestBody IdsRequest request) {
        return Response.ok(orgStructMdService.queryInvLocByWhNumIds(request));
    }

    @ApiOperation("批量查询组织所有的下级节点")
    @PostMapping("/batch-query-org-all-child")
    @Action(name = "批量查询组织所有的下级节点", value = "ORG_API_BATCH_QUERY_ORG_ALL_CHILD_ACTION")
    public Response<List<OrgStructMdDetailDTO>> batchQueryOrgAllChild(@RequestBody IdsRequest request) {
        return Response.ok(orgStructMdService.batchQueryOrgAllChild(request));
    }
}

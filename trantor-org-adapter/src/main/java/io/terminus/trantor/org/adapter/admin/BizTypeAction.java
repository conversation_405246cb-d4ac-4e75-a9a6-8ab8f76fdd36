package io.terminus.trantor.org.adapter.admin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.app.service.BizTypeAppService;
import io.terminus.trantor.org.spi.model.dto.*;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-07-18 10:22
 */
@Api(tags = "业务类型")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/biz-type")
public class BizTypeAction {

    private final BizTypeAppService bizTypeAppService;

    @ApiOperation("保存业务类型")
    @PostMapping("/save")
    @Action(name = "保存业务类型", value = "ORG_BIZ_TYPE_SAVE_ACTION")
    public Response<Void> save(@RequestBody BizTypeSaveDTO request) {
        bizTypeAppService.save(request);
        return Response.ok();
    }

    @ApiOperation("删除业务类型")
    @PostMapping("/delete")
    @Action(name = "删除业务类型", value = "ORG_BIZ_TYPE_DELETE_ACTION")
    public Response<Void> delete(@RequestBody IdRequest request) {
        bizTypeAppService.delete(request.getId());
        return Response.ok();
    }

    @ApiOperation("查询业务类型列表")
    @PostMapping("/list")
    @Action(name = "查询业务类型列表", value = "ORG_BIZ_TYPE_FIND_ALL_ACTION")
    public Response<List<BizTypeDTO>> findAll(@RequestBody BizTypeQueryDTO request) {
        return Response.ok(bizTypeAppService.findAll(request));
    }

    @ApiOperation("查询业务类型隐藏的字段")
    @PostMapping("/hidde-filed")
    @Action(name = "查询业务类型隐藏的字段", value = "ORG_BIZ_TYPE_QUERY_HIDDE_FILED_ACTION")
    public Response<BizTypeHideFileDTO> queryHiddeFiled(@RequestBody BizTypeHiddeFiledQueryDTO request) {
        return Response.ok(bizTypeAppService.queryHiddeFiled(request));
    }
}

package io.terminus.trantor.org.adapter.admin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.common.api.request.StringRequest;
import io.terminus.common.api.response.Response;
import io.terminus.gei.service.api.entity.ImportSliceData;
import io.terminus.trantor.org.app.service.EmployeeAppService;
import io.terminus.trantor.org.spi.model.dto.*;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * @author: 张博
 * @date: 2023-07-11 09:54
 */
@Api(tags = "员工信息管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/employee")
public class EmployeeAction {

    private final EmployeeAppService employeeAppService;

    @ApiOperation("保存员工")
    @PostMapping("/save")
    @Action(name = "保存员工", value = "ORG_EMPLOYEE_SAVE_ACTION")
    public Response<EmployeeSaveDTO> save(@RequestBody EmployeeSaveDTO request) {
        return Response.ok(employeeAppService.save(request));
    }

    @ApiOperation("查询员工详情")
    @PostMapping("/query-detail")
    @Action(name = "查询员工详情", value = "ORG_EMPLOYEE_QUERY_DETAIL_ACTION")
    public Response<EmployeeDTO> queryDetail(@RequestBody IdDTO request) {
        return Response.ok(employeeAppService.queryDetail(request.getId()));
    }

    @ApiOperation("删除员工")
    @PostMapping("/delete")
    @Action(name = "删除员工", value = "ORG_EMPLOYEE_DELETE_ACTION")
    public Response<Void> delete(@RequestBody IdRequest request) {
        employeeAppService.delete(request.getId());
        return Response.ok();
    }

    @ApiOperation("启用员工")
    @PostMapping("/enable")
    @Action(name = "启用员工", value = "ORG_EMPLOYEE_ENABLE_ACTION")
    public Response<Void> enable(@RequestBody IdRequest request) {
        employeeAppService.enable(request.getId());
        return Response.ok();
    }

    @ApiOperation("禁用员工")
    @PostMapping("/disable")
    @Action(name = "禁用员工", value = "ORG_EMPLOYEE_DISABLE_ACTION")
    public Response<Void> disable(@RequestBody IdRequest request) {
        employeeAppService.disable(request.getId());
        return Response.ok();
    }

    @ApiOperation("查询组织下的员工")
    @PostMapping("/page-by-org")
    @Action(name = "查询组织下的员工", value = "ORG_EMPLOYEE_PAGE_BY_ORG_ACTION")
    public Response<Paging<EmployeeDTO>> pageByOrg(@RequestBody EmployeePageQueryDTO request) {
        return Response.ok(employeeAppService.pageByOrg(request));
    }

    @ApiOperation("查询组织下(包含下级组织)的员工")
    @PostMapping("/page-by-org-contain-child-org")
    @Action(name = "查询组织下(包含下级组织)的员工", value = "ORG_EMPLOYEE_PAGE_BY_ORG_CONTAIN_CHILD_ORG_ACTION")
    public Response<Paging<EmployeeDTO>> pageByOrgContainChildOrg(@RequestBody EmployeePageQueryDTO request) {
        return Response.ok(employeeAppService.pageByOrgContainChildOrg(request));
    }

    @ApiOperation("查询用户组织下的员工")
    @PostMapping("/page-by-user-org-employee")
    @Action(name = "查询用户组织下的员工", value = "ORG_EMPLOYEE_PAGE_BY_USER_ORG_EMPLOYEE_ACTION")
    public Response<Paging<EmployeeDTO>> pageByUserOrgEmployee(@RequestBody EmployeePageQueryDTO request) {
        return Response.ok(employeeAppService.pageByUserOrgEmployee(request));
    }

    @ApiOperation("根据用户查询员工")
    @PostMapping("/query-by-user-id")
    @Action(name = "根据用户查询员工", value = "ORG_EMPLOYEE_QUERY_BY_USER_ID_ACTION")
    public Response<EmployeeDTO> queryByUserId(@RequestBody EmployeeQueryDTO request) {
        return Response.ok(employeeAppService.queryEmployeeByUserId(request.getUserId()));
    }

    @ApiOperation("根据用户ID集合批量查询员工")
    @PostMapping("/query-by-user-ids")
    @Action(name = "根据用户ID集合批量查询员工", value = "ORG_EMPLOYEE_QUERY_BY_USER_IDS_ACTION")
    public Response<List<EmployeeDTO>> queryByUserIds(@RequestBody IdsRequest request) {
        return Response.ok(employeeAppService.queryEmployeeByUserIds(new HashSet<>(request.getIds())));
    }

    @ApiOperation("员工分页查询")
    @PostMapping("/page")
    @Action(name = "员工分页查询", value = "ORG_EMPLOYEE_PAGE_ACTION")
    public Response<Paging<EmployeeDTO>> paging(@RequestBody EmployeePageQueryDTO request) {
        return Response.ok(employeeAppService.paging(request));
    }

    @ApiOperation("根据ID集合查询员工信息")
    @PostMapping("/query-by-ids")
    @Action(name = "根据ID集合查询员工信息", value = "ORG_EMPLOYEE_QUERY_BY_IDS_ACTION")
    public Response<List<EmployeeDTO>> queryByIds(@RequestBody IdsRequest request) {
        return Response.ok(employeeAppService.queryEmployeeByIds(new HashSet<>(request.getIds())));
    }

    @ApiOperation("根据编码集合查询员工信息")
    @PostMapping("/query-by-codes")
    @Action(name = "根据编码集合查询员工信息", value = "ORG_EMPLOYEE_QUERY_BY_CODES_ACTION")
    public Response<List<EmployeeDTO>> queryByCodes(@RequestBody EmployeeQueryCodes request) {
        return Response.ok(employeeAppService.queryEmployeeByCodes(request));
    }

    @ApiOperation("员工信息导入")
    @PostMapping("/import")
    @Action(name = "员工信息导入", value = "ORG_EMPLOYEE_IMPORT_ACTION")
    public Response<List<Map<String, Object>>> employeeImport(@RequestBody ImportSliceData request) {
        return Response.ok(employeeAppService.employeeImport(request));
    }

    @PostMapping("/query-by-notice-scene")
    @Action(name = "根据通知场景查询员工", value = "ORG_EMPLOYEE_QUERY_BY_NOTICE_SCENE_ACTION")
    public Response<List<EmployeeDTO>> queryByNoticeScene(StringRequest request) {
        return Response.ok(employeeAppService.queryByNoticeScene(request));
    }
}

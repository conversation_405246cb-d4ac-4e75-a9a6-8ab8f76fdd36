package io.terminus.trantor.org.adapter.api;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.model.BaseModel;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.app.service.OrgBaseModelAppService;
import io.terminus.trantor.org.spi.dict.OrgActionKeyDict;
import io.terminus.trantor.org.spi.model.req.OrgQueryByIdReq;
import io.terminus.trantor.org.spi.model.req.OrgQueryByIdsReq;
import io.terminus.trantor.org.spi.model.req.OrgQueryByUkReq;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "组织模型数据服务")
@RestController
@RequestMapping("/api/service/trantor/org/base-model")
@AllArgsConstructor
public class OrgBaseModelApiAction {
    private final OrgBaseModelAppService orgBaseModelAppService;

    @ApiOperation("根据Id查询指定组织数据模型数据")
    @PostMapping("/queryById")
    @Action(name = "根据Id查询指定组织数据模型数据", value = OrgActionKeyDict.QUERY_BY_ID)
    public Response<BaseModel> queryById(@RequestBody OrgQueryByIdReq req) {
        BaseModel model = orgBaseModelAppService.queryById(req);
        return Response.ok(model);
    }

    @ApiOperation("根据Id集合查询指定组织数据模型数据")
    @PostMapping("/queryByIds")
    @Action(name = "根据Id集合查询指定组织数据模型数据", value = OrgActionKeyDict.QUERY_BY_IDS)
    public Response<List<? extends BaseModel>> queryByIds(@RequestBody OrgQueryByIdsReq req) {
        List<? extends BaseModel> modelList = orgBaseModelAppService.queryByIds(req);
        return Response.ok(modelList);
    }

    @ApiOperation("根据条件查询指定组织数据模型单条数据")
    @PostMapping("/queryByUk")
    @Action(name = "根据条件查询指定组织数据模型单条数据", value = OrgActionKeyDict.QUERY_BY_UK)
    public Response<BaseModel> queryByUk(@RequestBody OrgQueryByUkReq req) {
        BaseModel model = orgBaseModelAppService.queryByUk(req);
        return Response.ok(model);
    }

    @ApiOperation("根据条件查询指定组织数据模型多条数据")
    @PostMapping("/queryList")
    @Action(name = "根据条件查询指定组织数据模型多条数据", value = OrgActionKeyDict.QUERY_LIST)
    public Response<List<? extends BaseModel>> queryList(@RequestBody OrgQueryByUkReq req) {
        List<? extends BaseModel> modelList = orgBaseModelAppService.queryList(req);
        return Response.ok(modelList);
    }
}

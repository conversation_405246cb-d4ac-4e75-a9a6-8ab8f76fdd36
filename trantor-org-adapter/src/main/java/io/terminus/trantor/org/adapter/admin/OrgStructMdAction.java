package io.terminus.trantor.org.adapter.admin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.domain.service.OrgStructMdService;
import io.terminus.trantor.org.spi.model.dto.*;
import io.terminus.trantor.org.spi.model.dto.attr.OrgBusinessTypeDTO;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "行政组织架构管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/org-struct-md")
public class OrgStructMdAction {

    private final OrgStructMdService orgStructMdService;

    @ApiOperation("组织树查询下级")
    @PostMapping("/findByPid")
    @Action(name = "组织树查询下级", value = "ORG_STRUCT_MD_FIND_BY_PID_ACTION")
    public Response<List<OrgStructMdTreeDTO>> findByPid(@RequestBody OrgStructMdQueryDTO request) {
        return Response.ok(orgStructMdService.findByPid(request));
    }

    @ApiOperation("根据名称查询组织树路径")
    @PostMapping("/findTreeByName")
    @Action(name = "根据名称查询组织树路径", value = "ORG_STRUCT_MD_FIND_TREE_BY_NAME_ACTION")
    public Response<List<OrgStructMdTreePathDTO>> findTreeByName(@RequestBody OrgStructMdQueryTreeDTO request) {
        return Response.ok(orgStructMdService.findTreeByName(request));
    }

    @ApiOperation("根据id查询组织树路径")
    @PostMapping("/findTreeById")
    @Action(name = "根据id查询组织树路径", value = "ORG_STRUCT_MD_FIND_TREE_BY_ID_ACTION")
    public Response<TreePathDTO> findTreeById(@RequestBody IdRequest request) {
        return Response.ok(orgStructMdService.findTreeById(request));
    }

    @ApiOperation("新组织搜索")
    @PostMapping("/search")
    @Action(name = "新组织搜索", value = "ORG_STRUCT_MD_SEARCH_ACTION")
    public Response<List<OrgStructMdDetailDTO>> search(@RequestBody OrgStructMdQueryTreeDTO request) {
        return Response.ok(orgStructMdService.search(request));
    }

    @ApiOperation("查询组织类型")
    @PostMapping("/findType")
    @Action(name = "查询组织类型列表", value = "ORG_STRUCT_MD_FIND_TYPE_ACTION")
    public Response<List<OrgBusinessTypeDTO>> findType(@RequestBody OrgDimensionDTO request) {
        return Response.ok(orgStructMdService.findType(request));
    }

    @ApiOperation("查询EHR组织单元详情")
    @PostMapping("/detail")
    @Action(name = "查询EHR组织单元详情", value = "ORG_STRUCT_MD_DETAIL_ACTION")
    public Response<OrgStructMdDetailWithTypeDTO> detail(@RequestBody OrgStructMdHistoryDetialDTO request) {
        return Response.ok(orgStructMdService.detail(request));
    }

    @ApiOperation("保存EHR组织单元")
    @PostMapping("/save")
    @Action(name = "保存EHR组织单元", value = "ORG_STRUCT_MD_SAVE_ACTION")
    public Response<OrgStructMdSaveDTO> save(@RequestBody OrgStructMdSaveDTO request) {
        return Response.ok(orgStructMdService.save(request));
    }

    @ApiOperation("删除EHR组织单元")
    @PostMapping("/delete")
    @Action(name = "删除EHR组织单元", value = "ORG_STRUCT_MD_DELETE_ACTION")
    public Response<Void> delete(@RequestBody IdRequest request) {
        orgStructMdService.delete(request);
        return Response.ok();
    }

    @ApiOperation("启动EHR组织单元")
    @PostMapping("/enabled")
    @Action(name = "启动EHR组织单元", value = "ORG_STRUCT_MD_ENABLED_ACTION")
    public Response<Void> enabled(@RequestBody IdRequest request) {
        orgStructMdService.enabled(request);
        return Response.ok();
    }

    @ApiOperation("停用组织EHR组织单元")
    @PostMapping("/disabled")
    @Action(name = "停用组织EHR组织单元", value = "ORG_STRUCT_MD_DISABLED_ACTION")
    public Response<Void> disabled(@RequestBody IdRequest request) {
        orgStructMdService.disabled(request);
        return Response.ok();
    }

    @ApiOperation("组织历史版本查看")
    @PostMapping("/history-detail")
    @Action(name = "EHR组织历史版本查看", value = "ORG_STRUCT_MD_HISTORY_DETAIL_ACTION")
    public Response<List<OrgStructMdHistoryTreeDTO>> historyDetail(@RequestBody OrgStructMdHistoryQueryDTO request) {
        return Response.ok(orgStructMdService.historyDetail(request));
    }

    @ApiOperation("根据维度构建一个组织树")
    @PostMapping("/build-org-struct-tree")
    @Action(name = "根据维度构建一个组织树", value = "ORG_STRUCT_BUILD_TREE_ACTION")
    public Response<OrgStructMdDetailDTO> buildOrgStructTree(@RequestBody OrgStructBuildTreeQueryDTO request) {
        return Response.ok(orgStructMdService.buildOrgStructTree(request));
    }

    @ApiOperation("查询当前组织的公司组织(组织新建使用)")
    @PostMapping("/query-current-org-com-org")
    @Action(name = "查询当前组织的公司组织(组织新建使用)", value = "ORG_STRUCT_QUERY_CURRENT_COM_ORG_ACTION")
    public Response<OrgStructMdDetailDTO> queryCurrentOrgComOrg(@RequestBody IdRequest request) {
        return Response.ok(orgStructMdService.queryCurrentOrgComOrg(request));
    }
}

package io.terminus.trantor.org.adapter.admin;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.response.Response;
import io.terminus.common.runtime.context.RequestContext;
import io.terminus.trantor.org.domain.integration.dingtalk.service.DingTalkSyncService;
import io.terminus.trantor.org.spi.model.dto.IdDTO;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.dto.Portal;
import io.terminus.trantor2.common.user.User;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @author: 张博
 * @date: 2024-09-03 15:55
 */
@Api(tags = "钉钉数据同步")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/dingtalk")
public class DingTalkSyncAction {

    private final DingTalkSyncService dingTalkSyncService;
    ExecutorService executor = Executors.newFixedThreadPool(1, new ThreadFactoryBuilder().setNameFormat("dingtalk-sync-pool-%d").build());


    @ApiOperation("钉钉同步组织架构")
    @GetMapping("/sync-org")
    @Action(name = "钉钉同步组织架构", value = "ORG_DING_TALK_SYNC_ORG_ACTION")
    public Response<Void> syncOrg(@RequestBody IdDTO request) {
        String appKey = RequestContext.getAppKey();
        Long originOrgId = RequestContext.getOriginOrgId();
        Long tenantId = RequestContext.getTenantId();
        String portalCode = TrantorContext.getPortalCode();
        User currentUser = TrantorContext.getCurrentUser();
        Portal currentPortal = TrantorContext.getCurrentPortal();
        executor.execute(() -> {
            RequestContext.setAppKey(appKey);
            RequestContext.setOriginOrgId(originOrgId);
            RequestContext.setTenantId(tenantId);
            TrantorContext.init();
            TrantorContext.setTeamId(tenantId);
            TrantorContext.setOriginOrgId(originOrgId);
            TrantorContext.setPortalCode(portalCode);
            TrantorContext.setCurrentUser(currentUser);
            TrantorContext.setCurrentPortal(currentPortal);
            dingTalkSyncService.syncOrg();
        });
        return Response.ok();
    }


    @ApiOperation("钉钉同步员工")
    @GetMapping("/sync-employee")
    @Action(name = "钉钉同步员工", value = "ORG_DING_TALK_SYNC_EMPLOYEE_ACTION")
    public Response<Void> syncEmployee(@RequestBody IdDTO request) {
        String appKey = RequestContext.getAppKey();
        Long originOrgId = RequestContext.getOriginOrgId();
        Long tenantId = RequestContext.getTenantId();
        String portalCode = TrantorContext.getPortalCode();
        User currentUser = TrantorContext.getCurrentUser();
        Portal currentPortal = TrantorContext.getCurrentPortal();
        executor.execute(() -> {
            RequestContext.setAppKey(appKey);
            RequestContext.setOriginOrgId(originOrgId);
            RequestContext.setTenantId(tenantId);
            TrantorContext.init();
            TrantorContext.setTeamId(tenantId);
            TrantorContext.setOriginOrgId(originOrgId);
            TrantorContext.setPortalCode(portalCode);
            TrantorContext.setCurrentUser(currentUser);
            TrantorContext.setCurrentPortal(currentPortal);
            dingTalkSyncService.syncEmployee();
        });
        return Response.ok();
    }
}

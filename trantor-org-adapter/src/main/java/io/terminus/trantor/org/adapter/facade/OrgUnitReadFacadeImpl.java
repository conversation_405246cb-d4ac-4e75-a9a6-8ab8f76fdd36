package io.terminus.trantor.org.adapter.facade;

import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.api.facade.OrgUnitReadFacade;
import io.terminus.trantor.org.app.service.OrgUnitAppService;
import io.terminus.trantor.org.spi.model.dto.EmployeeQueryDTO;
import io.terminus.trantor.org.spi.model.dto.OrgUnitDTO;
import io.terminus.trantor.org.spi.model.dto.OrgUnitInfoDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-07-19 11:02
 */
@RestController
@RequiredArgsConstructor
public class OrgUnitReadFacadeImpl implements OrgUnitReadFacade {

    private final OrgUnitAppService orgUnitAppService;

    @Override
    public Response<List<OrgUnitDTO>> queryOrgUnitByEmployeeCode(EmployeeQueryDTO request) {
        return Response.ok(orgUnitAppService.queryOrgUnitByEmployeeCode(request));
    }

    @Override
    public Response<OrgUnitInfoDTO> queryOrgUnitById(IdRequest request) {
        return Response.ok(orgUnitAppService.queryOrgUnitById(request));
    }
}

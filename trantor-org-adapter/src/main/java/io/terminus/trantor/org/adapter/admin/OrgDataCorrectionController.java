package io.terminus.trantor.org.adapter.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.request.IdRequest;
import io.terminus.trantor.org.domain.service.OrgStructMdService;
import io.terminus.trantor.org.infrastructure.gateway.OrgMdGateWay;
import io.terminus.trantor.org.infrastructure.repo.OrgBusinessTypeRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgStructMdRepo;
import io.terminus.trantor.org.spi.model.dto.attr.GenAttrDTO;
import io.terminus.trantor.org.spi.model.po.OrgBusinessTypePO;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2024-08-12 14:50
 */
@Api(tags = "数据修正")
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/data/correction")
public class OrgDataCorrectionController {

    private final OrgStructMdRepo orgStructMdRepo;
    private final OrgStructMdService orgStructMdService;
    private final OrgBusinessTypeRepo orgBusinessTypeRepo;
    private final OrgMdGateWay orgMdGateWay;

    @ApiOperation("组织属性修正")
    @PostMapping("/org-attr-correction")
    public void orgAttrCorrection(@RequestBody Map<String, String> attrMap) {
        QueryWrapper<OrgStructMdPO> queryWrapper = new QueryWrapper<>();
        List<OrgStructMdPO> orgStructMdPOList = orgStructMdRepo.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(orgStructMdPOList)) {
            return;
        }
        for (OrgStructMdPO orgStructMdPO : orgStructMdPOList) {
            Map<String, Object> objectMap = JSONObject.parseObject(JSON.toJSONString(orgStructMdPO), Map.class);
            List<String> orgBusinessTypeCodes = orgStructMdPO.getOrgBusinessTypeCodes();
            if (CollectionUtils.isEmpty(orgBusinessTypeCodes)) {
                continue;
            }
            try {
                Class<? extends OrgStructMdPO> structMdPOClass = orgStructMdPO.getClass();
                for (Map.Entry<String, String> entry : attrMap.entrySet()) {
                    String source = entry.getKey();
                    String target = entry.getValue();
                    Field field = structMdPOClass.getDeclaredField(target);
                    field.setAccessible(Boolean.TRUE);
                    field.set(orgStructMdPO, objectMap.get(source));
                }
                orgStructMdRepo.updateById(orgStructMdPO);
                log.info("当前操作的组织:{}", JSON.toJSONString(orgStructMdPO));
            } catch (Exception e) {
                log.error("数据修正失败", e);
            }
        }
    }

    @ApiOperation("组织路径修复")
    @GetMapping("/org-path-fix")
    public void orgPathFix() {
        QueryWrapper<OrgStructMdPO> queryWrapper = new QueryWrapper<>();
        List<OrgStructMdPO> orgStructMdPOList = orgStructMdRepo.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(orgStructMdPOList)) {
            return;
        }
        for (OrgStructMdPO orgStructMdPO : orgStructMdPOList) {
            List<OrgStructMdPO> orgStructMdPOS = orgStructMdService.queryPath(orgStructMdPO);
            orgStructMdPO.setPath(orgStructMdPOS.stream().map(OrgStructMdPO::getId).collect(Collectors.toList()));
            orgStructMdRepo.updateById(orgStructMdPO);
        }
    }

    @ApiOperation("组织属性修正")
    @GetMapping("/org-attr-fix")
    public void orgAttrFix() {
        List<String> list = new ArrayList<>();
        list.add("def1");
        list.add("def2");
        list.add("def3");
        list.add("def4");
        list.add("def5");
        list.add("def6");
        list.add("def7");
        list.add("def8");
        list.add("def9");
        list.add("def10");
        list.add("def11");
        list.add("def12");
        list.add("def13");
        list.add("def14");
        list.add("def15");
        list.add("def16");
        list.add("def17");
        list.add("def18");
        list.add("def19");
        list.add("def20");
        QueryWrapper<OrgStructMdPO> queryWrapper = new QueryWrapper<>();
        List<OrgStructMdPO> orgStructMdPOList = orgStructMdRepo.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(orgStructMdPOList)) {
            return;
        }
        Set<Long> orgBusinessIdSet = orgStructMdPOList.stream().map(OrgStructMdPO::getOrgBusinessTypeIds).flatMap(Collection::stream).collect(Collectors.toSet());
        List<OrgBusinessTypePO> orgBusinessTypePOS = orgBusinessTypeRepo.selectBatchIds(orgBusinessIdSet);
        Set<Long> attrGroupIdSet = orgBusinessTypePOS.stream().map(OrgBusinessTypePO::getAttrGroupId).collect(Collectors.toSet());
        Map<Long, List<GenAttrDTO>> attrGroupAttrMap = new HashMap<>();
        for (Long attrGroupId : attrGroupIdSet) {
            List<GenAttrDTO> genAttrDTOList = orgMdGateWay.findAttrByAttrGroupId(new IdRequest(attrGroupId));
            if (!CollectionUtils.isEmpty(genAttrDTOList)) {
                attrGroupAttrMap.put(attrGroupId, genAttrDTOList);
            }
        }
        for (OrgStructMdPO orgStructMdPO : orgStructMdPOList) {
            List<Long> orgBusinessTypeIds = orgStructMdPO.getOrgBusinessTypeIds();
            if (CollectionUtils.isEmpty(orgBusinessTypeIds)) {
                continue;
            }
            List<String> defList = new ArrayList<>();
            for (Long orgBusinessTypeId : orgBusinessTypeIds) {
                List<GenAttrDTO> genAttrDTOList = attrGroupAttrMap.get(orgBusinessTypeId);
                if (CollectionUtils.isEmpty(genAttrDTOList)) {
                    continue;
                }
                for (GenAttrDTO genAttrDTO : genAttrDTOList) {
                    defList.add(genAttrDTO.getAttrField());
                }
            }
            if (CollectionUtils.isEmpty(defList)) {
                continue;
            }
            LambdaUpdateWrapper<OrgStructMdPO> queryWrapper1 = new LambdaUpdateWrapper<>();
            for (String s : list) {
                if (!defList.contains(s)) {
                    if (s.equals("def1")) {
                        queryWrapper1.set(OrgStructMdPO::getDef1, null);
                    }
                    if (s.equals("def2")) {
                        queryWrapper1.set(OrgStructMdPO::getDef2, null);
                    }
                    if (s.equals("def3")) {
                        queryWrapper1.set(OrgStructMdPO::getDef3, null);
                    }
                    if (s.equals("def4")) {
                        queryWrapper1.set(OrgStructMdPO::getDef4, null);
                    }
                    if (s.equals("def5")) {
                        queryWrapper1.set(OrgStructMdPO::getDef5, null);
                    }
                    if (s.equals("def6")) {
                        queryWrapper1.set(OrgStructMdPO::getDef6, null);
                    }
                    if (s.equals("def7")) {
                        queryWrapper1.set(OrgStructMdPO::getDef7, null);
                    }
                    if (s.equals("def8")) {
                        queryWrapper1.set(OrgStructMdPO::getDef8, null);
                    }
                    if (s.equals("def9")) {
                        queryWrapper1.set(OrgStructMdPO::getDef9, null);
                    }
                    if (s.equals("def10")) {
                        queryWrapper1.set(OrgStructMdPO::getDef10, null);
                    }
                    if (s.equals("def11")) {
                        queryWrapper1.set(OrgStructMdPO::getDef11, null);
                    }
                    if (s.equals("def12")) {
                        queryWrapper1.set(OrgStructMdPO::getDef12, null);
                    }
                    if (s.equals("def13")) {
                        queryWrapper1.set(OrgStructMdPO::getDef13, null);
                    }
                    if (s.equals("def14")) {
                        queryWrapper1.set(OrgStructMdPO::getDef14, null);
                    }
                    if (s.equals("def15")) {
                        queryWrapper1.set(OrgStructMdPO::getDef15, null);
                    }
                    if (s.equals("def16")) {
                        queryWrapper1.set(OrgStructMdPO::getDef16, null);
                    }
                    if (s.equals("def17")) {
                        queryWrapper1.set(OrgStructMdPO::getDef17, null);
                    }
                    if (s.equals("def18")) {
                        queryWrapper1.set(OrgStructMdPO::getDef18, null);
                    }
                    if (s.equals("def19")) {
                        queryWrapper1.set(OrgStructMdPO::getDef19, null);
                    }
                    if (s.equals("def20")) {
                        queryWrapper1.set(OrgStructMdPO::getDef20, null);
                    }
                }
            }
            queryWrapper1.eq(OrgStructMdPO::getId, orgStructMdPO.getId());
            orgStructMdRepo.update(null, queryWrapper1);
        }
    }
}

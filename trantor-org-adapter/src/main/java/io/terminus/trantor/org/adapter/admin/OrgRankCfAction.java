package io.terminus.trantor.org.adapter.admin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.domain.service.OrgRankCfService;
import io.terminus.trantor.org.spi.model.dto.OrgRankCfDTO;
import io.terminus.trantor.org.spi.model.dto.OrgRankCfQueryDTO;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "职级管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/rank-cf")
public class OrgRankCfAction {

    private final OrgRankCfService orgRankCfService;
    @ApiOperation("查询职级列表")
    @PostMapping("/list")
    @Action(name = "查询职级列表", value = "ORG_RANK_CF_FIND_ALL_ACTION")
    public Response<List<OrgRankCfDTO>> findAll(@RequestBody OrgRankCfQueryDTO request) {
        return Response.ok(orgRankCfService.findAll(request));
    }

}

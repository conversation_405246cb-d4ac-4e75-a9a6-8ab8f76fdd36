package io.terminus.trantor.org.adapter.admin;

import io.swagger.annotations.Api;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.app.service.OrgPermissionAppService;
import io.terminus.trantor.org.spi.model.dto.IdDTO;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: 张博
 * @date: 2024-09-05 13:32
 */
@Api(tags = "组织权限")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/org-permission")
public class OrgPermissionAction {

    private final OrgPermissionAppService orgPermissionAppService;

    @GetMapping("/employee-org")
    @Action(name = "权限-员工所在组织", value = "ORG_PERMISSION_EMPLOYEE_ORG_ACTION")
    public Response<List<Long>> queryEmployeeOrg(IdDTO request) {
        return Response.ok(orgPermissionAppService.queryEmployeeOrg());
    }

    @GetMapping("/employee-org-and-child-org")
    @Action(name = "权限-员工所在组织及子组织", value = "ORG_PERMISSION_EMPLOYEE_ORG_AND_CHILD_ORG_ACTION")
    public Response<List<Long>> queryEmployeeOrgAndChildOrg(IdDTO request) {
        return Response.ok(orgPermissionAppService.queryEmployeeOrgAndChildOrg());
    }

    @GetMapping("/employee-jurisdiction-org")
    @Action(name = "权限-员工管辖的组织", value = "ORG_PERMISSION_EMPLOYEE_JURISDICTION_ORG_ACTION")
    public Response<List<Long>> queryEmployeeJurisdictionOrg(IdDTO request) {
        return Response.ok(orgPermissionAppService.queryEmployeeJurisdictionOrg());
    }

    @GetMapping("/employee-jurisdiction-org-and-child-org")
    @Action(name = "权限-员工管辖的组织及子组织", value = "ORG_PERMISSION_EMPLOYEE_JURISDICTION_ORG_AND_CHILD_ORG_ACTION")
    public Response<List<Long>> queryEmployeeJurisdictionOrgAndChildOrg(IdDTO request) {
        return Response.ok(orgPermissionAppService.queryEmployeeJurisdictionOrgAndChildOrg());
    }
}

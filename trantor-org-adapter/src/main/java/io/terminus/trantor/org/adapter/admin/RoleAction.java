package io.terminus.trantor.org.adapter.admin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.app.service.RoleAppService;
import io.terminus.trantor.org.spi.model.dto.PageDTO;
import io.terminus.trantor.org.spi.model.dto.RoleDTO;
import io.terminus.trantor.org.spi.model.dto.RolePageDTO;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * @author: 张博
 * @date: 2023-09-21 10:08
 */
@Api(tags = "角色管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/role")
public class RoleAction {

    private final RoleAppService roleAppService;

    @ApiOperation("前端获取角色列表")
    @GetMapping("/org-front-role-page")
    @Action(name = "前端获取角色列表", value = "ORG_ORG_FRONT_ROLE_PAGE_ACTION")
    public Response<Paging<RoleDTO>> frontRolePaging(@RequestBody RolePageDTO request) {
        return Response.ok(roleAppService.frontRolePaging(request));
    }

    @ApiOperation("获取角色列表")
    @GetMapping("/org-role-page")
    @Action(name = "获取角色列表", value = "ORG_ORG_ROLE_PAGE_ACTION")
    public Response<Paging<RoleDTO>> rolePaging(@RequestBody PageDTO request) {
        return Response.ok(roleAppService.rolePaging(request));
    }

    @ApiOperation("根据ID查询角色")
    @PostMapping("/query-role-by-id")
    @Action(name = "根据ID查询角色", value = "ORG_QUERY_ROLE_BY_ID_ACTION")
    public Response<RoleDTO> queryEmployeeRoleList(@RequestBody IdRequest request) {
        return Response.ok(roleAppService.queryById(request.getId()));
    }
}

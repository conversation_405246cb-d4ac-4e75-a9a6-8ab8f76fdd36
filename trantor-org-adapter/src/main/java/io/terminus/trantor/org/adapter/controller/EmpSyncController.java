package io.terminus.trantor.org.adapter.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.trantor.org.spi.model.req.OrgSyncDTO;
import io.terminus.trantor2.common.dto.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @time 2025/8/27 13:39
 */
@Api(tags = "员工同步晶澳数据")
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/api/tsrm/md/emp/sync")
public class EmpSyncController {


    @ApiOperation("VEND-同步新增")
    @PostMapping(value = "add")
    public Response<Void> add(@RequestBody OrgSyncDTO orgSyncDTO) {

    }

    @ApiOperation("VEND-同步更新")
    @PostMapping(value = "update")
    public Response<Void> update(@RequestBody OrgSyncDTO orgSyncDTO) {

    }

    @ApiOperation("VEND-同步删除")
    @PostMapping(value = "delete")
    public Response<Void> delete(@RequestBody OrgSyncDTO orgSyncDTO) {

    }

}

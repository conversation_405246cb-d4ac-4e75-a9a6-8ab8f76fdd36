package io.terminus.trantor.org.adapter.admin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.app.service.OrgUnitAppService;
import io.terminus.trantor.org.spi.model.OrgQueryDTO;
import io.terminus.trantor.org.spi.model.dto.*;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-02-28 16:30
 */
@Api(tags = "组织单元管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/org-unit")
public class OrgUnitAction {
    private final OrgUnitAppService orgUnitAppService;

    @ApiOperation("按业务类型分组统计")
    @GetMapping("/count-by-biz-type")
    @Action(name = "按业务类型分组统计", value = "ORG_ORG_UNIT_COUNT_BY_BIZ_TYPE_ACTION")
    public Response<List<OrgBizTypeCountDTO>> queryListAndCount(@RequestBody IdRequest request) {
        return Response.ok(orgUnitAppService.queryListAndCount());
    }

    @ApiOperation("保存组织单元")
    @PostMapping("/save")
    @Action(name = "保存组织单元", value = "ORG_ORG_UNIT_SAVE_ACTION")
    public Response<OrgUnitDTO> save(@RequestBody OrgUnitSaveDTO request) {
        return Response.ok(orgUnitAppService.save(request));
    }

    @ApiOperation("根据pid查询下级组织节点")
    @PostMapping("/query-by-pid")
    @Action(name = "根据pid查询下级组织节点", value = "ORG_ORG_UNIT_QUERY_PID_ACTION")
    public Response<List<OrgUnitDTO>> queryByPid(@RequestBody OrgUnitQueryDTO request) {
        return Response.ok(orgUnitAppService.queryByPid(request));
    }

    @ApiOperation("查询组织单元分页")
    @PostMapping("/page")
    @Action(name = "查询组织单元分页", value = "ORG_ORG_UNIT_PAGE_ACTION")
    public Response<Paging<OrgUnitDTO>> paging(@RequestBody OrgUnitPageDTO request) {
        return Response.ok(orgUnitAppService.paging(request));
    }

    @ApiOperation("组织上级单元分页")
    @PostMapping("/parent-page")
    @Action(name = "组织上级单元分页", value = "ORG_ORG_UNIT_PARENT_PAGE_ACTION")
    public Response<Paging<OrgUnitDTO>> parentPaging(@RequestBody OrgUnitPageDTO request) {
        return Response.ok(orgUnitAppService.parentPaging(request));
    }

    @ApiOperation("查询组织单元详情")
    @PostMapping("/detail")
    @Action(name = "查询组织单元详情", value = "ORG_ORG_UNIT_QUERY_DETAIL_ACTION")
    public Response<OrgUnitDTO> queryDetail(@RequestBody IdRequest request) {
        return Response.ok(orgUnitAppService.queryDetail(request.getId()));
    }

    @ApiOperation("删除组织单元")
    @PostMapping("/delete")
    @Action(name = "删除组织单元", value = "ORG_ORG_UNIT_DELETE_ACTION")
    public Response<Void> delete(@RequestBody IdRequest request) {
        orgUnitAppService.delete(request.getId());
        return Response.ok();
    }

    @ApiOperation("启用组织单元")
    @PostMapping("/enable")
    @Action(name = "启用组织单元", value = "ORG_ORG_UNIT_ENABLE_ACTION")
    public Response<Void> enable(@RequestBody IdRequest request) {
        orgUnitAppService.enable(request.getId());
        return Response.ok();
    }

    @ApiOperation("禁用组织单元")
    @PostMapping("/disable")
    @Action(name = "禁用组织单元", value = "ORG_ORG_UNIT_DISABLE_ACTION")
    public Response<Void> disable(@RequestBody IdRequest request) {
        orgUnitAppService.disable(request.getId());
        return Response.ok();
    }

    @ApiOperation("组织单元搜索")
    @PostMapping("/search")
    @Action(name = "组织单元搜索", value = "ORG_ORG_UNIT_SEARCH_ACTION")
    public Response<List<OrgSearchListDTO>> search(@RequestBody OrgSearchDTO request) {
        return Response.ok(orgUnitAppService.search(request));
    }

    @ApiOperation("根据条件查询所有的组织")
    @PostMapping("/find-all")
    @Action(name = "根据条件查询所有的组织", value = "ORG_ORG_UNIT_FIND_ALL_ACTION")
    public Response<List<OrgUnitDTO>> findAll(@RequestBody OrgQueryDTO request) {
        return Response.ok(orgUnitAppService.findAll(request));
    }
}

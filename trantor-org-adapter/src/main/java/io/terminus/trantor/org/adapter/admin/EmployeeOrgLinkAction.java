package io.terminus.trantor.org.adapter.admin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.domain.service.EmployeeOrgLinkService;
import io.terminus.trantor.org.spi.model.dto.EmployeeOrgLinkDTO;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "组织员工关联")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/employee-org-link")
public class EmployeeOrgLinkAction {

    private final EmployeeOrgLinkService employeeOrgLinkService;

    @ApiOperation("保存员工组织关联关系")
    @PostMapping("/save")
    @Action(name = "保存员工组织关联关系", value = "ORG_ADMIN_EMPLOYEE_ORG_LINK_SAVE_ACTION")
    public Response<Void> save(@RequestBody EmployeeOrgLinkDTO request) {
        employeeOrgLinkService.save(request);
        return Response.ok();
    }

    @ApiOperation("删除员工组织关联关系")
    @PostMapping("/delete")
    @Action(name = "删除员工组织关联关系", value = "ORG_ADMIN_EMPLOYEE_ORG_LINK_DELETE_ACTION")
    public Response<Void> delete(@RequestBody IdRequest request) {
        employeeOrgLinkService.delete(request);
        return Response.ok();
    }

}

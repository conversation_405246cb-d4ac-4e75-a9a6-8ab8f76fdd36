package io.terminus.trantor.org.adapter.facade;

import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.api.facade.RankReadFacade;
import io.terminus.trantor.org.app.service.OrgRankAppService;
import io.terminus.trantor.org.spi.model.dto.OrgRankCfDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class RankReadFacadeImpl implements RankReadFacade {
    private final OrgRankAppService orgRankAppService;

    @Override
    public Response<OrgRankCfDTO> queryRankById(IdRequest request) {
        return Response.ok(orgRankAppService.queryRankById(request));
    }
}

package io.terminus.trantor.org.adapter.admin;

import io.swagger.annotations.Api;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.app.service.OrgRelationAppService;
import io.terminus.trantor.org.spi.model.dto.OrgRelationRuleCfDTO;
import io.terminus.trantor.org.spi.model.dto.OrgRelationSaveDTO;
import io.terminus.trantor.org.spi.model.dto.attr.OrgBusinessTypeDTO;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Api(tags = "组织关联")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/relation-cf")
public class OrgRelationCfAction {

    private final OrgRelationAppService orgRelationAppService;

    @PostMapping("/save")
    @Action(name = "组织关联保存", value = "ORG_RELATION_CF_SAVE_ACTION")
    public Response<Void> save(@RequestBody OrgRelationSaveDTO request) {
        orgRelationAppService.save(request);
        return Response.ok();
    }

    @PostMapping("/enabled")
    @Action(name = "组织关联启动", value = "ORG_RELATION_CF_ENABLE_ACTION")
    public Response<Void> enabled(@RequestBody IdRequest request) {
        orgRelationAppService.enabled(request);
        return Response.ok();
    }


    @Action(name = "组织关联批量启动", value = "ORG_RELATION_CF_BATCH_ENABLE_ACTION")
    public Response<Void> batchEnabled(@RequestBody IdsRequest request) {
        orgRelationAppService.batchEnabled(request);
        return Response.ok();
    }

    @Action(name = "根据维度Id查询源组织", value = "ORG_BUSINESS_TYPE_QUERY_ACTION")
    public Response<List<OrgBusinessTypeDTO>> queryByDimensionId(@RequestBody IdRequest request) {
        return Response.ok(orgRelationAppService.queryByDimensionId(request));
    }

    @Action(name = "组织关联规则保存", value = "ORG_RELATION_RULE_SAVE_ACTION")
    public Response<Void> OrgRelationRuleSave(@RequestBody OrgRelationRuleCfDTO request) {
        orgRelationAppService.OrgRelationRuleSave(request);
        return Response.ok();
    }
}

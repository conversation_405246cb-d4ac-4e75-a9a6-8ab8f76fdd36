package io.terminus.trantor.org.adapter.admin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.domain.service.OrgDimensionCfService;
import io.terminus.trantor.org.spi.model.dto.IdDTO;
import io.terminus.trantor.org.spi.model.dto.OrgDimensionDTO;
import io.terminus.trantor2.doc.annotation.Action;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Api(tags = "组织维度管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/trantor/org/org-dimension-cf")
public class OrgDimensionCfAction {
    private final OrgDimensionCfService orgDimensionCfService;


    @ApiOperation("启动组织维度")
    @PostMapping("/enable")
    @Action(name = "启动组织维度", value = "ORG_DIMENSION_CF_ENABLE_ACTION")
    public Response<Void> enable(@RequestBody IdsRequest request) {
        orgDimensionCfService.enable(request);
        return Response.ok();
    }

    @ApiOperation("查询启用的组织维度列表")
    @PostMapping("/query-enable-dimension-list")
    @Action(name = "查询启用的组织维度列表", value = "ORG_DIMENSION_QUERY_ENABLE_LIST_ACTION")
    public Response<List<OrgDimensionDTO>> queryEnableList(@RequestBody IdDTO idDTO) {
        return Response.ok(orgDimensionCfService.queryEnableList());
    }
}

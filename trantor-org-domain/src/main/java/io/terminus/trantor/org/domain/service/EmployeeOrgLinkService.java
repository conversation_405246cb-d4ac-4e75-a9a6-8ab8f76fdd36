package io.terminus.trantor.org.domain.service;

import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.request.IdRequest;
import io.terminus.trantor.org.domain.constant.OrgConstants;
import io.terminus.trantor.org.infrastructure.repo.EmployeeOrgLinkRepo;
import io.terminus.trantor.org.spi.model.dto.EmployeeOrgLinkDTO;
import io.terminus.trantor.org.spi.model.po.EmployeeOrgLinkPO;
import io.terminus.trantor.org.spi.msg.OrgMsg;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @author: 张博
 * @date: 2023-11-24 10:40
 */
@Service
@RequiredArgsConstructor
public class EmployeeOrgLinkService {

    private final EmployeeOrgLinkRepo employeeOrgLinkRepo;
    private final RedisTemplate<String, Object> orgRedisTemplate;

    public void save(EmployeeOrgLinkDTO request) {
        if (Objects.isNull(request.getId())) {
            EmployeeOrgLinkPO employeeOrgLinkPO = new EmployeeOrgLinkPO();
            employeeOrgLinkPO.setOrgUnitId(request.getOrgUnitId());
            employeeOrgLinkPO.setIdentityId(request.getIdentityId());
            employeeOrgLinkPO.setEmployeeId(request.getEmployeeId());
            employeeOrgLinkPO.setIsMainOrg(request.getIsMainOrg());
            // 校验重复
            EmployeeOrgLinkPO employeeOrgLinkPO1 = employeeOrgLinkRepo.queryByEmployeeAndOrgAndRankId(request.getEmployeeId(), request.getOrgUnitId(), request.getIdentityId());
            if (Objects.nonNull(employeeOrgLinkPO1)) {
                throw new BusinessException(OrgMsg.ORG_STRUCT_MEMBER_IS_EXIST);
            }
            employeeOrgLinkRepo.insert(employeeOrgLinkPO);
        } else {
            EmployeeOrgLinkPO employeeOrgLinkPO = new EmployeeOrgLinkPO();
            employeeOrgLinkPO.setId(request.getId());
            employeeOrgLinkPO.setEmployeeId(request.getEmployeeId());
            employeeOrgLinkPO.setIsMainOrg(request.getIsMainOrg());
            employeeOrgLinkPO.setOrgUnitId(request.getOrgUnitId());
            employeeOrgLinkPO.setIdentityId(request.getIdentityId());
            employeeOrgLinkRepo.updateById(employeeOrgLinkPO);
        }

        orgRedisTemplate.delete(OrgConstants.ORG_REC_USER_CHARGE_ORG_PREFIX + request.getEmployeeId());
    }

    public void delete(IdRequest request) {
        EmployeeOrgLinkPO employeeOrgLinkPO = employeeOrgLinkRepo.selectById(request.getId());
        if (Objects.nonNull(employeeOrgLinkPO)) {
            orgRedisTemplate.delete(OrgConstants.ORG_REC_USER_CHARGE_ORG_PREFIX + employeeOrgLinkPO.getEmployeeId());
        }
        employeeOrgLinkRepo.deleteById(request.getId());
    }
}

package io.terminus.trantor.org.domain.converter;

import io.terminus.trantor.org.spi.model.dto.OrgSwitchModelSaveDTO;
import io.terminus.trantor.org.spi.model.po.OrgSwitchModelCfPO;
import org.mapstruct.Mapper;

/**
 * @author: 张博
 * @date: 2023-12-29 15:28
 */
@Mapper(componentModel = "spring")
public interface OrgSwitchConverter {

    OrgSwitchModelCfPO converter(OrgSwitchModelSaveDTO orgSwitchModelSaveDTO);
}

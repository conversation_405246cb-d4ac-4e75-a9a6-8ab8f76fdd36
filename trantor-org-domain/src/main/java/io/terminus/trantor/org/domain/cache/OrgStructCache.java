package io.terminus.trantor.org.domain.cache;

import io.terminus.common.api.request.IdsRequest;
import io.terminus.common.cache.TerminusCache;
import io.terminus.common.cache.support.TerminusCacheManager;
import io.terminus.erp.strategy.StrategyLoader;
import io.terminus.trantor.org.infrastructure.gateway.OrgMdGateWay;
import io.terminus.trantor.org.infrastructure.repo.OrgBusinessTypeRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgStructMdRepo;
import io.terminus.trantor.org.spi.model.dto.attr.GenAttrDTO;
import io.terminus.trantor.org.spi.model.po.OrgBusinessTypePO;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import io.terminus.trantor.org.spi.strategy.attr.AttrTypeTransStrategy;
import io.terminus.trantor.org.spi.utils.OrgFiledUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2024-02-29 17:54
 */
@Component
@RequiredArgsConstructor
@CacheConfig(cacheNames = "DEL_ORG_STRUCT_INFO")
@Slf4j
public class OrgStructCache {

    private final OrgStructMdRepo orgStructMdRepo;
    private final TerminusCacheManager cacheManager;
    private final OrgBusinessTypeRepo orgBusinessTypeRepo;
    private final OrgMdGateWay orgMdGateWay;
    private static String CACHE_NAME = "DEL_ORG_STRUCT_INFO";


    @Cacheable(key = "#id")
    public Map<String, Object> findById(Long id) {
        OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectById(id);

        if (Objects.isNull(orgStructMdPO)) {
            return null;
        }
        List<OrgBusinessTypePO> orgBusinessTypePOList = orgBusinessTypeRepo.selectBatchIds(orgStructMdPO.getOrgBusinessTypeIds());
        Set<Long> attrGroupIdSet = orgBusinessTypePOList.stream().map(OrgBusinessTypePO::getAttrGroupId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<String, GenAttrDTO> genAttrDTOMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(attrGroupIdSet)) {
            List<GenAttrDTO> genAttrDTOList = orgMdGateWay.findAttrByAttrGroupIds(new IdsRequest(attrGroupIdSet));
            if (!CollectionUtils.isEmpty(genAttrDTOList)) {
                genAttrDTOMap = genAttrDTOList.stream().collect(Collectors.toMap(GenAttrDTO::getAttrField, Function.identity(), (o1, o2) -> o1));
            }
        }
        Field[] declaredFields = OrgFiledUtils.getAllFields(OrgStructMdPO.class);
        Map<String, Object> orgStructMdMap = new HashMap<>();
        for (Field field : declaredFields) {
            try {
                field.setAccessible(Boolean.TRUE);
                if (CollectionUtils.isEmpty(genAttrDTOMap)) {
                    orgStructMdMap.put(field.getName(), field.get(orgStructMdPO));
                } else {
                    GenAttrDTO genAttrDTO = genAttrDTOMap.get(field.getName());
                    if (Objects.isNull(genAttrDTO)) {
                        orgStructMdMap.put(field.getName(), field.get(orgStructMdPO));
                    } else {
                        Object fieldValue = field.get(orgStructMdPO);
                        if (fieldValue != null) {
                            AttrTypeTransStrategy attrTypeTransStrategy = StrategyLoader.load(AttrTypeTransStrategy.class, alg -> alg.match(genAttrDTO.getAttrDataType(), Objects.isNull(genAttrDTO.getAttrIsMulti()) ? Boolean.FALSE : genAttrDTO.getAttrIsMulti()));
                            orgStructMdMap.put(genAttrDTO.getAttrCode(), attrTypeTransStrategy.trans(String.valueOf(fieldValue)));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("组织详情数据转换出现异常", e);
            }
        }
        return orgStructMdMap;
    }

    @CacheEvict(key = "#id")
    public void clear(Long id) {
    }

    public List<Map<String, Object>> getByIds(List<Long> ids) {
        TerminusCache cache = cacheManager.getTerminusCache(CACHE_NAME);
        return cache.mGetList(ids, idsToLoad -> {
            //将结果返回成一个map
            Map<Long, Map<String, Object>> map = new HashMap<>();
            for (Long idToLoad : idsToLoad) {
                Map<String, Object> orgStructMd = findById(idToLoad);
                if (Objects.isNull(orgStructMd)) {
                    continue;
                }
                map.put(idToLoad, orgStructMd);
            }
            return map;
        });
    }
}

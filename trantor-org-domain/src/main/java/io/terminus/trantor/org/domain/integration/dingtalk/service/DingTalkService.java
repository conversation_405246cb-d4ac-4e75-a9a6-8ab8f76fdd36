package io.terminus.trantor.org.domain.integration.dingtalk.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkhrm_1_0.models.QueryDismissionStaffIdListResponse;
import com.aliyun.dingtalkhrm_1_0.models.QueryDismissionStaffIdListResponseBody;
import com.aliyun.tea.TeaException;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.trantor.org.domain.integration.dingtalk.constant.DingTalkConstant;
import io.terminus.trantor.org.infrastructure.repo.OrgChannelRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgSyncRecordRepo;
import io.terminus.trantor.org.spi.model.dto.integration.dingtalk.DingTalkConfigDTO;
import io.terminus.trantor.org.spi.model.po.OrgChannelPO;
import io.terminus.trantor.org.spi.model.po.OrgSyncRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2024-09-02 10:59
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DingTalkService {

    private final OrgChannelRepo orgChannelRepo;
    private final OrgSyncRecordRepo orgSyncRecordRepo;
    private final RedisTemplate<String, Object> orgRedisTemplate;

    /**
     * 获取accessToken
     *
     * @return
     */
    public String getAccessToken() {
        OrgSyncRecordPO syncRecordPO = new OrgSyncRecordPO();
        syncRecordPO.setChannelCode(DingTalkConstant.DING_TALK_CHANNEL_CODE);
        syncRecordPO.setDesc("获取accessToken");
        try {
            log.info("dingtalk get access token");
            Object cacheValue = orgRedisTemplate.opsForValue().get(DingTalkConstant.ACCESS_TOKEN_CACHE_KEY);
            if (Objects.nonNull(cacheValue)) {
                return cacheValue.toString();
            }
            OrgChannelPO orgChannelPO = orgChannelRepo.findChannelByCode(DingTalkConstant.DING_TALK_CHANNEL_CODE);
            if (Objects.isNull(orgChannelPO)) {
                throw new BusinessException("dingtalk channel not config");
            }
            DingTalkConfigDTO dingTalkConfigDTO = JSONObject.parseObject(orgChannelPO.getChannelConfig(), DingTalkConfigDTO.class);
            DingTalkClient client = new DefaultDingTalkClient(DingTalkConstant.ACCESS_TOKEN_URL);
            OapiGettokenRequest request = new OapiGettokenRequest();
            request.setAppkey(dingTalkConfigDTO.getAppKey());
            request.setAppsecret(dingTalkConfigDTO.getAppSecret());
            request.setHttpMethod("GET");
            OapiGettokenResponse response = client.execute(request);
            if (response.isSuccess()) {
                orgRedisTemplate.opsForValue().set(DingTalkConstant.ACCESS_TOKEN_CACHE_KEY, response.getAccessToken(), DingTalkConstant.ACCESS_TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);
                return response.getAccessToken();
            } else {
                syncRecordPO.setRequest(JSON.toJSONString(request));
                syncRecordPO.setResponse(JSON.toJSONString(response));
                throw new BusinessException(response.getErrmsg());
            }
        } catch (BusinessException e) {
            syncRecordPO.setErrorMsg(e.getErrorMsg());
            orgSyncRecordRepo.insert(syncRecordPO);
            log.error("dingtalk get access token error", e);
        } catch (Exception e) {
            syncRecordPO.setErrorMsg(e.getMessage());
            orgSyncRecordRepo.insert(syncRecordPO);
            log.error("dingtalk get access token error", e);
        }
        return null;
    }

    /**
     * 查询部门列表
     *
     * @param deptId
     * @return
     */
    public List<OapiV2DepartmentListsubResponse.DeptBaseResponse> queryDepartment(Long deptId) {
        OrgSyncRecordPO syncRecordPO = new OrgSyncRecordPO();
        syncRecordPO.setChannelCode(DingTalkConstant.DING_TALK_CHANNEL_CODE);
        syncRecordPO.setDesc("获取部门");
        try {
            DingTalkClient client = new DefaultDingTalkClient(DingTalkConstant.QUERY_DEPARTMENT_LIST_URL);
            OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
            req.setDeptId(deptId);
            req.setLanguage("zh_CN");
            syncRecordPO.setRequest(JSON.toJSONString(req));
            OapiV2DepartmentListsubResponse response = client.execute(req, getAccessToken());
            syncRecordPO.setResponse(JSON.toJSONString(response));
            if (response.isSuccess()) {
                return response.getResult();
            } else {
                throw new BusinessException(response.getErrmsg());
            }
        } catch (BusinessException e) {
            log.error("dingtalk query department error", e);
            syncRecordPO.setErrorMsg(e.getErrorMsg());
        } catch (Exception e) {
            log.error("dingtalk query department error", e);
            syncRecordPO.setErrorMsg(e.getMessage());
        } finally {
            orgSyncRecordRepo.insert(syncRecordPO);
        }
        return null;
    }

    /**
     * 查询部门详情
     */
    public OapiV2DepartmentGetResponse.DeptGetResponse queryDepartmentDetail(Long deptId) {
        OrgSyncRecordPO syncRecordPO = new OrgSyncRecordPO();
        syncRecordPO.setChannelCode(DingTalkConstant.DING_TALK_CHANNEL_CODE);
        syncRecordPO.setDesc("获取部门详情");
        try {
            DingTalkClient client = new DefaultDingTalkClient(DingTalkConstant.QUERY_DEPARTMENT_DETAIL_URL);
            OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
            req.setDeptId(deptId);
            req.setLanguage("zh_CN");
            syncRecordPO.setRequest(JSON.toJSONString(req));
            OapiV2DepartmentGetResponse response = client.execute(req, getAccessToken());
            syncRecordPO.setResponse(JSON.toJSONString(response));
            if (response.isSuccess()) {
                return response.getResult();
            } else {
                throw new BusinessException(response.getErrmsg());
            }
        } catch (BusinessException e) {
            log.error("dingtalk query department error", e);
            syncRecordPO.setErrorMsg(e.getErrorMsg());
        } catch (Exception e) {
            log.error("dingtalk query department error", e);
            syncRecordPO.setErrorMsg(e.getMessage());
        } finally {
            orgSyncRecordRepo.insert(syncRecordPO);
        }
        return null;
    }

    /**
     * 查询所有的员工
     *
     * @param offset
     * @param size
     * @return
     */
    public OapiSmartworkHrmEmployeeQueryonjobResponse.PageResult queryAllEmployee(Long offset, Long size) {
        OrgSyncRecordPO syncRecordPO = new OrgSyncRecordPO();
        syncRecordPO.setChannelCode(DingTalkConstant.DING_TALK_CHANNEL_CODE);
        syncRecordPO.setDesc("分页查询员工ID");
        try {
            DingTalkClient client = new DefaultDingTalkClient(DingTalkConstant.QUERY_EMPLOYEE_USER_ID_LIST_URL);
            OapiSmartworkHrmEmployeeQueryonjobRequest req = new OapiSmartworkHrmEmployeeQueryonjobRequest();
            req.setStatusList("2,3,5,-1");
            req.setOffset(offset);
            req.setSize(size);
            syncRecordPO.setRequest(JSON.toJSONString(req));
            OapiSmartworkHrmEmployeeQueryonjobResponse response = client.execute(req, getAccessToken());
            syncRecordPO.setResponse(JSON.toJSONString(response));
            if (response.isSuccess()) {
                return response.getResult();
            } else {
                throw new BusinessException(response.getErrmsg());
            }
        } catch (BusinessException e) {
            log.error("dingtalk query employee error", e);
            syncRecordPO.setErrorMsg(e.getErrorMsg());
        } catch (Exception e) {
            log.error("dingtalk query employee error", e);
            syncRecordPO.setErrorMsg(e.getMessage());
        } finally {
            orgSyncRecordRepo.insert(syncRecordPO);
        }
        return null;
    }

    /**
     * 批量查询员工详情
     *
     * @param userIds
     * @param agentId
     * @return
     */
    public List<OapiSmartworkHrmEmployeeV2ListResponse.EmpRosterFieldVo> batchQueryEmployeeDetail(Set<String> userIds, Long agentId) {
        OrgSyncRecordPO syncRecordPO = new OrgSyncRecordPO();
        syncRecordPO.setChannelCode(DingTalkConstant.DING_TALK_CHANNEL_CODE);
        syncRecordPO.setDesc("批量查询员工详情");
        try {
            DingTalkClient client = new DefaultDingTalkClient(DingTalkConstant.QUERY_EMPLOYEE_DETAIL_URL);
            OapiSmartworkHrmEmployeeV2ListRequest req = new OapiSmartworkHrmEmployeeV2ListRequest();
            req.setUseridList(userIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            List<String> filedList = new ArrayList<>();
            filedList.add("sys00-name"); // 姓名
            filedList.add("sys00-email"); // 邮箱
            filedList.add("sys00-mainDeptId"); // 主部门ID
            filedList.add("sys00-mobile"); // 手机号
            filedList.add("sys00-confirmJoinTime"); // 入职时间
            filedList.add("sys01-employeeType"); // 员工类型
            filedList.add("sys01-employeeStatus"); // 员工状态
            filedList.add("sys00-jobNumber"); // 工号
            req.setFieldFilterList(filedList.stream().map(String::valueOf).collect(Collectors.joining(",")));
            req.setAgentid(agentId);
            syncRecordPO.setRequest(JSON.toJSONString(req));
            OapiSmartworkHrmEmployeeV2ListResponse response = client.execute(req, getAccessToken());
            syncRecordPO.setResponse(JSON.toJSONString(response));
            if (response.isSuccess()) {
                return response.getResult();
            } else {
                throw new BusinessException(response.getErrmsg());
            }
        } catch (BusinessException e) {
            log.error("dingtalk batch query employee detail error", e);
            syncRecordPO.setErrorMsg(e.getErrorMsg());
        } catch (Exception e) {
            log.error("dingtalk batch query employee detail error", e);
            syncRecordPO.setErrorMsg(e.getMessage());
        } finally {
            orgSyncRecordRepo.insert(syncRecordPO);
        }
        return null;
    }

    public QueryDismissionStaffIdListResponseBody queryResignedEmployeeIdList(Long offset, Integer size) {
        OrgSyncRecordPO syncRecordPO = new OrgSyncRecordPO();
        syncRecordPO.setChannelCode(DingTalkConstant.DING_TALK_CHANNEL_CODE);
        syncRecordPO.setDesc("查询离职员工ID");
        try {
            com.aliyun.dingtalkhrm_1_0.Client client = createClient();
            com.aliyun.dingtalkhrm_1_0.models.QueryDismissionStaffIdListHeaders queryDismissionStaffIdListHeaders = new com.aliyun.dingtalkhrm_1_0.models.QueryDismissionStaffIdListHeaders();
            queryDismissionStaffIdListHeaders.xAcsDingtalkAccessToken = getAccessToken();
            com.aliyun.dingtalkhrm_1_0.models.QueryDismissionStaffIdListRequest queryDismissionStaffIdListRequest = new com.aliyun.dingtalkhrm_1_0.models.QueryDismissionStaffIdListRequest()
                    .setNextToken(offset)
                    .setMaxResults(size);
            syncRecordPO.setRequest(JSON.toJSONString(queryDismissionStaffIdListRequest));
            QueryDismissionStaffIdListResponse queryDismissionStaffIdListResponse = client.queryDismissionStaffIdListWithOptions(queryDismissionStaffIdListRequest,
                    queryDismissionStaffIdListHeaders, new com.aliyun.teautil.models.RuntimeOptions());
            syncRecordPO.setResponse(JSON.toJSONString(queryDismissionStaffIdListResponse));
            if (queryDismissionStaffIdListResponse.getStatusCode().equals(200)) {
                return queryDismissionStaffIdListResponse.body;
            }
        } catch (TeaException err) {
            log.error("dingtalk query resigned employee error", err);
            syncRecordPO.setErrorMsg(err.getMessage());
        } catch (Exception _err) {
            log.error("dingtalk query resigned employee error", _err);
            syncRecordPO.setErrorMsg(_err.getMessage());
        } finally {
            orgSyncRecordRepo.insert(syncRecordPO);
        }
        return null;
    }

    public com.aliyun.dingtalkhrm_1_0.Client createClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkhrm_1_0.Client(config);
    }
}

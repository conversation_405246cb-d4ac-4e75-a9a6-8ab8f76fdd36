package io.terminus.trantor.org.domain.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.common.api.request.StringRequest;
import io.terminus.common.api.util.AssertUtils;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.iam.api.enums.role.RoleRelationBizType;
import io.terminus.iam.api.request.role.RoleRelationCreateParams;
import io.terminus.iam.api.request.user.CompleteUserParams;
import io.terminus.iam.api.request.user.UserGroupsInsertOrDeleteUsersParams;
import io.terminus.iam.api.request.user.UserPagingParams;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.api.response.user.User;
import io.terminus.iam.sdk.client.IAMClient;
import io.terminus.trantor.org.domain.converter.EmployeeConverter;
import io.terminus.trantor.org.infrastructure.gateway.OrgMdGateWay;
import io.terminus.trantor.org.infrastructure.repo.*;
import io.terminus.trantor.org.spi.dict.EmployeeStatusDict;
import io.terminus.trantor.org.spi.model.dto.*;
import io.terminus.trantor.org.spi.model.dto.dict.GenDictItemDTO;
import io.terminus.trantor.org.spi.model.po.*;
import io.terminus.trantor.org.spi.msg.OrgMsg;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.iam.IamClientFactory;
import io.terminus.trantor2.permission.cache.PermissionCacheCleanser;
import io.terminus.trantor2.permission.impl.common.cache.PortalToEndpointUtil;
import io.terminus.trantor2.service.dsl.properties.Pageable;
import io.terminus.trantor2.service.dsl.util.PageableUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2023-02-28 09:46
 */
@Service
@RequiredArgsConstructor
public class EmployeeService {

    private final IamClientFactory iamClientFactory;
    private final OrgUnitRepo orgUnitRepo;
    private final EmployeeRepo employeeRepo;
    private final PermissionCacheCleanser permissionCacheCleanser;
    private final EmployeeConverter employeeConverter;
    private final EmployeeOrgLinkRepo employeeOrgLinkRepo;
    private final IdGenerator idGenerator;
    private final OrgRankCfRepo orgRankCfRepo;
    private final OrgStructMdRepo orgStructMdRepo;
    private final OrgMdGateWay orgMdGateWay;
    private final OrgDimensionCfRepo orgDimensionCfRepo;
    private final EmployeeNoticeSceneLinkRepo employeeNoticeSceneLinkRepo;
    private final PortalToEndpointUtil portalToEndpointUtil;
    private static final String PHONE_NUMBER_PATTERN = "^1[3-9]\\d{9}$";
    private static final Pattern pattern = Pattern.compile(PHONE_NUMBER_PATTERN);


    @Transactional
    public EmployeeSaveDTO create(EmployeeSaveDTO request) {
        EmployeePO employeePO = employeeConverter.convert(request);
        employeePO.setId(idGenerator.nextId(EmployeePO.class));
        // 校验员工编码
        EmployeePO employee = employeeRepo.queryByCode(request.getCode());
        if (Objects.nonNull(employee)) {
            throw new BusinessException(OrgMsg.ORG_EMPLOYEE_CODE_IS_REPEAT);
        }
        // 校验手机号码是否重复
        isValidPhoneNumber(employeePO.getMobile());
        EmployeePO origin = employeeRepo.queryByMobile(employeePO.getMobile());
        if (Objects.nonNull(origin)) {
            throw new BusinessException(OrgMsg.ORG_EMPLOYEE_MOBILE_IS_REPEAT);
        }
        // 校验邮箱
        if (StringUtils.hasText(request.getEmail())) {
            EmployeePO employeePO1 = employeeRepo.queryByEmail(request.getEmail());
            if (Objects.nonNull(employeePO1)) {
                throw new BusinessException(OrgMsg.ORG_EMPLOYEE_EMAIL_IS_REPEAT);
            }
        }
        // 创建用户
        User user = createUser(request);
        if (Objects.isNull(user) || Objects.isNull(user.getId())) {
            throw new BusinessException(OrgMsg.ORG_CREATE_USER_FAIL);
        }
        // 判断该用户ID是否绑定了其他的员工
        EmployeePO employeePO1 = employeeRepo.queryByUserId(user.getId());
        if (Objects.nonNull(employeePO1)) {
            throw new BusinessException(OrgMsg.ORG_THIS_USER_HAS_BIND_OTHER_EMPLOYEE);
        }
        employeePO.setUserId(user.getId());
        employeePO.setUserName(user.getUsername());
        employeePO.setStatus(EmployeeStatusDict.ENABLED);
        // 处理员工角色
//        if (request.getIsHandleRole()) {
//            if (!CollectionUtils.isEmpty(request.getEmployeeRoleLinkList())) {
//                createOrSaveUserRole(request, employeePO);
//            }
//        }
        // 处理员工组织角色
        createEmployeeOrgLink(request, employeePO);
        // 处理通知模版
        createNoticeLink(request, employeePO);
        // 创建员工
        employeeRepo.insert(employeePO);
        request.setId(employeePO.getId());
        return request;
    }

    public void isValidPhoneNumber(String phoneNumber) {
        if (!pattern.matcher(phoneNumber).matches()) {
            throw new BusinessException(OrgMsg.ORG_EMPLOYEE_PHONE_NUMBER_IS_NOT_VALID);
        }
    }

    @Transactional
    public EmployeeSaveDTO update(EmployeeSaveDTO request) {
        AssertUtils.nonNull(request.getId(), OrgMsg.ORG_EMPLOYEE_ID_IS_NOT_NULL);
        EmployeePO employeePO = employeeConverter.convert(request);
        // 校验员工是否存在
        EmployeePO ex = employeeRepo.selectById(request.getId());
        if (Objects.isNull(ex)) {
            throw new BusinessException(OrgMsg.ORG_EMPLOYEE_IS_NOT_EXIST);
        }
        // 校验员工编码
        EmployeePO employee = employeeRepo.queryByCode(request.getCode());
        if (Objects.nonNull(employee) && !employee.getId().equals(request.getId())) {
            throw new BusinessException(OrgMsg.ORG_EMPLOYEE_CODE_IS_REPEAT);
        }
        // 校验手机号码是否重复
        isValidPhoneNumber(employeePO.getMobile());
        EmployeePO origin = employeeRepo.queryByMobile(employeePO.getMobile());
        if (Objects.nonNull(origin) && !origin.getId().equals(request.getId())) {
            throw new BusinessException(OrgMsg.ORG_EMPLOYEE_MOBILE_IS_REPEAT);
        }
        // 校验邮箱
        if (StringUtils.hasText(request.getEmail())) {
            EmployeePO employeePO1 = employeeRepo.queryByEmail(request.getEmail());
            if (Objects.nonNull(employeePO1) && !employeePO1.getId().equals(request.getId())) {
                throw new BusinessException(OrgMsg.ORG_EMPLOYEE_EMAIL_IS_REPEAT);
            }
        }
        // 更新用户信息
        updateUserInfo(request, ex);
        // 处理员工角色
//        if (request.getIsHandleRole()) {
//            createOrSaveUserRole(request, ex);
//        }
        // 处理员工组织角色关系
        updateEmployeeOrgLink(request, ex);
        // 处理通知模版
        updateNoticeLink(request, ex);
        // 更新员工
        employeeRepo.updateById(employeePO);
        return request;
    }

    public EmployeeDTO queryDetail(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        EmployeePO employeePO = employeeRepo.selectById(id);
        if (Objects.isNull(employeePO)) {
            throw new BusinessException(OrgMsg.ORG_EMPLOYEE_IS_NOT_EXIST);
        }
        EmployeeDTO employeeDTO = employeeConverter.convertDTO(employeePO);
        // 查询关联关系
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeId(employeePO.getId());
        employeeDTO.setEmployeeOrgLinkList(employeeConverter.convert(employeeOrgLinkPOS));
        List<EmployeeNoticeSceneLinkPO> employeeNoticeSceneLinkPOS = employeeNoticeSceneLinkRepo.queryByEmployeeId(employeePO.getId());
        employeeDTO.setNoticeSceneList(employeeConverter.covert(employeeNoticeSceneLinkPOS));
        // 查询员工权限
        if (Objects.nonNull(employeeDTO.getUserId())) {
            try {
                IAMClient iamClient = iamClientFactory.getIamClient();
                List<Role> roleList = iamClient.rolerelationClient().findRoleByUserId(employeePO.getUserId()).execute();
                if (!CollectionUtils.isEmpty(roleList)) {
                    List<EmployeeRoleLinkDTO> employeeRoleLinkDTOS = roleList.stream().map(item -> {
                        EmployeeRoleLinkDTO employeeRoleLinkDTO = new EmployeeRoleLinkDTO();
                        employeeRoleLinkDTO.setRoleId(item.getId());
                        employeeRoleLinkDTO.setRoleName(item.getName());
                        return employeeRoleLinkDTO;
                    }).collect(Collectors.toList());
                    employeeDTO.setEmployeeRoleLinkList(employeeRoleLinkDTOS);
                }
            } catch (Exception e) {
                throw new BusinessException(e.getMessage());
            }
        }
        return employeeDTO;
    }

    public List<RoleDTO> queryEmployeeRoleList(Long id) {
        EmployeePO employeePO = employeeRepo.selectById(id);
        if (Objects.isNull(employeePO)) {
            throw new BusinessException(OrgMsg.ORG_EMPLOYEE_IS_NOT_EXIST);
        }
        try {
            IAMClient iamClient = iamClientFactory.getIamClient();
            List<Role> roleList = iamClient.rolerelationClient().findRoleByUserId(employeePO.getUserId()).execute();
            if (!CollectionUtils.isEmpty(roleList)) {
                return roleList.stream().map(item -> {
                    RoleDTO roleDTO = new RoleDTO();
                    roleDTO.setId(item.getId());
                    roleDTO.setRoleName(item.getName());
                    roleDTO.setRoleId(item.getId());
                    return roleDTO;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return Collections.emptyList();
    }

    @Transactional
    public void delete(Long id) {
        // 校验员工是否存在
        EmployeePO employeePO = employeeRepo.selectById(id);
        if (Objects.isNull(employeePO)) {
            throw new BusinessException(OrgMsg.ORG_EMPLOYEE_IS_NOT_EXIST);
        }
        if (Objects.nonNull(employeePO.getUserId())) {
            try {
                IAMClient iamClient = iamClientFactory.getIamClient();
                Boolean result = iamClient.users().destroyById(employeePO.getUserId()).execute();
                if (result) {
                    employeePO.setUserId(null);
                    employeeRepo.update(employeePO, Wrappers.<EmployeePO>lambdaUpdate().set(EmployeePO::getUserId, null).eq(EmployeePO::getId, employeePO.getId()));
                }
            } catch (Exception e) {
                throw new BusinessException(OrgMsg.ORG_USER_DELETE_FAIL);
            }
        }
        // 删除员工组织关系
        employeeOrgLinkRepo.deleteByEmployeeId(employeePO.getId());
        employeeRepo.deleteById(employeePO);
    }

    public Paging<EmployeeDTO> paging(EmployeePageQueryDTO request) {
        Paging<EmployeePO> page = employeeRepo.page(request);
        if (CollectionUtils.isEmpty(page.getData())) {
            return Paging.empty();
        }
        Paging<EmployeeDTO> employeeDOPaging = new Paging<>();
        employeeDOPaging.setData(employeeConverter.convertDTO(page.getData()));
        employeeDOPaging.setTotal(page.getTotal());
        return employeeDOPaging;
    }


    public EmployeeDTO queryEmployeeByUserId(Long userId) {
        EmployeePO employeePO = employeeRepo.queryByUserId(userId);
        return employeeConverter.convertDTO(employeePO);
    }

    public List<EmployeeDTO> queryEmployeeByUserIds(Set<Long> userId) {
        List<EmployeePO> employeePOList = employeeRepo.queryByUserIds(userId);
        return employeeConverter.convertDTO(employeePOList);
    }

    /**
     * 该接口只为iam使用，其他勿用
     */
    public String queryCurrentEmployeeForIam() {
        Long userId = TrantorContext.getCurrentUserId();
        if (Objects.isNull(userId)) {
            return null;
        }
        EmployeePO employeePO = employeeRepo.queryByUserId(userId);
        if (Objects.isNull(employeePO)) {
            return null;
        }
        return "$" + employeePO.getId() + "$";
    }

    public Paging<EmployeeDTO> pageByOrg(EmployeePageQueryDTO request) {
        Page<EmployeePO> page = new Page<>();
        page.setCurrent(request.getPageNo());
        page.setSize(request.getPageSize());
        IPage<EmployeePO> employeePOIPage = employeeRepo.pageByOrg(page, request);
        if (CollectionUtils.isEmpty(employeePOIPage.getRecords())) {
            return Paging.empty();
        }
        Paging<EmployeeDTO> paging = new Paging<>();
        paging.setTotal(employeePOIPage.getTotal());
        paging.setData(employeeConverter.convertDTO(employeePOIPage.getRecords()));
        return paging;
    }

    public Paging<EmployeeDTO> pageByOrgContainChildOrg(EmployeePageQueryDTO request) {
        if (Objects.isNull(request.getOrgUnitId())) {
            return Paging.empty();
        }
        if (Objects.nonNull(request.getPageable())) {
            Pageable pageable = PageableUtil.toPageable(request.getPageable());
            Map<String, Object> pageableMap = request.getPageable();
            request.setPageNo(pageable.getPageNo());
            request.setPageSize(pageable.getPageSize());
            request.setName(Objects.isNull(PageableUtil.getValue("name", pageableMap)) ? null : PageableUtil.getValue("name", pageableMap).toString());
            request.setCode(Objects.isNull(PageableUtil.getValue("code", pageableMap)) ? null : PageableUtil.getValue("code", pageableMap).toString());
            request.setMobile(Objects.isNull(PageableUtil.getValue("mobile", pageableMap)) ? null : PageableUtil.getValue("mobile", pageableMap).toString());
        }
        // 组织递归
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryAllChild(request.getOrgUnitId());
        Set<Long> orgIdSet = new HashSet<>(orgStructMdPOS.size());
        if (!CollectionUtils.isEmpty(orgStructMdPOS)) {
            orgIdSet.addAll(orgStructMdPOS.stream().map(OrgStructMdPO::getId).collect(Collectors.toList()));
        }
        Page<EmployeePO> page = new Page<>();
        page.setCurrent(request.getPageNo());
        page.setSize(request.getPageSize());
        IPage<EmployeePO> employeePOIPage = employeeRepo.pageByUserOrgEmployee(page, request, orgIdSet);
        if (CollectionUtils.isEmpty(employeePOIPage.getRecords())) {
            return Paging.empty();
        }
        Paging<EmployeeDTO> paging = new Paging<>();
        paging.setTotal(employeePOIPage.getTotal());
        paging.setData(employeeConverter.convertDTO(employeePOIPage.getRecords()));
        return paging;
    }

    public Paging<EmployeeDTO> pageByUserOrgEmployee(EmployeePageQueryDTO request) {
        Long userId = TrantorContext.getCurrentUserId();
        if (Objects.isNull(userId)) {
            return Paging.empty();
        }
        // 先查询员工
        EmployeePO employeePO = employeeRepo.queryByUserId(request.getUserId());
        if (Objects.isNull(employeePO)) {
            return Paging.empty();
        }
        // 查询员工组织的关联表
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeId(employeePO.getId());
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return Paging.empty();
        }
        Set<Long> orgUnitIdSet = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toSet());
        Page<EmployeePO> page = new Page<>();
        page.setCurrent(request.getPageNo());
        page.setSize(request.getPageSize());
        IPage<EmployeePO> employeePOIPage = employeeRepo.pageByUserOrgEmployee(page, request, orgUnitIdSet);
        if (CollectionUtils.isEmpty(employeePOIPage.getRecords())) {
            return Paging.empty();
        }
        Paging<EmployeeDTO> paging = new Paging<>();
        paging.setTotal(employeePOIPage.getTotal());
        paging.setData(employeeConverter.convertDTO(employeePOIPage.getRecords()));
        return paging;
    }

    public List<Long> queryUserOrgList(UserOrgQueryDTO request) {
        // 先查询员工
        EmployeePO employeePO = employeeRepo.queryByUserId(request.getUserId());
        if (Objects.isNull(employeePO)) {
            return Collections.emptyList();
        }
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeId(employeePO.getId());
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return Collections.emptyList();
        }
        return employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toList());
    }

    public EmployeeDTO queryEmployeeByCode(String code) {
        EmployeePO employeePO = employeeRepo.queryByCode(code);
        return employeeConverter.convertDTO(employeePO);
    }

    public EmployeeDTO queryEmployeeById(Long id) {
        EmployeePO employeePO = employeeRepo.selectById(id);
        return employeeConverter.convertDTO(employeePO);
    }

    public List<EmployeeDTO> queryEmployeeByIds(Set<Long> ids) {
        List<EmployeePO> employeePOList = employeeRepo.selectBatchIds(ids);
        return employeeConverter.convertDTO(employeePOList);
    }

    /**
     * 当前用户的下级用户id
     * 切勿删除该方法
     */
    public List<Long> queryCurrentUserAllChildUser() {
        Long userId = TrantorContext.getCurrentUserId();
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        EmployeePO employeePO = employeeRepo.queryByUserId(userId);
        if (Objects.isNull(employeePO)) {
            return Collections.emptyList();
        }
        // 查询员工关联的组织
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeId(employeePO.getId());
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return Collections.emptyList();
        }
        List<Long> orgUnitIdList = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toList());
        Set<Long> orgUnitIdSet = new HashSet<>();
        Set<Long> orgIds = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toSet());
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.selectBatchIds(orgIds);
        if (!CollectionUtils.isEmpty(orgStructMdPOS)) {
            orgUnitIdSet = orgStructMdPOS.stream().map(OrgStructMdPO::getPath).flatMap(Collection::stream).collect(Collectors.toSet());
        }
        if (CollectionUtils.isEmpty(orgUnitIdSet)) {
            return Collections.emptyList();
        }
        // 去掉自己的组织
        orgUnitIdList.forEach(orgUnitIdSet::remove);
        if (CollectionUtils.isEmpty(orgUnitIdSet)) {
            return Collections.emptyList();
        }
        // 查询员工
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS1 = employeeOrgLinkRepo.queryByOrgUnitIds(orgUnitIdSet);
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS1)) {
            return Collections.emptyList();
        }
        // 查询员工
        Set<Long> employeeIdSet = employeeOrgLinkPOS1.stream().map(EmployeeOrgLinkPO::getEmployeeId).collect(Collectors.toSet());
        List<EmployeePO> employeePOS = employeeRepo.selectBatchIds(employeeIdSet);
        if (CollectionUtils.isEmpty(employeePOS)) {
            return Collections.emptyList();
        }
        return employeePOS.stream().map(EmployeePO::getUserId).collect(Collectors.toList());
    }

    /**
     * 当前用户的下级组织id
     * 切勿删除该方法
     */
    public List<Long> queryCurrentUserAllChildOrg() {
        Long userId = TrantorContext.getCurrentUserId();
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        EmployeePO employeePO = employeeRepo.queryByUserId(userId);
        if (Objects.isNull(employeePO)) {
            return Collections.emptyList();
        }
        // 查询员工关联的组织
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeId(employeePO.getId());
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return Collections.emptyList();
        }
        Set<Long> orgIds = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toSet());
        Set<Long> orgUnitIdSet = new HashSet<>();
        for (EmployeeOrgLinkPO employeeOrgLinkPO : employeeOrgLinkPOS) {
            List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryAllChild(employeeOrgLinkPO.getOrgUnitId());
            if (!CollectionUtils.isEmpty(orgStructMdPOS)) {
                orgUnitIdSet.addAll(orgStructMdPOS.stream().map(OrgStructMdPO::getId).collect(Collectors.toSet()));
            }
        }
        orgIds.forEach(orgUnitIdSet::remove);
        if (CollectionUtils.isEmpty(orgUnitIdSet)) {
            return Collections.emptyList();
        }
        return new ArrayList<>(orgUnitIdSet);
    }

    /**
     * 当前用户的所在组织和下级组织id
     * 切勿删除该方法
     */
    public List<Long> queryCurrentUserOrgAndAllChildOrg() {
        Long userId = TrantorContext.getCurrentUserId();
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        EmployeePO employeePO = employeeRepo.queryByUserId(userId);
        if (Objects.isNull(employeePO)) {
            return Collections.emptyList();
        }
        // 查询员工关联的组织
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeId(employeePO.getId());
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return Collections.emptyList();
        }
        Set<Long> orgUnitIdSet = new HashSet<>();
        for (EmployeeOrgLinkPO employeeOrgLinkPO : employeeOrgLinkPOS) {
            List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryAllChild(employeeOrgLinkPO.getOrgUnitId());
            if (!CollectionUtils.isEmpty(orgStructMdPOS)) {
                orgUnitIdSet.addAll(orgStructMdPOS.stream().map(OrgStructMdPO::getId).collect(Collectors.toSet()));
            }
        }
        return new ArrayList<>(orgUnitIdSet);
    }

    private void handelEmployeeUserGroup(Set<Long> userGroupIdSet, Boolean isDelete, EmployeePO employeePO) {
        UserGroupsInsertOrDeleteUsersParams userGroupsInsertOrDeleteUsersParams = new UserGroupsInsertOrDeleteUsersParams();
        userGroupsInsertOrDeleteUsersParams.setUserIds(Sets.newHashSet(employeePO.getUserId()));
        userGroupsInsertOrDeleteUsersParams.setUserGroupIds(Sets.newHashSet(userGroupIdSet));
        try {
            IAMClient iamClient = iamClientFactory.getIamClient();
            if (isDelete) {
                iamClient.userGroups().deleteUsersFromUserGroups(userGroupsInsertOrDeleteUsersParams).execute();
                permissionCacheCleanser.invalidateUserCache(employeePO.getUserId());
            } else {
                iamClient.userGroups().insertUsersToUserGroups(userGroupsInsertOrDeleteUsersParams).execute();
                permissionCacheCleanser.invalidateUserCache(employeePO.getUserId());
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 创建用户
     */
    private User createUser(EmployeeSaveDTO request) {
        User user;
        try {
            // 先根据手机号码查询是否存在对应的用户
            UserPagingParams userPagingParams = new UserPagingParams();
            userPagingParams.setNo(1);
            userPagingParams.setSize(100);
            userPagingParams.setMobile(request.getMobile());
            IAMClient iamClient = iamClientFactory.getIamClient();
            List<User> users = iamClient.users().findAll(userPagingParams).execute();
            if (!CollectionUtils.isEmpty(users)) {
                return users.get(0);
            }
            // 根据邮箱查询是否有对应的用户
            if (StringUtils.hasText(request.getEmail())) {
                userPagingParams.setMobile(null);
                userPagingParams.setEmail(request.getEmail());
                List<User> userList = iamClient.users().findAll(userPagingParams).execute();
                if (!CollectionUtils.isEmpty(userList)) {
                    return userList.get(0);
                }
                CompleteUserParams completeUserParams = new CompleteUserParams();
                if (StringUtils.hasText(request.getUserName())) {
                    completeUserParams.setUsername(request.getUserName());
                } else {
                    completeUserParams.setUsername("u_" + request.getMobile());
                }
                completeUserParams.setNickname(request.getName());
                completeUserParams.setRealname(request.getName());
                completeUserParams.setMobile(request.getMobile());
                completeUserParams.setEmail(request.getEmail());
                user = iamClient.users().create(completeUserParams).execute();
                return user;
            }
            CompleteUserParams completeUserParams = new CompleteUserParams();
            if (StringUtils.hasText(request.getUserName())) {
                completeUserParams.setUsername(request.getUserName());
            } else {
                completeUserParams.setUsername("u_" + request.getMobile());
            }
            completeUserParams.setNickname(request.getName());
            completeUserParams.setRealname(request.getName());
            completeUserParams.setMobile(request.getMobile());
            completeUserParams.setEmail(request.getEmail());
            if (StringUtils.hasText(TrantorContext.getPortalCode())) {
                Long iamAppIdByPortalCode = portalToEndpointUtil.getIamAppIdByPortalCode(TrantorContext.getPortalCode());
                completeUserParams.setSignByApplication(iamAppIdByPortalCode);
            }
            user = iamClient.users().create(completeUserParams).execute();
            return user;
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 启用员工
     */
    public void enable(Long id) {
        // 校验员工是否存在
        EmployeePO ex = employeeRepo.selectById(id);
        if (Objects.isNull(ex)) {
            throw new BusinessException(OrgMsg.ORG_EMPLOYEE_IS_NOT_EXIST);
        }
        try {
            if (Objects.nonNull(ex.getUserId())) {
                IAMClient iamClient = iamClientFactory.getIamClient();
                iamClient.users().enabledById(ex.getUserId()).execute();
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        ex.setStatus(EmployeeStatusDict.ENABLED);
        employeeRepo.updateById(ex);
    }

    /**
     * 禁用用员工
     */
    public void disable(Long id) {
        // 校验员工是否存在
        EmployeePO ex = employeeRepo.selectById(id);
        if (Objects.isNull(ex)) {
            throw new BusinessException(OrgMsg.ORG_EMPLOYEE_IS_NOT_EXIST);
        }
        try {
            if (Objects.nonNull(ex.getUserId())) {
                IAMClient iamClient = iamClientFactory.getIamClient();
                iamClient.users().disabledById(ex.getUserId()).execute();
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        ex.setStatus(EmployeeStatusDict.DISABLED);
        employeeRepo.updateById(ex);
    }

    private void updateUserInfo(EmployeeSaveDTO request, EmployeePO employeePO) {
        try {
            IAMClient iamClient = iamClientFactory.getIamClient();
            // 更新用户信息
            CompleteUserParams completeUserParams = new CompleteUserParams();
            if (StringUtils.hasText(request.getUserName())) {
                completeUserParams.setUsername(request.getUserName());
            }
            completeUserParams.setNickname(request.getName());
            completeUserParams.setRealname(request.getName());
            completeUserParams.setMobile(request.getMobile());
            completeUserParams.setEmail(request.getEmail());
            completeUserParams.setId(employeePO.getUserId());
            if (StringUtils.hasText(TrantorContext.getPortalCode())) {
                Long iamAppIdByPortalCode = portalToEndpointUtil.getIamAppIdByPortalCode(TrantorContext.getPortalCode());
                completeUserParams.setSignByApplication(iamAppIdByPortalCode);
            }
            iamClient.users().updateById(employeePO.getUserId(), completeUserParams).execute();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    private void createOrSaveUserRole(EmployeeSaveDTO request, EmployeePO employeePO) {
        try {
            IAMClient iamClient = iamClientFactory.getIamClient();
            // 处理角色
            List<Long> roleIdList;
            if (Objects.isNull(request.getId())) {
                roleIdList = request.getEmployeeRoleLinkList().stream().map(EmployeeRoleLinkDTO::getRoleId).collect(Collectors.toList());
            } else {
                if (CollectionUtils.isEmpty(request.getEmployeeRoleLinkList())) {
                    roleIdList = new ArrayList<>();
                } else {
                    roleIdList = request.getEmployeeRoleLinkList().stream().map(EmployeeRoleLinkDTO::getRoleId).collect(Collectors.toList());
                }
            }
            RoleRelationCreateParams roleRelationCreateParams = new RoleRelationCreateParams();
            roleRelationCreateParams.setBizType(RoleRelationBizType.USER_ROLE);
            roleRelationCreateParams.setBizId(String.valueOf(employeePO.getUserId()));
            roleRelationCreateParams.setRoleIds(roleIdList);
            iamClient.rolerelationClient().flushToNewRoleRelation(roleRelationCreateParams).execute();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    private void createEmployeeOrgLink(EmployeeSaveDTO request, EmployeePO employeePO) {
        try {
            // 创建员工组织关联关系
            if (!CollectionUtils.isEmpty(request.getEmployeeOrgLinkList())) {
                List<EmployeeOrgLinkPO> employeeOrgLinkPOList = new ArrayList<>();
                for (EmployeeOrgLinkDTO employeeOrgLinkDTO : request.getEmployeeOrgLinkList()) {
                    EmployeeOrgLinkPO employeeOrgLinkPO = new EmployeeOrgLinkPO();
                    employeeOrgLinkPO.setEmployeeId(employeePO.getId());
                    employeeOrgLinkPO.setOrgUnitId(employeeOrgLinkDTO.getOrgUnitId());
                    employeeOrgLinkPO.setIdentityId(employeeOrgLinkDTO.getIdentityId());
                    employeeOrgLinkPO.setIsMainOrg(employeeOrgLinkDTO.getIsMainOrg());
                    employeeOrgLinkPOList.add(employeeOrgLinkPO);
                }
                employeeOrgLinkRepo.insertBatch(employeeOrgLinkPOList);
//                // 处理用户和用户组的关系
//                Set<Long> orgUnitIdList = request.getEmployeeOrgLinkList().stream().map(EmployeeOrgLinkDTO::getOrgUnitId).collect(Collectors.toSet());
//                Set<Long> userGroupIdSet = queryOrgUserGroup(orgUnitIdList);
//                if (!CollectionUtils.isEmpty(userGroupIdSet)) {
//                    handelEmployeeUserGroup(userGroupIdSet, Boolean.FALSE, employeePO);
//                }
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    private void createNoticeLink(EmployeeSaveDTO request, EmployeePO employeePO) {
        if (CollectionUtils.isEmpty(request.getNoticeSceneList())) {
            return;
        }
        Map<String, List<EmployeeNoticeSceneLinkDTO>> employeeNoticeSceneLinkMap = request.getNoticeSceneList().stream().collect(Collectors.groupingBy(EmployeeNoticeSceneLinkDTO::getNoticeSceneKey));
        if (employeeNoticeSceneLinkMap.size() != request.getNoticeSceneList().size()) {
            throw new BusinessException(OrgMsg.ORG_NOTICE_SCENE_IS_NOT_UNIQUE);
        }
        List<EmployeeNoticeSceneLinkPO> employeeNoticeSceneLinkPOS = new ArrayList<>(request.getNoticeSceneList().size());
        for (EmployeeNoticeSceneLinkDTO employeeNoticeSceneLinkDTO : request.getNoticeSceneList()) {
            EmployeeNoticeSceneLinkPO employeeNoticeSceneLinkPO = new EmployeeNoticeSceneLinkPO();
            employeeNoticeSceneLinkPO.setEmployeeId(employeePO.getId());
            employeeNoticeSceneLinkPO.setNoticeSceneKey(employeeNoticeSceneLinkDTO.getNoticeSceneKey());
            employeeNoticeSceneLinkPO.setNoticeSceneId(employeeNoticeSceneLinkDTO.getNoticeSceneId());
            employeeNoticeSceneLinkPOS.add(employeeNoticeSceneLinkPO);
        }
        employeeNoticeSceneLinkRepo.insertBatch(employeeNoticeSceneLinkPOS);
    }

    private void updateNoticeLink(EmployeeSaveDTO request, EmployeePO employeePO) {
        employeeNoticeSceneLinkRepo.deleteByEmployeeId(employeePO.getId());
        createNoticeLink(request, employeePO);
    }

    private void updateEmployeeOrgLink(EmployeeSaveDTO request, EmployeePO employeePO) {
        try {
//            // 创建员工组织关联关系
//            List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeId(employeePO.getId());
//            if (!CollectionUtils.isEmpty(employeeOrgLinkPOS) && !CollectionUtils.isEmpty(request.getEmployeeOrgLinkList())) {
//                Set<Long> existOrgIdSet = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toSet());
//                Set<Long> newOrgIdSet = request.getEmployeeOrgLinkList().stream().map(EmployeeOrgLinkDTO::getOrgUnitId).collect(Collectors.toSet());
//                Set<Long> existUserGrouIdSet = queryOrgUserGroup(existOrgIdSet);
//                Set<Long> updateUserGroupIdSet = queryOrgUserGroup(newOrgIdSet);
//
//                Set<Long> createUserGroupIdSet = updateUserGroupIdSet.stream().filter(t -> !existUserGrouIdSet.contains(t)).collect(Collectors.toSet());
//                Set<Long> deleteUserGroupIdSet = existUserGrouIdSet.stream().filter(t -> !updateUserGroupIdSet.contains(t)).collect(Collectors.toSet());
//                if (!CollectionUtils.isEmpty(createUserGroupIdSet)) {
//                    handelEmployeeUserGroup(createUserGroupIdSet, Boolean.FALSE, employeePO);
//                }
//                if (!CollectionUtils.isEmpty(deleteUserGroupIdSet)) {
//                    handelEmployeeUserGroup(deleteUserGroupIdSet, Boolean.TRUE, employeePO);
//                }
//            }
//            if (!CollectionUtils.isEmpty(employeeOrgLinkPOS) && CollectionUtils.isEmpty(request.getEmployeeOrgLinkList())) {
//                // 将员工从用户组中删除
//                Set<Long> orgUnitIdList = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toSet());
//                Set<Long> userGroupIdSet = queryOrgUserGroup(orgUnitIdList);
//                if (!CollectionUtils.isEmpty(userGroupIdSet)) {
//                    handelEmployeeUserGroup(userGroupIdSet, Boolean.TRUE, employeePO);
//                }
//            }
//            if (CollectionUtils.isEmpty(employeeOrgLinkPOS) && !CollectionUtils.isEmpty(request.getEmployeeOrgLinkList())) {
//                // 将员工加入用户组
//                Set<Long> orgUnitIdList = request.getEmployeeOrgLinkList().stream().map(EmployeeOrgLinkDTO::getOrgUnitId).collect(Collectors.toSet());
//                Set<Long> userGroupIdSet = queryOrgUserGroup(orgUnitIdList);
//                if (!CollectionUtils.isEmpty(userGroupIdSet)) {
//                    handelEmployeeUserGroup(userGroupIdSet, Boolean.FALSE, employeePO);
//                }
//            }
            // 先删除关系
            employeeOrgLinkRepo.deleteByEmployeeId(request.getId());
            if (!CollectionUtils.isEmpty(request.getEmployeeOrgLinkList())) {
                List<EmployeeOrgLinkPO> employeeOrgLinkPOList = new ArrayList<>(request.getEmployeeOrgLinkList().size());
                for (EmployeeOrgLinkDTO employeeOrgLinkDTO : request.getEmployeeOrgLinkList()) {
                    EmployeeOrgLinkPO employeeOrgLinkPO = new EmployeeOrgLinkPO();
                    employeeOrgLinkPO.setEmployeeId(employeePO.getId());
                    employeeOrgLinkPO.setOrgUnitId(employeeOrgLinkDTO.getOrgUnitId());
                    employeeOrgLinkPO.setIdentityId(employeeOrgLinkDTO.getIdentityId());
                    employeeOrgLinkPO.setIsMainOrg(employeeOrgLinkDTO.getIsMainOrg());
                    employeeOrgLinkPOList.add(employeeOrgLinkPO);
                }
                employeeOrgLinkRepo.insertBatch(employeeOrgLinkPOList);
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    private Set<Long> queryOrgUserGroup(Set<Long> orgUnitIdSet) {
        List<OrgUnitPO> orgUnitPOS = orgUnitRepo.selectBatchIds(orgUnitIdSet);
        Set<Long> orgUnitSet = new HashSet<>();
        for (OrgUnitPO orgUnitPO : orgUnitPOS) {
            if (StringUtils.hasText(orgUnitPO.getPath())) {
                orgUnitSet.addAll(Objects.requireNonNull(JSON.parseArray(orgUnitPO.getPath(), Long.class)));
            }
        }
        if (!CollectionUtils.isEmpty(orgUnitSet)) {
            List<OrgUnitPO> orgUnitPOList = orgUnitRepo.selectBatchIds(orgUnitSet);
            for (OrgUnitPO orgUnitPO : orgUnitPOList) {
                if (Objects.isNull(orgUnitPO.getUserGroup())) {
                    throw new BusinessException("Inventory.org.not.config.user.group.please.update.org");
                }
            }
            return orgUnitPOList.stream().map(OrgUnitPO::getUserGroup).filter(Objects::nonNull).collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

    public List<EmployeeDTO> queryOrgUnitAppointRankEmployee(OrgRankEmployeeQueryDTO request) {
        if (Objects.isNull(request.getOrgUnitId())) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(request.getRankIdSet())) {
            return Collections.emptyList();
        }
        if (request.getIsRecursion()) {
            OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectById(request.getOrgUnitId());
            List<Long> path = orgStructMdPO.getPath();
            for (Long orgUnitId : path) {
                List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryOrgUnitAppointRankEmployee(orgUnitId, request.getRankIdSet(), Boolean.TRUE);
                if (!CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
                    Set<Long> employeeIdSet = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getEmployeeId).collect(Collectors.toSet());
                    return employeeConverter.convertDTO(employeeRepo.selectBatchIds(employeeIdSet));
                }
            }
            return Collections.emptyList();
        } else {
            List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryOrgUnitAppointRankEmployee(request.getOrgUnitId(), request.getRankIdSet(), Boolean.TRUE);
            if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
                return Collections.emptyList();
            }
            Set<Long> employeeIdSet = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getEmployeeId).collect(Collectors.toSet());
            List<EmployeePO> employeePOS = employeeRepo.selectBatchIds(employeeIdSet);
            if (CollectionUtils.isEmpty(employeePOS)) {
                return Collections.emptyList();
            }
            return employeeConverter.convertDTO(employeePOS);
        }
    }

    public OrgAndRankDTO queryOrgUnitRoleByCode(String code) {
        OrgAndRankDTO result = new OrgAndRankDTO();
        EmployeePO employeePO = employeeRepo.queryByCode(code);
        if (Objects.isNull(employeePO)) {
            return result;
        }
        EmployeeOrgLinkPO employeeOrgLinkPO = employeeOrgLinkRepo.queryByMainEmployeeId(employeePO.getId(), Boolean.TRUE);
        if (Objects.isNull(employeeOrgLinkPO)) {
            return result;
        }
        if (Objects.nonNull(employeeOrgLinkPO.getOrgUnitId())) {
            OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectById(employeeOrgLinkPO.getOrgUnitId());
            if (Objects.nonNull(orgStructMdPO)) {
                OrgUnitDTO orgUnitDTO = new OrgUnitDTO();
                orgUnitDTO.setId(orgStructMdPO.getId());
                orgUnitDTO.setCode(orgStructMdPO.getOrgCode());
                orgUnitDTO.setName(orgStructMdPO.getOrgName());
                result.setOrgUnitDTO(orgUnitDTO);
            }
        }
        if (Objects.nonNull(employeeOrgLinkPO.getIdentityId())) {
            List<GenDictItemDTO> dictItemByIds = orgMdGateWay.findDictItemByIds(new IdsRequest(Sets.newHashSet(employeeOrgLinkPO.getIdentityId())));
            if (!CollectionUtils.isEmpty(dictItemByIds)) {
                GenDictItemDTO genDictItemDTO = dictItemByIds.get(0);
                OrgRankCfDTO orgRankCfDTO = new OrgRankCfDTO();
                orgRankCfDTO.setId(genDictItemDTO.getId());
                orgRankCfDTO.setCode(genDictItemDTO.getCode());
                orgRankCfDTO.setName(genDictItemDTO.getName());
                result.setRankCfDTO(orgRankCfDTO);
            }
        }
        return result;
    }

    public List<EmployeeRankDTO> queryEmployeeByOrgRankNew(List<EmployeeQueryOrgRoleDTO> request) {
        List<EmployeeRankDTO> employeeRankDTOList = new ArrayList<>();
        for (EmployeeQueryOrgRoleDTO dto : request) {
            EmployeeRankDTO employeeRankDTO = new EmployeeRankDTO();
            employeeRankDTO.setRankCode(dto.getRankCode());
            employeeRankDTO.setUnitCode(dto.getUnitCode());
            employeeRankDTOList.add(employeeRankDTO);
            GenDictItemDTO query = new GenDictItemDTO();
            query.setCode(dto.getRankCode());
            GenDictItemDTO genDictItemDTO = orgMdGateWay.queryDictItemByCode(query);
            if (Objects.isNull(genDictItemDTO)) {
                continue;
            }
            OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(dto.getOrgDimensionCode());
            if (Objects.isNull(orgDimensionCfPO)) {
                continue;
            }
            OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectByCode(orgDimensionCfPO.getId(), dto.getUnitCode());
            if (Objects.isNull(orgStructMdPO)) {
                continue;
            }
            List<EmployeeOrgLinkPO> employeeOrgLinkPOS = null;
            if (dto.getFlag()) {
                //查询path
                List<Long> path = orgStructMdPO.getPath();
                if (!dto.getInclude()) {
                    path = path.subList(0, path.size() - 1);
                }
                for (Long p : path) {
                    employeeOrgLinkPOS = employeeOrgLinkRepo.queryOrgUnitByRankEmployee(p, genDictItemDTO.getId(), Boolean.TRUE);
                    if (!CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
                        break;
                    }
                }
            } else {
                //不用向上查找
                employeeOrgLinkPOS = employeeOrgLinkRepo.queryOrgUnitByRankEmployee(orgStructMdPO.getId(), genDictItemDTO.getId(), Boolean.TRUE);
            }
            if (!CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
                EmployeeOrgLinkPO employeeOrgLinkPO = employeeOrgLinkPOS.get(0);
                EmployeePO employeePO = employeeRepo.selectById(employeeOrgLinkPO.getEmployeeId());
                employeeRankDTO.setEmployeeDTO(employeeConverter.convertDTO(employeePO));
                break;
            }
        }
        return employeeRankDTOList;
    }

    public List<EmployeeDTO> queryEmployeeByOrgRank(List<EmployeeQueryOrgRoleDTO> request) {
        HashMap<String, Long> map = new HashMap<>();
        HashMap<String, OrgUnitPO> unitMap = new HashMap<>();
        for (EmployeeQueryOrgRoleDTO dto : request) {
            if (!map.containsKey(dto.getRankCode())) {
                OrgRankCfPO orgRankCfPO = orgRankCfRepo.selectByCode(dto.getRankCode());
                if (Objects.isNull(orgRankCfPO)) {
                    return Collections.emptyList();
                }
                map.put(dto.getRankCode(), orgRankCfPO.getId());
            }
            if (!unitMap.containsKey(dto.getUnitCode())) {
                OrgUnitPO orgUnitPO = orgUnitRepo.queryByCode(dto.getUnitCode());
                if (Objects.isNull(orgUnitPO)) {
                    return Collections.emptyList();
                }
                unitMap.put(dto.getUnitCode(), orgUnitPO);
            }
            List<EmployeeOrgLinkPO> po = null;
            if (dto.getFlag() == Boolean.TRUE) {
                //查询path
                OrgUnitPO unitPO = unitMap.get(dto.getUnitCode());
                List<Long> path = JSON.parseArray(unitPO.getPath(), Long.class);
                Collections.reverse(path);
                if (Boolean.FALSE == dto.getInclude()) {
                    path = path.subList(1, path.size());
                }
                for (Long p : path) {
                    po = employeeOrgLinkRepo.queryOrgUnitByRankEmployee(p, map.get(dto.getRankCode()), Boolean.TRUE);
                    if (!CollectionUtils.isEmpty(po)) {
                        break;
                    }
                }
            } else {
                //不用向上查找
                OrgUnitPO unitPO = unitMap.get(dto.getUnitCode());
                po = employeeOrgLinkRepo.queryOrgUnitByRankEmployee(unitPO.getId(), map.get(dto.getRankCode()), Boolean.TRUE);
            }
            if (!CollectionUtils.isEmpty(po)) {
                Set<Long> ids = po.stream().map(EmployeeOrgLinkPO::getEmployeeId).collect(Collectors.toSet());
                List<EmployeePO> employeePOS = employeeRepo.selectBatchIds(ids);
                if (CollectionUtils.isEmpty(employeePOS)) {
                    return Collections.emptyList();
                }
                return employeeConverter.convertDTO(employeePOS);
            } else {
                return Collections.emptyList();
            }

        }
        return Collections.emptyList();
    }

    public OrgAndRankDTO queryOrgUnitSupRankByCode(EmployeeSupQueryDTO dto) {
        EmployeePO employeePO = employeeRepo.queryByCode(dto.getCode());
        if (Objects.isNull(employeePO)) {
            return null;
        }
        GenDictItemDTO query = new GenDictItemDTO();
        query.setCode(dto.getRankCode());
        GenDictItemDTO genDictItem = orgMdGateWay.queryDictItemByCode(query);
        if (Objects.isNull(genDictItem)) {
            return null;
        }
        EmployeeOrgLinkPO employeeOrgLinkPO = employeeOrgLinkRepo.queryByMainEmployeeId(employeePO.getId(), Boolean.TRUE);
        if (Objects.isNull(employeeOrgLinkPO)) {
            return null;
        }
        OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectById(employeeOrgLinkPO.getOrgUnitId());
        if (Objects.isNull(orgStructMdPO.getOrgParentId())) {
            return null;
        }
        List<EmployeeOrgLinkPO> linkPOS = employeeOrgLinkRepo.queryOrgUnitByRankEmployee(orgStructMdPO.getOrgParentId(), genDictItem.getId(), Boolean.TRUE);
        if (CollectionUtils.isEmpty(linkPOS)) {
            return null;
        }
        EmployeeOrgLinkPO linkPO = linkPOS.get(0);
        OrgAndRankDTO orgAndRankDTO = new OrgAndRankDTO();
        if (Objects.nonNull(linkPO.getOrgUnitId())) {
            OrgStructMdPO orgStructMd = orgStructMdRepo.selectById(linkPO.getOrgUnitId());
            if (Objects.nonNull(orgStructMd)) {
                OrgUnitDTO orgUnitDTO = new OrgUnitDTO();
                orgUnitDTO.setId(orgStructMd.getId());
                orgUnitDTO.setCode(orgStructMd.getOrgCode());
                orgUnitDTO.setName(orgStructMd.getOrgName());
                orgAndRankDTO.setOrgUnitDTO(orgUnitDTO);
            }
        }
        if (Objects.nonNull(linkPO.getIdentityId())) {
            List<GenDictItemDTO> dictItemByIds = orgMdGateWay.findDictItemByIds(new IdsRequest(Sets.newHashSet(linkPO.getIdentityId())));
            if (!CollectionUtils.isEmpty(dictItemByIds)) {
                GenDictItemDTO genDictItemDTO = dictItemByIds.get(0);
                OrgRankCfDTO orgRankCfDTO = new OrgRankCfDTO();
                orgRankCfDTO.setId(genDictItemDTO.getId());
                orgRankCfDTO.setCode(genDictItemDTO.getCode());
                orgRankCfDTO.setName(genDictItemDTO.getName());
                orgAndRankDTO.setRankCfDTO(orgRankCfDTO);
            }
        }
        return orgAndRankDTO;
    }

    public List<EmployeeDTO> queryEmployeeByCodes(EmployeeQueryCodes request) {
        if (CollectionUtils.isEmpty(request.getCodes())) {
            return Collections.emptyList();
        }
        List<EmployeePO> employeePOS = employeeRepo.selectByCodes(request.getCodes());
        if (CollectionUtils.isEmpty(employeePOS)) {
            return Collections.emptyList();
        }
        return employeeConverter.convertDTO(employeePOS);
    }

    public List<EmployeeDTO> queryEmployeeByOrgUnitCode(OrgUnitCodeQueryDto request) {
        if (Objects.isNull(request.getCode())) {
            return Collections.emptyList();
        }
        OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectByCode(null, request.getCode());
        if (Objects.isNull(orgStructMdPO)) {
            return Collections.emptyList();
        }
        List<EmployeeOrgLinkPO> linkPO = employeeOrgLinkRepo.queryOrgUnitByUnitId(orgStructMdPO.getId());
        if (CollectionUtils.isEmpty(linkPO)) {
            return Collections.emptyList();
        }
        Set<Long> ids = linkPO.stream().map(EmployeeOrgLinkPO::getEmployeeId).collect(Collectors.toSet());
        List<EmployeePO> employeePOS = employeeRepo.selectBatchIds(ids);
        if (CollectionUtils.isEmpty(employeePOS)) {
            return Collections.emptyList();
        }
        return employeeConverter.convertDTO(employeePOS);
    }

    private void buildOrgPath(Long orgId, List<Long> path) {
        if (Objects.isNull(orgId)) {
            return;
        }
        path.add(orgId);
        OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectById(orgId);
        buildOrgPath(orgStructMdPO.getOrgParentId(), path);
    }

    private void getChildren(List<OrgStructMdPO> orgStructMdPOList, Long parentId) {
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryByPid(parentId);
        if (!CollectionUtils.isEmpty(orgStructMdPOS)) {
            for (OrgStructMdPO orgStructMdPO : orgStructMdPOS) {
                orgStructMdPOList.add(orgStructMdPO);
                getChildren(orgStructMdPOList, orgStructMdPO.getId());
            }
        }
    }

    public List<EmployeeDTO> queryByNoticeScene(StringRequest request) {
        if (!StringUtils.hasText(request.getValue())) {
            return Collections.emptyList();
        }
        List<EmployeeNoticeSceneLinkPO> employeeNoticeSceneLinkPOS = employeeNoticeSceneLinkRepo.queryBySceneKey(request.getValue());
        if (CollectionUtils.isEmpty(employeeNoticeSceneLinkPOS)) {
            return Collections.emptyList();
        }
        Set<Long> employeeIds = employeeNoticeSceneLinkPOS.stream().map(EmployeeNoticeSceneLinkPO::getEmployeeId).collect(Collectors.toSet());
        return employeeConverter.convertDTO(employeeRepo.selectBatchIds(employeeIds));
    }
}

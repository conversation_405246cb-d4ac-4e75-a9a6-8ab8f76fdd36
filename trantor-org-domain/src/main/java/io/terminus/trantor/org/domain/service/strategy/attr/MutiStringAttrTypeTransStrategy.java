package io.terminus.trantor.org.domain.service.strategy.attr;

import io.terminus.erp.strategy.Strategy;
import io.terminus.trantor.org.spi.dict.AttrDataTypeDict;
import io.terminus.trantor.org.spi.strategy.attr.AttrTypeTransStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * @author: 张博
 * @date: 2023-11-17 19:00
 */
@Component
@AllArgsConstructor
@Strategy(value = AttrTypeTransStrategy.class, implKey = AttrDataTypeDict.MULTICHAR)
public class MutiStringAttrTypeTransStrategy implements AttrTypeTransStrategy<String> {

    @Override
    public Boolean match(String attrDataType, Boolean isMulti) {
        return AttrDataTypeDict.MULTICHAR.equals(attrDataType);
    }

    @Override
    public String trans(String value) {
        return value;
    }
}

package io.terminus.trantor.org.domain.cache;

import io.terminus.trantor.org.infrastructure.repo.OrgBusinessTypeRepo;
import io.terminus.trantor.org.spi.model.po.OrgBusinessTypePO;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * @author: 张博
 * @date: 2024-02-29 18:00
 */
@Component
@RequiredArgsConstructor
public class OrgBusinessTypeCache {

    private final OrgBusinessTypeRepo orgBusinessTypeRepo;

    @Cacheable(cacheNames = "ORG_BUSINESS_TYPE_BY_ID:", key = "#id")
    public OrgBusinessTypePO findById(Long id) {
        return orgBusinessTypeRepo.selectById(id);
    }

    @CacheEvict(cacheNames = "ORG_BUSINESS_TYPE_BY_ID:", key = "#id")
    public void clear(Long id) {
    }
}

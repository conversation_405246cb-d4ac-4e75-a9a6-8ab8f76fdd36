package io.terminus.trantor.org.domain.service;

import io.terminus.common.api.request.IdsRequest;
import io.terminus.trantor.org.domain.converter.OrgDimensionConverter;
import io.terminus.trantor.org.infrastructure.repo.OrgDimensionCfRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgStructMdRepo;
import io.terminus.trantor.org.spi.dict.OrgStatusDict;
import io.terminus.trantor.org.spi.model.dto.OrgDimensionDTO;
import io.terminus.trantor.org.spi.model.po.OrgDimensionCfPO;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@RequiredArgsConstructor
public class OrgDimensionCfService {

    private final OrgDimensionConverter orgDimensionConverter;
    private final OrgDimensionCfRepo orgDimensionCfRepo;
    private final OrgStructMdRepo orgStructMdRepo;

    public void enable(IdsRequest request) {
        for (Long id : request.getIds()) {
            OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.selectById(id);
            orgDimensionCfPO.setOrgDimensionStatus(OrgStatusDict.ENABLED);
            orgDimensionCfRepo.updateById(orgDimensionCfPO);

            OrgStructMdPO struct = orgStructMdRepo.selectByDimensionId(id);
            if (struct == null) {
                OrgStructMdPO structMdPO = new OrgStructMdPO();
                structMdPO.setOrgCode(orgDimensionCfPO.getOrgDimensionCode());
                structMdPO.setOrgName(orgDimensionCfPO.getOrgDimensionName());
                structMdPO.setOrgDimensionId(id);
                orgStructMdRepo.insert(structMdPO);
            }
        }
    }

    public List<OrgDimensionDTO> queryEnableList() {
        List<OrgDimensionCfPO> orgDimensionCfPOS = orgDimensionCfRepo.queryEnableList();
        return orgDimensionConverter.convert(orgDimensionCfPOS);
    }
}

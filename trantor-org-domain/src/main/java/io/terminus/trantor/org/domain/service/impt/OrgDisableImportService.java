package io.terminus.trantor.org.domain.service.impt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import io.terminus.gei.service.api.CustomImportService;
import io.terminus.gei.service.api.entity.ImportResult;
import io.terminus.gei.service.api.entity.ImportSliceData;
import io.terminus.trantor.org.infrastructure.repo.OrgDimensionCfRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgStructMdRepo;
import io.terminus.trantor.org.spi.dict.OrgStatusDict;
import io.terminus.trantor.org.spi.model.po.OrgDimensionCfPO;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2023-12-04 11:13
 * 组织禁用导入
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrgDisableImportService implements CustomImportService {

    private final OrgDimensionCfRepo orgDimensionCfRepo;
    private final OrgStructMdRepo orgStructMdRepo;

    @Override
    public List<ImportResult> importData(ImportSliceData customImportSliceData) {
        log.info("组织禁用导入的数据为:{}", JSON.toJSONString(customImportSliceData));
        JSONObject jsonObject = (JSONObject) JSON.parse(customImportSliceData.getImportContext());
        String orgDimensionCode = jsonObject.getString("orgDimensionCode");
        if (CollectionUtils.isEmpty(customImportSliceData.getSliceData())) {
            return Lists.newArrayList();
        }
        // 组织维度
        OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(orgDimensionCode);
        List<ImportResult> res = new ArrayList<>();
        List<Map<String, Object>> dataList = customImportSliceData.getSliceData();
        // 获取组织编码
        Set<String> orgCodeSet = dataList.stream().map(t -> String.valueOf(t.get("orgStructCode"))).collect(Collectors.toSet());
        Map<String, OrgStructMdPO> orgStructMdExistMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(orgCodeSet)) {
            List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryByCodes(orgCodeSet, orgDimensionCfPO.getId());
            if (!CollectionUtils.isEmpty(orgStructMdPOS)) {
                orgStructMdExistMap = orgStructMdPOS.stream().collect(Collectors.toMap(OrgStructMdPO::getOrgCode, Function.identity()));
            }
        }
        // 数据基础校验
        int errorCount = 0;
        for (Map<String, Object> map : dataList) {
            ImportResult customImportResult = new ImportResult();
            customImportResult.setSheetNo(new Integer(String.valueOf(map.get("_sheetNo"))));
            customImportResult.setRowIndex(new Integer(String.valueOf(map.get("_rowNo"))));
            customImportResult.setSuccessImported(false);
            boolean isSuccess = Boolean.TRUE;
            if (Objects.isNull(map.get("orgStructCode"))) {
                customImportResult.setErrMessage("织编码不能为空");
                res.add(customImportResult);
                errorCount++;
                continue;
            }
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                if (key.equals("_sheetName") || key.equals("_rowNo") || key.equals("version") || key.equals("_sheetNo")) {
                    continue;
                }
                String value = String.valueOf(entry.getValue());
                // 组织编码
                if (key.equals("orgStructCode")) {
                    OrgStructMdPO orgStructMdPO = orgStructMdExistMap.get(value);
                    if (Objects.isNull(orgStructMdPO)) {
                        customImportResult.setErrMessage("编码不存在");
                        res.add(customImportResult);
                        errorCount++;
                        isSuccess = Boolean.FALSE;
                        break;
                    }
                }
            }
            if (isSuccess) {
                customImportResult.setSuccessImported(Boolean.TRUE);
                customImportResult.setErrMessage("数据校验通过");
                res.add(customImportResult);
            }
        }
        if (errorCount > 0) {
            return res;
        } else {
            res = new ArrayList<>();
        }
        // 数据处理
        for (Map<String, Object> map : dataList) {
            List<OrgStructMdPO> orgStructChildList = new ArrayList<>();
            ImportResult customImportResult = new ImportResult();
            customImportResult.setSheetNo(new Integer(String.valueOf(map.get("_sheetNo"))));
            customImportResult.setRowIndex(new Integer(String.valueOf(map.get("_rowNo"))));
            customImportResult.setSuccessImported(true);
            res.add(customImportResult);
            OrgStructMdPO orgStructMdPO = orgStructMdExistMap.get(map.get("orgStructCode"));
            if (OrgStatusDict.ENABLED.equals(orgStructMdPO.getOrgStatus())) {
                getChildren(orgStructChildList, orgStructMdPO.getId());
                if (!CollectionUtils.isEmpty(orgStructChildList)) {
                    for (OrgStructMdPO child : orgStructChildList) {
                        if (OrgStatusDict.ENABLED.equals(child.getOrgStatus())) {
                            child.setOrgStatus(OrgStatusDict.DISABLED);
                            orgStructMdRepo.updateById(child);
                        }
                        if (OrgStatusDict.DRAFT.equals(child.getOrgStatus()) || OrgStatusDict.INACTIVE.equals(child.getOrgStatus())) {
                            orgStructMdRepo.deleteById(child);
                        }
                    }
                }
                orgStructMdPO.setOrgStatus(OrgStatusDict.DISABLED);
                orgStructMdRepo.updateById(orgStructMdPO);
            }
        }
        return res;
    }

    private void getChildren(List<OrgStructMdPO> orgStructMdPOList, Long parentId) {
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryByPid(parentId);
        if (!CollectionUtils.isEmpty(orgStructMdPOS)) {
            for (OrgStructMdPO orgStructMdPO : orgStructMdPOS) {
                orgStructMdPOList.add(orgStructMdPO);
                getChildren(orgStructMdPOList, orgStructMdPO.getId());
            }
        }
    }

}

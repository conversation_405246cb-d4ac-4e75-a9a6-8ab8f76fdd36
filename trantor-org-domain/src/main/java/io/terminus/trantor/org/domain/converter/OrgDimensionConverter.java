package io.terminus.trantor.org.domain.converter;

import io.terminus.trantor.org.spi.model.dto.OrgDimensionDTO;
import io.terminus.trantor.org.spi.model.po.OrgDimensionCfPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-11-17 11:45
 */
@Mapper(componentModel = "spring")
public interface OrgDimensionConverter {

    List<OrgDimensionDTO> convert(List<OrgDimensionCfPO> orgDimensionCfPOS);
}

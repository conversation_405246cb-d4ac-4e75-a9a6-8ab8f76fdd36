package io.terminus.trantor.org.domain.service.strategy.attr;


import io.terminus.erp.strategy.Strategy;
import io.terminus.trantor.org.spi.dict.AttrDataTypeDict;
import io.terminus.trantor.org.spi.strategy.attr.AttrTypeTransStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * @author: 张博
 * @date: 2023-11-17 19:00
 */
@Component
@AllArgsConstructor
@Strategy(value = AttrTypeTransStrategy.class, implKey = AttrDataTypeDict.OBJECT)
public class ObjectAttrTypeTransStrategy implements AttrTypeTransStrategy<Long> {

    @Override
    public Boolean match(String attrDataType, Boolean isMulti) {
        return AttrDataTypeDict.OBJECT.equals(attrDataType) && !isMulti;
    }

    @Override
    public Long trans(String value) {
        if (StringUtils.hasText(value)) {
            return Long.parseLong(value);
        }
        return null;
    }
}

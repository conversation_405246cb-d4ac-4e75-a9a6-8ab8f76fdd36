package io.terminus.trantor.org.domain.service;

import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.model.Paging;
import io.terminus.iam.api.request.role.RolePageParams;
import io.terminus.iam.api.response.PageResult;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.sdk.client.IAMClient;
import io.terminus.trantor.org.spi.model.dto.PageDTO;
import io.terminus.trantor.org.spi.model.dto.RoleDTO;
import io.terminus.trantor.org.spi.model.dto.RolePageDTO;
import io.terminus.trantor2.common.iam.IamClientFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2023-09-21 10:09
 */
@Service
@RequiredArgsConstructor
public class RoleService {

    private final IamClientFactory iamClientFactory;

    public RoleDTO queryById(Long id) {
        try {
            IAMClient iamClient = iamClientFactory.getIamClient();
            Role role = iamClient.roleClient().getRoleById(id).execute();
            if (Objects.nonNull(role)) {
                RoleDTO roleDTO = new RoleDTO();
                roleDTO.setId(role.getId());
                roleDTO.setRoleName(role.getName());
                roleDTO.setRoleId(role.getId());
                return roleDTO;
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return null;
    }

    public Paging<RoleDTO> frontRolePaging(RolePageDTO request) {
        Paging<RoleDTO> paging = new Paging<>();
        try {
            RolePageParams rolePageParams = new RolePageParams();
            rolePageParams.setNo(request.getPageNo());
            rolePageParams.setSize(request.getPageSize());
            if (StringUtils.hasText(request.getRoleName())) {
                rolePageParams.setName(request.getRoleName());
            }
            if (StringUtils.hasText(request.getKey())) {
                rolePageParams.setKey(request.getKey());
            }
            if (Objects.nonNull(request.getEnabled())) {
                rolePageParams.setEnabled(request.getEnabled());
            }
            IAMClient iamClient = iamClientFactory.getIamClient();
            PageResult<Role> pageResult = iamClient.roleClient().pageRole(rolePageParams).execute();
            if (CollectionUtils.isEmpty(pageResult.getData())) {
                return Paging.empty();
            }
            List<RoleDTO> roleDTOList = pageResult.getData().stream().map(t -> {
                RoleDTO roleDTO = new RoleDTO();
                roleDTO.setId(t.getId());
                roleDTO.setRoleName(t.getName());
                roleDTO.setRoleId(t.getId());
                roleDTO.setDesc(t.getDesc());
                roleDTO.setEnabled(t.getEnabled());
                return roleDTO;
            }).collect(Collectors.toList());
            paging.setTotal(pageResult.getTotal());
            paging.setData(roleDTOList);
            return paging;
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    public Paging<RoleDTO> rolePaging(PageDTO request) {
        Paging<RoleDTO> paging = new Paging<>();
        try {
            Map<String, Object> pageable = request.getPageable();
            Integer pageNo = (Integer) pageable.get("pageNo");
            Integer pageSize = (Integer) pageable.get("pageSize");

            RolePageParams rolePageParams = new RolePageParams();
            rolePageParams.setNo(pageNo);
            rolePageParams.setSize(pageSize);
            IAMClient iamClient = iamClientFactory.getIamClient();
            PageResult<Role> pageResult = iamClient.roleClient().pageRole(rolePageParams).execute();
            if (CollectionUtils.isEmpty(pageResult.getData())) {
                return Paging.empty();
            }
            List<RoleDTO> roleDTOList = pageResult.getData().stream().map(t -> {
                RoleDTO roleDTO = new RoleDTO();
                roleDTO.setId(t.getId());
                roleDTO.setRoleName(t.getName());
                roleDTO.setRoleId(t.getId());
                roleDTO.setDesc(t.getDesc());
                roleDTO.setEnabled(t.getEnabled());
                return roleDTO;
            }).collect(Collectors.toList());
            paging.setTotal(pageResult.getTotal());
            paging.setData(roleDTOList);
            return paging;
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }
}

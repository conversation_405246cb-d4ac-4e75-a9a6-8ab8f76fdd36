package io.terminus.trantor.org.domain.converter;


import io.terminus.trantor.org.spi.model.dto.OrgStructMdHistoryTreeDTO;
import io.terminus.trantor.org.spi.model.po.OrgStructEditionMdPO;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import org.mapstruct.Mapper;

import java.util.List;


@Mapper(componentModel = "spring")
public interface OrgStructMdAdjustHisConverter {
    List<OrgStructEditionMdPO> converHistory(List<OrgStructMdPO> list);


    List<OrgStructMdHistoryTreeDTO> converQueryHistory(List<OrgStructEditionMdPO> result);
}

package io.terminus.trantor.org.domain.converter;


import io.terminus.trantor.org.spi.model.dto.OrgBizTypeLinkDTO;
import io.terminus.trantor.org.spi.model.dto.OrgUnitDTO;
import io.terminus.trantor.org.spi.model.dto.OrgUnitInfoDTO;
import io.terminus.trantor.org.spi.model.dto.OrgUnitSaveDTO;
import io.terminus.trantor.org.spi.model.po.OrgBizTypeLinkPO;
import io.terminus.trantor.org.spi.model.po.OrgUnitPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-02-28 13:49
 */
@Mapper(componentModel = "spring")
public interface OrgUnitConverter {

    OrgUnitDTO convert(OrgUnitPO orgUnitPO);

    List<OrgUnitDTO> convert(List<OrgUnitPO> orgUnitPOS);

    OrgUnitPO convert(OrgUnitSaveDTO request);

    List<OrgBizTypeLinkDTO> convertLink(List<OrgBizTypeLinkPO> list);

    OrgUnitInfoDTO convertInfo(OrgUnitPO orgUnitPO);
}

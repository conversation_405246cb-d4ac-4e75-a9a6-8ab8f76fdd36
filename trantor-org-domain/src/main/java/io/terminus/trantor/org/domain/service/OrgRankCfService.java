package io.terminus.trantor.org.domain.service;

import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdRequest;
import io.terminus.trantor.org.domain.constant.OrgConstants;
import io.terminus.trantor.org.domain.converter.OrgRankCfConverter;
import io.terminus.trantor.org.infrastructure.gateway.OrgMdGateWay;
import io.terminus.trantor.org.infrastructure.repo.OrgRankCfRepo;
import io.terminus.trantor.org.spi.model.dto.OrgRankCfDTO;
import io.terminus.trantor.org.spi.model.dto.OrgRankCfQueryDTO;
import io.terminus.trantor.org.spi.model.dto.dict.DictHeadPageQueryDTO;
import io.terminus.trantor.org.spi.model.dto.dict.GenDictHeadDTO;
import io.terminus.trantor.org.spi.model.dto.dict.GenDictItemDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class OrgRankCfService {

    private final OrgRankCfRepo orgRankCfRepo;
    private final OrgRankCfConverter orgRankCfConverter;
    private final OrgMdGateWay orgMdGateWay;

    @Value("${trantor.org.version:v1}")
    private String version;

    public List<OrgRankCfDTO> findAll(OrgRankCfQueryDTO request) {
        if (OrgConstants.ORG_VERSION_V1.equals(version)) {
            return orgRankCfConverter.convertDTO(orgRankCfRepo.findAll(request));
        }
        DictHeadPageQueryDTO dictHeadPageQueryDTO = new DictHeadPageQueryDTO();
        dictHeadPageQueryDTO.setCode("orgempgroup");
        Paging<GenDictHeadDTO> dictHeadPaging = orgMdGateWay.dictHeadPaging(dictHeadPageQueryDTO);
        if (CollectionUtils.isEmpty(dictHeadPaging.getData())) {
            return Collections.emptyList();
        }
        GenDictHeadDTO genDictHeadDTO = dictHeadPaging.getData().get(0);
        List<GenDictItemDTO> genDictItemDTOList = orgMdGateWay.findDictItemByDictHeadId(new IdRequest(genDictHeadDTO.getId()));
        if (CollectionUtils.isEmpty(genDictItemDTOList)) {
            return Collections.emptyList();
        }
        return genDictItemDTOList.stream().map(t -> {
            OrgRankCfDTO orgRankCfDTO = new OrgRankCfDTO();
            orgRankCfDTO.setId(t.getId());
            orgRankCfDTO.setName(t.getName());
            orgRankCfDTO.setCode(t.getCode());
            return orgRankCfDTO;
        }).collect(Collectors.toList());
    }
}

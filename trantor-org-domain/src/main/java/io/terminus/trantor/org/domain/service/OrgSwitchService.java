package io.terminus.trantor.org.domain.service;

import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.request.IdRequest;
import io.terminus.trantor.org.domain.constant.OrgConstants;
import io.terminus.trantor.org.domain.converter.OrgSwitchConverter;
import io.terminus.trantor.org.infrastructure.repo.*;
import io.terminus.trantor.org.spi.model.dto.JudgeModelOrgSwitchDTO;
import io.terminus.trantor.org.spi.model.dto.JudgeUserInOrgDTO;
import io.terminus.trantor.org.spi.model.dto.OrgSwitchModelSaveDTO;
import io.terminus.trantor.org.spi.model.dto.OrgSwitchUserComDTO;
import io.terminus.trantor.org.spi.model.po.*;
import io.terminus.trantor.org.spi.msg.OrgMsg;
import io.terminus.trantor2.common.TrantorContext;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2023-12-11 16:14
 */
@Service
@RequiredArgsConstructor
public class OrgSwitchService {

    private final OrgSwitchModelRepo orgSwitchModelRepo;
    private final OrgSwitchListRepo orgSwitchListRepo;
    private final OrgSwitchConverter orgSwitchConverter;
    private final OrgStructMdRepo orgStructMdRepo;
    private final EmployeeOrgSwitchRepo employeeOrgSwitchRepo;
    private final EmployeeOrgLinkOrgSwitchRepo employeeOrgLinkOrgSwitchRepo;
    private final OrgStructMdService orgStructMdService;
    private final RedisTemplate<String, Object> orgRedisTemplate;

    public Boolean judgeUserIsExistCurrentOrg(JudgeUserInOrgDTO request) {
        List<EmployeePO> employeePOS = employeeOrgSwitchRepo.queryByUserId(request.getCurrentUserId());
        if (CollectionUtils.isEmpty(employeePOS)) {
            return Boolean.FALSE;
        }
        Set<Long> employeeIds = employeePOS.stream().map(EmployeePO::getId).collect(Collectors.toSet());
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkOrgSwitchRepo.queryByEmployeeIds(employeeIds);
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return Boolean.TRUE;
        }
        Set<Long> allOrgIds = new HashSet<>();
        Set<Long> orgIdSet = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(orgIdSet)) {
            return Boolean.FALSE;
        }
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.selectBatchIds(orgIdSet);
        if (CollectionUtils.isEmpty(orgStructMdPOS)) {
            return Boolean.FALSE;
        }
        for (OrgStructMdPO orgStructMdPO : orgStructMdPOS) {
            if (!CollectionUtils.isEmpty(orgStructMdPO.getPath())) {
                allOrgIds.addAll(orgStructMdPO.getPath());
            }
        }
        if (allOrgIds.contains(request.getOriginOrgId())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public Boolean judgeModelOpenOrgSwitch(JudgeModelOrgSwitchDTO request) {
        if (!StringUtils.hasText(request.getModelKey())) {
            return Boolean.FALSE;
        }
        if (request.getModelKey().contains("$")) {
            int index = request.getModelKey().indexOf("$");
            request.setModelKey(request.getModelKey().substring(index + 1));
        }
        Object isOpen = orgRedisTemplate.opsForValue().get(OrgConstants.ORG_SWITCH_MODEL_CACHE_PREFIX + request.getModelKey());
        if (Objects.nonNull(isOpen)) {
            return (Boolean) isOpen;
        }
        if ("org_switch_model_cf".equals(request.getModelKey())) {
            return Boolean.FALSE;
        }
        OrgSwitchModelCfPO orgSwitchModelCfPO = orgSwitchModelRepo.queryByModelKey(request.getModelKey());
        if (Objects.isNull(orgSwitchModelCfPO)) {
            orgRedisTemplate.opsForValue().set(OrgConstants.ORG_SWITCH_MODEL_CACHE_PREFIX + request.getModelKey(), Boolean.FALSE, 15, TimeUnit.DAYS);
            return Boolean.FALSE;
        }
        orgRedisTemplate.opsForValue().set(OrgConstants.ORG_SWITCH_MODEL_CACHE_PREFIX + request.getModelKey(), orgSwitchModelCfPO.getIsOpen(), 15, TimeUnit.DAYS);
        if (orgSwitchModelCfPO.getIsOpen()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public List<OrgSwitchUserComDTO> queryUserComList() {
        Long currentUserId = TrantorContext.getCurrentUserId();
        if (Objects.isNull(currentUserId)) {
            return Collections.emptyList();
        }
        List<EmployeePO> employeePOS = employeeOrgSwitchRepo.queryByUserId(currentUserId);
        if (CollectionUtils.isEmpty(employeePOS)) {
            return Collections.emptyList();
        }
        Set<Long> employeeIds = employeePOS.stream().map(EmployeePO::getId).collect(Collectors.toSet());
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkOrgSwitchRepo.queryByEmployeeIds(employeeIds);
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return Collections.emptyList();
        }
        Set<Long> allOrgIds = new HashSet<>();
        Set<Long> orgIdSet = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toSet());
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.selectBatchIds(orgIdSet);
        if (CollectionUtils.isEmpty(orgStructMdPOS)) {
            return Collections.emptyList();
        }
        for (OrgStructMdPO orgStructMdPO : orgStructMdPOS) {
            if (!CollectionUtils.isEmpty(orgStructMdPO.getPath())) {
                allOrgIds.addAll(orgStructMdPO.getPath());
            }
        }
        if (CollectionUtils.isEmpty(allOrgIds)) {
            return Collections.emptyList();
        }
        List<OrgSwitchListCfPO> orgSwitchListCfPOS = orgSwitchListRepo.queryByOrgIds(allOrgIds);
        if (CollectionUtils.isEmpty(orgSwitchListCfPOS)) {
            return Collections.emptyList();
        }
        return orgSwitchListCfPOS.stream().map(t -> {
            OrgSwitchUserComDTO orgSwitchUserComDTO = new OrgSwitchUserComDTO();
            orgSwitchUserComDTO.setId(t.getSwitchOrgId());
            orgSwitchUserComDTO.setName(t.getSwitchName());
            orgSwitchUserComDTO.setIsHeadquarters(t.getSwitchHeadquarters());
            return orgSwitchUserComDTO;
        }).collect(Collectors.toList());
    }

    public OrgSwitchModelSaveDTO switchModelSave(OrgSwitchModelSaveDTO request) {
        if (StringUtils.hasText(request.getModelKey())) {
            if (request.getModelKey().contains("$")) {
                int index = request.getModelKey().indexOf("$");
                request.setModelKey(request.getModelKey().substring(index + 1));
            }
            orgRedisTemplate.delete(OrgConstants.ORG_SWITCH_MODEL_CACHE_PREFIX + request.getModelKey());
        }
        if (Objects.isNull(request.getId())) {
            OrgSwitchModelCfPO orgSwitchModelPO = orgSwitchModelRepo.queryByModelKey(request.getModelKey());
            if (Objects.nonNull(orgSwitchModelPO)) {
                throw new BusinessException(OrgMsg.ORG_SWiTCH_MODEL_IS_EXIST);
            }
            OrgSwitchModelCfPO orgSwitchModelCfPO = orgSwitchConverter.converter(request);
            orgSwitchModelRepo.insert(orgSwitchModelCfPO);
            return request;
        }
        OrgSwitchModelCfPO orgSwitchModelCfPO = orgSwitchConverter.converter(request);
        orgSwitchModelRepo.updateById(orgSwitchModelCfPO);
        return request;
    }

    public void switchModelDelete(IdRequest request) {
        OrgSwitchModelCfPO orgSwitchModelCfPO = orgSwitchModelRepo.selectById(request.getId());
        if (Objects.isNull(orgSwitchModelCfPO)) {
            return;
        }
        if (orgSwitchModelCfPO.getModelKey().contains("$")) {
            int index = orgSwitchModelCfPO.getModelKey().indexOf("$");
            orgSwitchModelCfPO.setModelKey(orgSwitchModelCfPO.getModelKey().substring(index + 1));
        }
        orgRedisTemplate.delete(OrgConstants.ORG_SWITCH_MODEL_CACHE_PREFIX + orgSwitchModelCfPO.getModelKey());
        orgSwitchModelRepo.deleteById(orgSwitchModelCfPO);
    }

}

package io.terminus.trantor.org.domain.service;

import com.google.common.collect.Sets;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.trantor.org.infrastructure.repo.OrgLegalPersonRelationCfRepo;
import io.terminus.trantor.org.spi.dict.OrgLegalStatusDict;
import io.terminus.trantor.org.spi.model.dto.LegalComDTO;
import io.terminus.trantor.org.spi.model.po.OrgLegalPersonRelationCfPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2023-11-28 19:17
 */
@Service
@RequiredArgsConstructor
public class LegalComService {

    private final OrgLegalPersonRelationCfRepo orgLegalPersonRelationCfRepo;

    public List<LegalComDTO> queryLegalComByOrgStructIds(IdsRequest request) {
        List<OrgLegalPersonRelationCfPO> orgLegalPersonRelationCfPOS = orgLegalPersonRelationCfRepo.queryByOrgStructIds(Sets.newHashSet(request.getIds()), OrgLegalStatusDict.ENABLED);
        if (CollectionUtils.isEmpty(orgLegalPersonRelationCfPOS)) {
            return Collections.emptyList();
        }
        Map<Long, List<OrgLegalPersonRelationCfPO>> groupByOrgStructId = orgLegalPersonRelationCfPOS.stream().collect(Collectors.groupingBy(OrgLegalPersonRelationCfPO::getOrgRelationUnitId));
        List<LegalComDTO> legalComDTOS = new ArrayList<>(request.getIds().size());
        for (Long id : request.getIds()) {
            LegalComDTO legalComDTO = new LegalComDTO();
            legalComDTO.setOrgStructId(id);
            List<OrgLegalPersonRelationCfPO> orgLegalPersonRelationCfPOS1 = groupByOrgStructId.get(id);
            if (!CollectionUtils.isEmpty(orgLegalPersonRelationCfPOS1)) {
                OrgLegalPersonRelationCfPO orgLegalPersonRelationCfPO = orgLegalPersonRelationCfPOS1.get(0);
                legalComDTO.setComId(orgLegalPersonRelationCfPO.getGenComTypeId());
            }
            legalComDTOS.add(legalComDTO);
        }
        return legalComDTOS;
    }
}

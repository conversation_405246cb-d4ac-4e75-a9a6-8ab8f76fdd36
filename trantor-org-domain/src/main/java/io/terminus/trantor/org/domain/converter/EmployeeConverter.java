package io.terminus.trantor.org.domain.converter;

import io.terminus.trantor.org.spi.model.dto.EmployeeDTO;
import io.terminus.trantor.org.spi.model.dto.EmployeeNoticeSceneLinkDTO;
import io.terminus.trantor.org.spi.model.dto.EmployeeOrgLinkDTO;
import io.terminus.trantor.org.spi.model.dto.EmployeeSaveDTO;
import io.terminus.trantor.org.spi.model.po.EmployeeNoticeSceneLinkPO;
import io.terminus.trantor.org.spi.model.po.EmployeeOrgLinkPO;
import io.terminus.trantor.org.spi.model.po.EmployeePO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-02-28 09:48
 */
@Mapper(componentModel = "spring")
public interface EmployeeConverter {

    EmployeePO convert(EmployeeSaveDTO request);

    EmployeeSaveDTO convertSaveDTO(EmployeePO employeePO);

    EmployeeDTO convertDTO(EmployeePO employeePO);

    List<EmployeeDTO> convertDTO(List<EmployeePO> list);

    List<EmployeeOrgLinkDTO> convert(List<EmployeeOrgLinkPO> list);

    List<EmployeeNoticeSceneLinkDTO> covert(List<EmployeeNoticeSceneLinkPO> list);
}

package io.terminus.trantor.org.domain.service;

import com.google.common.collect.Sets;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.trantor.org.domain.constant.OrgConstants;
import io.terminus.trantor.org.domain.converter.OrgRankCfConverter;
import io.terminus.trantor.org.infrastructure.gateway.OrgMdGateWay;
import io.terminus.trantor.org.infrastructure.repo.OrgRankCfRepo;
import io.terminus.trantor.org.spi.model.dto.OrgRankCfDTO;
import io.terminus.trantor.org.spi.model.dto.dict.GenDictItemDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @author: 张博
 * @date: 2023-02-28 16:36
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrgRankService {

    private final OrgRankCfRepo orgRankCfRepo;
    private final OrgRankCfConverter converter;
    private final OrgMdGateWay orgMdGateWay;

    @Value("${trantor.org.version:v1}")
    private String version;

    public OrgRankCfDTO queryRankById(IdRequest request) {
        if (Objects.isNull(request.getId())) {
            return null;
        }
        if (OrgConstants.ORG_VERSION_V1.equals(version)) {
            return converter.convert(orgRankCfRepo.selectById(request.getId()));
        }
        IdsRequest idsRequest = new IdsRequest();
        idsRequest.setIds(Sets.newHashSet(request.getId()));
        List<GenDictItemDTO> dictItemDTOList = orgMdGateWay.findDictItemByIds(idsRequest);
        if (CollectionUtils.isEmpty(dictItemDTOList)) {
            return null;
        }
        GenDictItemDTO genDictItemDTO = dictItemDTOList.get(0);
        OrgRankCfDTO orgRankCfDTO = new OrgRankCfDTO();
        orgRankCfDTO.setId(genDictItemDTO.getId());
        orgRankCfDTO.setName(genDictItemDTO.getName());
        orgRankCfDTO.setCode(genDictItemDTO.getCode());
        orgRankCfDTO.setStatus(genDictItemDTO.getStatus());
        return orgRankCfDTO;
    }
}

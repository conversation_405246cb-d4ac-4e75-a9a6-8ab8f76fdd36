package io.terminus.trantor.org.domain.service.impt;

import com.google.common.collect.Lists;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.common.api.response.Response;
import io.terminus.gei.api.constant.IndexedColors;
import io.terminus.gei.api.facade.GeiTemplateFacade;
import io.terminus.gei.api.model.template.*;
import io.terminus.trantor.org.infrastructure.gateway.OrgMdGateWay;
import io.terminus.trantor.org.infrastructure.repo.OrgBusinessTypeRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgDimensionBusinessLinkRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgDimensionCfRepo;
import io.terminus.trantor.org.spi.dict.AttrDataTypeDict;
import io.terminus.trantor.org.spi.dict.OrgStatusDict;
import io.terminus.trantor.org.spi.dict.OrgStructImportTypeDict;
import io.terminus.trantor.org.spi.model.dto.OrgImportHeaderContextDTO;
import io.terminus.trantor.org.spi.model.dto.OrgImportHeaderDTO;
import io.terminus.trantor.org.spi.model.dto.OrgStructImportTemplateDTO;
import io.terminus.trantor.org.spi.model.dto.OrgStructImportTemplateQueryDTO;
import io.terminus.trantor.org.spi.model.dto.attr.GenAttrDTO;
import io.terminus.trantor.org.spi.model.dto.attr.GenAttrGroupDTO;
import io.terminus.trantor.org.spi.model.dto.dict.GenDictItemDTO;
import io.terminus.trantor.org.spi.model.po.OrgBusinessTypePO;
import io.terminus.trantor.org.spi.model.po.OrgDimensionBusinessLinkPO;
import io.terminus.trantor.org.spi.model.po.OrgDimensionCfPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2023-11-30 19:58
 */
@Service
@RequiredArgsConstructor
public class OrgStructImportService {

    private final OrgDimensionCfRepo orgDimensionCfRepo;
    private final GeiTemplateFacade geiTemplateFacade;
    private final OrgMdGateWay orgMdGateWay;
    private final OrgBusinessTypeRepo orgBusinessTypeRepo;
    private final OrgDimensionBusinessLinkRepo orgDimensionBusinessLinkRepo;

    public OrgStructImportTemplateDTO findImportTemplate(OrgStructImportTemplateQueryDTO request) {
        if (Objects.isNull(request.getOrgDimensionCode())) {
            throw new BusinessException("Org.org.import.please.choose.dimension");
        }
        OrgStructImportTemplateDTO orgStructImportTemplateDTO = new OrgStructImportTemplateDTO();
        List<OrgImportHeaderContextDTO> importHeaderContextList = new ArrayList<>();
        // 新增组织
        if (OrgStructImportTypeDict.ORG_CREATE.equals(request.getTemplateType()) || OrgStructImportTypeDict.ORG_UPDATE.equals(request.getTemplateType())) {
            OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(request.getOrgDimensionCode());
            if (Objects.isNull(orgDimensionCfPO)) {
                return null;
            }
            List<OrgDimensionBusinessLinkPO> orgDimensionBusinessLinkPOS = orgDimensionBusinessLinkRepo.queryGroupIdLinkByDimensionId(orgDimensionCfPO.getId());
            if (CollectionUtils.isEmpty(orgDimensionBusinessLinkPOS)) {
                return null;
            }
            Set<Long> orgBusinessIdSet = orgDimensionBusinessLinkPOS.stream().map(OrgDimensionBusinessLinkPO::getOrgBusinessTypeId).collect(Collectors.toSet());
            List<OrgBusinessTypePO> orgBusinessTypePOS = orgBusinessTypeRepo.selectBatchIds(orgBusinessIdSet);
            if (CollectionUtils.isEmpty(orgBusinessTypePOS)) {
                return null;
            }
            List<OrgBusinessTypePO> enabledOrgBusinessTypePOS = orgBusinessTypePOS.stream().filter(t -> OrgStatusDict.ENABLED.equals(t.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(enabledOrgBusinessTypePOS)) {
                return null;
            }
            Set<Long> attrGroupIdSet = enabledOrgBusinessTypePOS.stream().map(OrgBusinessTypePO::getAttrGroupId).collect(Collectors.toSet());
            List<GenAttrGroupDTO> genAttrGroupDTOList = orgMdGateWay.findAttrGroupByIds(new IdsRequest(attrGroupIdSet));
            if (CollectionUtils.isEmpty(genAttrGroupDTOList)) {
                return null;
            }
            List<GenAttrGroupDTO> enableAttrGroupList = genAttrGroupDTOList.stream().filter(t -> OrgStatusDict.ENABLED.equals(t.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(enableAttrGroupList)) {
                return null;
            }
            Map<Long, List<GenAttrDTO>> attrGroupAttrMap = new HashMap<>(attrGroupIdSet.size());
            for (GenAttrGroupDTO genAttrGroupDTO : enableAttrGroupList) {
                List<GenAttrDTO> genAttrDTOList = orgMdGateWay.findAttrByAttrGroupId(new IdRequest(genAttrGroupDTO.getId()));
                attrGroupAttrMap.put(genAttrGroupDTO.getId(), genAttrDTOList);
            }
            WorkbookHeadInfo workbookHeadInfo = new WorkbookHeadInfo();
            if (OrgStructImportTypeDict.ORG_CREATE.equals(request.getTemplateType())) {
                workbookHeadInfo.setTemplateName("组织新增导入模版");
            }
            if (OrgStructImportTypeDict.ORG_UPDATE.equals(request.getTemplateType())) {
                workbookHeadInfo.setTemplateName("组织调整导入模版");
            }
            List<SheetHeadInfo> sheetHeadInfoList = new ArrayList<>(enableAttrGroupList.size());
            OrgImportHeaderContextDTO orgImportHeaderContextDTO = new OrgImportHeaderContextDTO();
            List<OrgImportHeaderDTO> headerItems = new ArrayList<>();
            orgImportHeaderContextDTO.setHeaderItems(headerItems);
            importHeaderContextList.add(orgImportHeaderContextDTO);
            if (OrgStructImportTypeDict.ORG_CREATE.equals(request.getTemplateType())) {
                orgImportHeaderContextDTO.setHeaderRow(3);
            }
            if (OrgStructImportTypeDict.ORG_UPDATE.equals(request.getTemplateType())) {
                orgImportHeaderContextDTO.setHeaderRow(3);
            }
            SheetHeadInfo sheetHeadInfo = new SheetHeadInfo();
            // 隐藏行
            sheetHeadInfo.setSheetName("组织导入信息");
            Set<Integer> hiddenHeadRowIndexSet = new HashSet<>(1);
            hiddenHeadRowIndexSet.add(2);
            sheetHeadInfo.setHiddenHeadRowIndexSet(hiddenHeadRowIndexSet);
            List<SheetColumnInfo> sheetColumnInfoList = new ArrayList<>();
            List<List<String>> headerList = assembleCreateDefaultHead(request.getTemplateType(), sheetHeadInfo,
                    attrGroupAttrMap, orgBusinessTypePOS, sheetColumnInfoList, headerItems);
            sheetHeadInfo.setHeaderList(headerList);
            sheetHeadInfo.setSheetColumnInfoList(sheetColumnInfoList);
            sheetHeadInfoList.add(sheetHeadInfo);
            workbookHeadInfo.setSheetHeadInfoList(sheetHeadInfoList);
            Response<GenerateTemplateResultDTO> generateTemplateResultDTOResponse = geiTemplateFacade.generateTemplate(workbookHeadInfo);
            if (generateTemplateResultDTOResponse.isSuccess()) {
                GenerateTemplateResultDTO generateTemplateResultDTO = generateTemplateResultDTOResponse.getData();
                orgStructImportTemplateDTO.setTemplateUrl(generateTemplateResultDTO.getTemplateFileUrl());
            } else {
                throw new BusinessException(generateTemplateResultDTOResponse.getErrorMsg());
            }
        }
        if (OrgStructImportTypeDict.ORG_DISABLED.equals(request.getTemplateType())) {
            OrgImportHeaderContextDTO orgImportHeaderContextDTO = new OrgImportHeaderContextDTO();
            List<OrgImportHeaderDTO> headerItems = new ArrayList<>();
            orgImportHeaderContextDTO.setHeaderItems(headerItems);
            orgImportHeaderContextDTO.setHeaderRow(2);
            importHeaderContextList.add(orgImportHeaderContextDTO);
            WorkbookHeadInfo workbookHeadInfo = new WorkbookHeadInfo();
            workbookHeadInfo.setTemplateName("组织停用导入模版");
            SheetHeadInfo sheetHeadInfo = new SheetHeadInfo();
            Set<Integer> hiddenHeadRowIndexSet = new HashSet<>(1);
            hiddenHeadRowIndexSet.add(1);
            sheetHeadInfo.setHiddenHeadRowIndexSet(hiddenHeadRowIndexSet);
            List<HeadCellColorInfo> headCellColorInfos = new ArrayList<>(2);
            List<List<String>> headerList = new ArrayList<>();
            HeadCellColorInfo headCellColorInfo = new HeadCellColorInfo();
            headCellColorInfo.setStartRowIndex(0);
            headCellColorInfo.setEndRowIndex(0);
            headCellColorInfo.setStartColumnIndex(0);
            headCellColorInfo.setEndColumnIndex(0);
            headCellColorInfo.setFillForegroundColor(IndexedColors.SKY_BLUE);
            headCellColorInfo.setFontColor(IndexedColors.RED);
            headCellColorInfos.add(headCellColorInfo);

            HeadCellColorInfo headCellColorInfo3 = new HeadCellColorInfo();
            headCellColorInfo3.setStartRowIndex(0);
            headCellColorInfo3.setEndRowIndex(0);
            headCellColorInfo3.setStartColumnIndex(1);
            headCellColorInfo3.setEndColumnIndex(1);
            headCellColorInfo3.setFillForegroundColor(IndexedColors.SKY_BLUE);
            headCellColorInfo3.setFontColor(IndexedColors.BLACK);
            headCellColorInfos.add(headCellColorInfo3);

            OrgImportHeaderDTO orgStructCode = new OrgImportHeaderDTO();
            orgStructCode.setField("orgStructCode");
            orgStructCode.setName("组织编码,orgStructCode");
            headerItems.add(orgStructCode);
            headerList.add(Lists.newArrayList("组织编码", "orgStructCode"));

            OrgImportHeaderDTO orgStructName = new OrgImportHeaderDTO();
            orgStructName.setField("orgStructName");
            orgStructName.setName("组织名称,orgStructName");
            headerItems.add(orgStructName);
            headerList.add(Lists.newArrayList("组织名称", "orgStructName"));

            sheetHeadInfo.setSheetName("组织停用信息");
            sheetHeadInfo.setHeaderList(headerList);
            sheetHeadInfo.setHeadCellColorInfos(headCellColorInfos);
            workbookHeadInfo.setSheetHeadInfoList(Lists.newArrayList(sheetHeadInfo));
            Response<GenerateTemplateResultDTO> generateTemplateResultDTOResponse = geiTemplateFacade.generateTemplate(workbookHeadInfo);
            if (generateTemplateResultDTOResponse.isSuccess()) {
                GenerateTemplateResultDTO generateTemplateResultDTO = generateTemplateResultDTOResponse.getData();
                orgStructImportTemplateDTO.setTemplateUrl(generateTemplateResultDTO.getTemplateFileUrl());
            } else {
                throw new BusinessException(generateTemplateResultDTOResponse.getErrorMsg());
            }
        }
        orgStructImportTemplateDTO.setImportHeaderContextList(importHeaderContextList);
        return orgStructImportTemplateDTO;
    }

    private List<List<String>> assembleCreateDefaultHead(String type, SheetHeadInfo sheetHeadInfo,
                                                         Map<Long, List<GenAttrDTO>> attrGroupAttrMap, List<OrgBusinessTypePO> orgBusinessTypePOS,
                                                         List<SheetColumnInfo> sheetColumnInfoList, List<OrgImportHeaderDTO> headerItems) {
        List<List<String>> headerList = new ArrayList<>();
        List<HeadCellColorInfo> headCellColorInfos = new ArrayList<>();
        headerList.add(Lists.newArrayList("组织基本信息", "父组织编码", "orgStructParentCode"));
        OrgImportHeaderDTO orgStructParentCode = new OrgImportHeaderDTO();
        orgStructParentCode.setField("orgStructParentCode");
        orgStructParentCode.setName("组织基本信息,父组织编码,orgStructParentCode");
        headerItems.add(orgStructParentCode);
        if (OrgStructImportTypeDict.ORG_CREATE.equals(type)) {
            headerList.add(Lists.newArrayList("组织基本信息", "父组织名称", "orgStructParentName"));
            OrgImportHeaderDTO orgStructParentName = new OrgImportHeaderDTO();
            orgStructParentName.setField("orgStructParentName");
            orgStructParentName.setName("组织基本信息,父组织名称,orgStructParentName");
            headerItems.add(orgStructParentName);
            HeadCellColorInfo headCellColorInfo = new HeadCellColorInfo();
            headCellColorInfo.setStartRowIndex(1);
            headCellColorInfo.setEndRowIndex(1);
            headCellColorInfo.setStartColumnIndex(0);
            headCellColorInfo.setEndColumnIndex(0);
            headCellColorInfo.setFontColor(IndexedColors.RED);
            headCellColorInfos.add(headCellColorInfo);

            HeadCellColorInfo headCellColorInfo3 = new HeadCellColorInfo();
            headCellColorInfo3.setStartRowIndex(1);
            headCellColorInfo3.setEndRowIndex(1);
            headCellColorInfo3.setStartColumnIndex(1);
            headCellColorInfo3.setEndColumnIndex(1);
            headCellColorInfo3.setFontColor(IndexedColors.BLACK);
            headCellColorInfos.add(headCellColorInfo3);

            HeadCellColorInfo headCellColorInfo1 = new HeadCellColorInfo();
            headCellColorInfo1.setStartRowIndex(1);
            headCellColorInfo1.setEndRowIndex(1);
            headCellColorInfo1.setStartColumnIndex(2);
            headCellColorInfo1.setEndColumnIndex(4);
            headCellColorInfo1.setFontColor(IndexedColors.RED);
            headCellColorInfos.add(headCellColorInfo1);

            HeadCellColorInfo headCellColorInfo4 = new HeadCellColorInfo();
            headCellColorInfo4.setStartRowIndex(1);
            headCellColorInfo4.setEndRowIndex(1);
            headCellColorInfo4.setStartColumnIndex(5);
            headCellColorInfo4.setEndColumnIndex(5);
            headCellColorInfo4.setFontColor(IndexedColors.BLACK);
            headCellColorInfos.add(headCellColorInfo4);

            HeadCellColorInfo headCellColorInfo2 = new HeadCellColorInfo();
            headCellColorInfo2.setStartRowIndex(1);
            headCellColorInfo2.setEndRowIndex(1);
            headCellColorInfo2.setStartColumnIndex(6);
            headCellColorInfo2.setEndColumnIndex(7);
            headCellColorInfo2.setFillForegroundColor(IndexedColors.SKY_BLUE);
            headCellColorInfo2.setFontColor(IndexedColors.RED);
            headCellColorInfos.add(headCellColorInfo2);
        } else {
            HeadCellColorInfo headCellColorInfo = new HeadCellColorInfo();
            headCellColorInfo.setStartRowIndex(1);
            headCellColorInfo.setEndRowIndex(1);
            headCellColorInfo.setStartColumnIndex(0);
            headCellColorInfo.setEndColumnIndex(0);
            headCellColorInfo.setFontColor(IndexedColors.BLACK);
            headCellColorInfos.add(headCellColorInfo);

            HeadCellColorInfo headCellColorInfo3 = new HeadCellColorInfo();
            headCellColorInfo3.setStartRowIndex(1);
            headCellColorInfo3.setEndRowIndex(1);
            headCellColorInfo3.setStartColumnIndex(1);
            headCellColorInfo3.setEndColumnIndex(1);
            headCellColorInfo3.setFontColor(IndexedColors.RED);
            headCellColorInfos.add(headCellColorInfo3);

            HeadCellColorInfo headCellColorInfo1 = new HeadCellColorInfo();
            headCellColorInfo1.setStartRowIndex(1);
            headCellColorInfo1.setEndRowIndex(1);
            headCellColorInfo1.setStartColumnIndex(2);
            headCellColorInfo1.setEndColumnIndex(5);
            headCellColorInfo1.setFontColor(IndexedColors.BLACK);
            headCellColorInfos.add(headCellColorInfo1);
        }
        headerList.add(Lists.newArrayList("组织基本信息", "组织编码", "orgStructCode"));
        OrgImportHeaderDTO orgStructCode = new OrgImportHeaderDTO();
        orgStructCode.setField("orgStructCode");
        orgStructCode.setName("组织基本信息,组织编码,orgStructCode");
        headerItems.add(orgStructCode);
        headerList.add(Lists.newArrayList("组织基本信息", "组织名称", "orgStructName"));
        OrgImportHeaderDTO orgStructName = new OrgImportHeaderDTO();
        orgStructName.setField("orgStructName");
        orgStructName.setName("组织基本信息,组织名称,orgStructName");
        headerItems.add(orgStructName);
        headerList.add(Lists.newArrayList("组织基本信息", "业务类型(例：公司组织，采购组织)", "orgBusinessTypeNames"));
        OrgImportHeaderDTO orgBusinessTypeNames = new OrgImportHeaderDTO();
        orgBusinessTypeNames.setField("orgBusinessTypeNames");
        orgBusinessTypeNames.setName("组织基本信息,业务类型(例：公司组织，采购组织),orgBusinessTypeNames");
        headerItems.add(orgBusinessTypeNames);
        headerList.add(Lists.newArrayList("组织基本信息", "排序", "sort"));
        OrgImportHeaderDTO sort = new OrgImportHeaderDTO();
        sort.setField("sort");
        sort.setName("组织基本信息,排序,sort");
        headerItems.add(sort);
        headerList.add(Lists.newArrayList("组织基本信息", "启用日期", "enableDate"));
        OrgImportHeaderDTO enableDate = new OrgImportHeaderDTO();
        enableDate.setField("enableDate");
        enableDate.setName("组织基本信息,启用日期,enableDate");
        headerItems.add(enableDate);
        headerList.add(Lists.newArrayList("组织业务类型包含公司组织、采购组织、销售组织、库存组织中任一，这2列就需要维护", "公司组织编码", "comOrgCode"));
        OrgImportHeaderDTO comOrgCode = new OrgImportHeaderDTO();
        comOrgCode.setField("comOrgCode");
        comOrgCode.setName("组织业务类型包含公司组织、采购组织、销售组织、库存组织中任一，这2列就需要维护,公司组织编码,comOrgCode");
        headerItems.add(comOrgCode);
        headerList.add(Lists.newArrayList("组织业务类型包含公司组织、采购组织、销售组织、库存组织中任一，这2列就需要维护", "合作伙伴编码", "partnerCode"));
        OrgImportHeaderDTO partnerCode = new OrgImportHeaderDTO();
        partnerCode.setField("partnerCode");
        partnerCode.setName("组织业务类型包含公司组织、采购组织、销售组织、库存组织中任一，这2列就需要维护,合作伙伴编码,partnerCode");
        headerItems.add(partnerCode);
        AtomicInteger i = new AtomicInteger(0);
        headerList.forEach(t -> {
            SheetColumnInfo sheetColumnInfo = new SheetColumnInfo();
            sheetColumnInfo.setColumnIndex(i.get());
            sheetColumnInfo.setColumnType(ColumnType.TEXT.name());
            i.getAndIncrement();
            sheetColumnInfoList.add(sheetColumnInfo);
        });
        for (OrgBusinessTypePO orgBusinessTypePO : orgBusinessTypePOS) {
            List<GenAttrDTO> genAttrDTOList = attrGroupAttrMap.get(orgBusinessTypePO.getAttrGroupId());
            if (!CollectionUtils.isEmpty(genAttrDTOList)) {
                for (GenAttrDTO genAttrDTO : genAttrDTOList) {
                    if (genAttrDTO.getAttrDataType().equals(AttrDataTypeDict.OBJECT)) {
                        continue;
                    }
                    OrgImportHeaderDTO orgImportHeaderDTO = new OrgImportHeaderDTO();
                    orgImportHeaderDTO.setField(genAttrDTO.getAttrCode());
                    orgImportHeaderDTO.setName(orgBusinessTypePO.getName() + "," + genAttrDTO.getAttrName() + "," + genAttrDTO.getAttrCode());
                    headerItems.add(orgImportHeaderDTO);
                    headerList.add(Lists.newArrayList(orgBusinessTypePO.getName(), genAttrDTO.getAttrName(), genAttrDTO.getAttrCode()));
                    SheetColumnInfo sheetColumnInfo = new SheetColumnInfo();
                    sheetColumnInfo.setColumnIndex(sheetColumnInfoList.size());
                    sheetColumnInfo.setColumnType(ColumnType.TEXT.name());
                    if (AttrDataTypeDict.DICTIONARY.equals(genAttrDTO.getAttrDataType())) {
                        sheetColumnInfo.setColumnType(ColumnType.OPTION.name());
                        List<GenDictItemDTO> dictItemDTOList = orgMdGateWay.findDictItemByDictHeadId(new IdRequest(genAttrDTO.getDictHeadId()));
                        List<GenDictItemDTO> enabledictItemDTOList = dictItemDTOList.stream().filter(t -> OrgStatusDict.ENABLED.equals(t.getStatus())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(enabledictItemDTOList)) {
                            List<String> options = enabledictItemDTOList.stream().map(GenDictItemDTO::getName).collect(Collectors.toList());
                            sheetColumnInfo.setOptionValues(options);
                        } else {
                            sheetColumnInfo.setOptionValues(Collections.emptyList());
                        }
                    }
                    if (AttrDataTypeDict.BOOLEAN.equals(genAttrDTO.getAttrDataType())) {
                        sheetColumnInfo.setColumnType(ColumnType.OPTION.name());
                        sheetColumnInfo.setOptionValues(Arrays.asList("是", "否"));
                    }
                    HeadCellColorInfo headCellColorInfo2 = new HeadCellColorInfo();
                    headCellColorInfo2.setStartRowIndex(1);
                    headCellColorInfo2.setEndRowIndex(1);
                    headCellColorInfo2.setStartColumnIndex(headerList.size() - 1);
                    headCellColorInfo2.setEndColumnIndex(headerList.size() - 1);
                    if (OrgStructImportTypeDict.ORG_UPDATE.equals(type)) {
                        headCellColorInfo2.setFontColor(IndexedColors.BLACK);
                    }
                    if (OrgStructImportTypeDict.ORG_CREATE.equals(type)) {
                        if (genAttrDTO.getAttrIsRequire()) {
                            headCellColorInfo2.setFontColor(IndexedColors.RED);
                        } else {
                            headCellColorInfo2.setFontColor(IndexedColors.BLACK);
                        }
                    }
                    headCellColorInfos.add(headCellColorInfo2);
                    sheetColumnInfoList.add(sheetColumnInfo);
                }
            }
        }
        headerList.forEach(t -> {
            HeadCellColorInfo headCellColorInfo1 = new HeadCellColorInfo();
            headCellColorInfo1.setStartRowIndex(1);
            headCellColorInfo1.setEndRowIndex(1);
            headCellColorInfo1.setStartColumnIndex(0);
            headCellColorInfo1.setEndColumnIndex(headerList.size() - 1);
            headCellColorInfo1.setFillForegroundColor(IndexedColors.SKY_BLUE);
            headCellColorInfos.add(headCellColorInfo1);
        });
        sheetHeadInfo.setHeadCellColorInfos(headCellColorInfos);
        return headerList;
    }
}

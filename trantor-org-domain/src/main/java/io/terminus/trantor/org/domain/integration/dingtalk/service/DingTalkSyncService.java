package io.terminus.trantor.org.domain.integration.dingtalk.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkhrm_1_0.models.QueryDismissionStaffIdListResponseBody;
import com.dingtalk.api.response.OapiSmartworkHrmEmployeeQueryonjobResponse;
import com.dingtalk.api.response.OapiSmartworkHrmEmployeeV2ListResponse;
import com.dingtalk.api.response.OapiV2DepartmentGetResponse;
import com.dingtalk.api.response.OapiV2DepartmentListsubResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.common.api.request.IdRequest;
import io.terminus.trantor.org.domain.converter.EmployeeConverter;
import io.terminus.trantor.org.domain.converter.OrgStructMdConverter;
import io.terminus.trantor.org.domain.integration.dingtalk.constant.DingTalkConstant;
import io.terminus.trantor.org.domain.service.EmployeeService;
import io.terminus.trantor.org.domain.service.OrgStructMdService;
import io.terminus.trantor.org.infrastructure.repo.*;
import io.terminus.trantor.org.spi.dict.EmployeeStatusDict;
import io.terminus.trantor.org.spi.dict.EmployeeTypeDict;
import io.terminus.trantor.org.spi.dict.OrgStatusDict;
import io.terminus.trantor.org.spi.model.dto.EmployeeOrgLinkDTO;
import io.terminus.trantor.org.spi.model.dto.EmployeeSaveDTO;
import io.terminus.trantor.org.spi.model.dto.OrgStructMdSaveDTO;
import io.terminus.trantor.org.spi.model.dto.integration.dingtalk.DingTalkConfigDTO;
import io.terminus.trantor.org.spi.model.po.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2024-09-02 16:10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DingTalkSyncService {

    private final DingTalkService dingTalkService;
    private final OrgStructMdService orgStructMdService;
    private final OrgChannelRepo orgChannelRepo;
    private final OrgStructMdRepo orgStructMdRepo;
    private final OrgDimensionCfRepo orgDimensionCfRepo;
    private final OrgBusinessTypeRepo orgBusinessTypeRepo;
    private final EmployeeRepo employeeRepo;
    private final EmployeeService employeeService;
    private final OrgStructMdConverter orgStructMdConverter;
    private final EmployeeConverter employeeConverter;
    private final EmployeeOrgLinkRepo employeeOrgLinkRepo;
    private final EmployeeNoticeSceneLinkRepo employeeNoticeSceneLinkRepo;

    /**
     * 同步钉钉组织架构
     */
    public void syncOrg() {
        OrgChannelPO channelPO = orgChannelRepo.findChannelByCode(DingTalkConstant.DING_TALK_CHANNEL_CODE);
        if (Objects.isNull(channelPO)) {
            log.error("dingtalk.org.sync: dingtalk channel is not exist");
            return;
        }
        OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(channelPO.getOrgDimensionCode());
        if (Objects.isNull(orgDimensionCfPO)) {
            log.error("dingtalk.org.sync: dingtalk org dimension is not exist");
            return;
        }
        OrgBusinessTypePO orgBusinessTypePO = orgBusinessTypeRepo.findByCode(channelPO.getOrgBusinessTypeCode());
        if (Objects.isNull(orgBusinessTypePO)) {
            log.error("dingtalk.org.sync: dingtalk org business type is not exist");
            return;
        }
        // 查询根节点
        OapiV2DepartmentGetResponse.DeptGetResponse deptGetResponse = dingTalkService.queryDepartmentDetail(1L);
        if (Objects.isNull(deptGetResponse)) {
            log.error("dingtalk.org.sync: dingtalk query root department is empty");
            return;
        }
        OrgStructMdSaveDTO root = buildOrgStructMdSaveDTO(null, orgDimensionCfPO, orgBusinessTypePO, deptGetResponse);
        orgStructMdService.save(root);
        // 查询下级节点
        List<OapiV2DepartmentListsubResponse.DeptBaseResponse> deptBaseResponseList = dingTalkService.queryDepartment(null);
        log.info("dingtalk.org.sync: dingtalk sync org data is: {}", JSON.toJSONString(deptBaseResponseList));
        if (CollectionUtils.isEmpty(deptBaseResponseList)) {
            log.error("dingtalk.org.sync: dingtalk query org is empty");
            return;
        }
        // 处理删除的组织节点
        this.handleDeleteOrgData(deptBaseResponseList, 1L, orgDimensionCfPO);
        // 递归处理组织节点
        for (OapiV2DepartmentListsubResponse.DeptBaseResponse deptBaseResponse : deptBaseResponseList) {
            OrgStructMdSaveDTO orgStructMdSaveDTO = buildOrgStructMdSaveDTO(deptBaseResponse, orgDimensionCfPO, orgBusinessTypePO, null);
            orgStructMdService.save(orgStructMdSaveDTO);
            orgRecursion(deptBaseResponse, orgDimensionCfPO, orgBusinessTypePO);
        }
    }

    private void handleDeleteOrgData(List<OapiV2DepartmentListsubResponse.DeptBaseResponse> deptBaseResponseList, Long deptId, OrgDimensionCfPO orgDimensionCfPO) {
        if (CollectionUtils.isEmpty(deptBaseResponseList)) {
            OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectByCode(orgDimensionCfPO.getId(), String.valueOf(deptId));
            if (Objects.isNull(orgStructMdPO)) {
                return;
            }
            List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryAllChild(orgStructMdPO.getId());
            if (CollectionUtils.isEmpty(orgStructMdPOS)) {
                return;
            }
            Set<Long> orgIds = orgStructMdPOS.stream().map(OrgStructMdPO::getId).collect(Collectors.toSet());
            orgIds.remove(orgStructMdPO.getId());
            for (Long orgId : orgIds) {
                orgStructMdRepo.deleteById(orgId);
            }
            orgStructMdPO.setLeaf(Boolean.TRUE);
            orgStructMdRepo.updateById(orgStructMdPO);
            // 删除组织和员工的关联关系
            if (!CollectionUtils.isEmpty(orgIds)) {
                employeeOrgLinkRepo.deleteByOrgIds(orgIds);
            }
            return;
        }
        Set<Long> deptIdSet = deptBaseResponseList.stream().map(OapiV2DepartmentListsubResponse.DeptBaseResponse::getDeptId).collect(Collectors.toSet());
        OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectByCode(orgDimensionCfPO.getId(), String.valueOf(deptId));
        if (Objects.isNull(orgStructMdPO)) {
            return;
        }
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryByPid(orgStructMdPO.getId());
        if (CollectionUtils.isEmpty(orgStructMdPOS)) {
            return;
        }
        Set<String> existOrgCodeSet = orgStructMdPOS.stream().map(OrgStructMdPO::getOrgCode).collect(Collectors.toSet());
        Set<String> deleteOrgCodeSet = existOrgCodeSet.stream().filter(t -> !deptIdSet.contains(Long.valueOf(t))).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(deleteOrgCodeSet)) {
            return;
        }
        Map<String, OrgStructMdPO> orgStructMdPOMap = orgStructMdPOS.stream().collect(Collectors.toMap(OrgStructMdPO::getOrgCode, Function.identity(), (o1, o2) -> o1));
        for (String orgCode : deleteOrgCodeSet) {
            OrgStructMdPO orgStructMdPO1 = orgStructMdPOMap.get(orgCode);
            if (Objects.nonNull(orgStructMdPO1)) {
                orgStructMdService.delete(new IdRequest(orgStructMdPO1.getId()));
                // 删除组织和员工的关联关系
                employeeOrgLinkRepo.deleteByOrgIds(Sets.newHashSet(orgStructMdPO1.getId()));
            }
        }
    }

    private void orgRecursion(OapiV2DepartmentListsubResponse.DeptBaseResponse deptBaseResponse, OrgDimensionCfPO orgDimensionCfPO, OrgBusinessTypePO orgBusinessTypePO) {
        List<OapiV2DepartmentListsubResponse.DeptBaseResponse> deptBaseResponseList = dingTalkService.queryDepartment(deptBaseResponse.getDeptId());
        this.handleDeleteOrgData(deptBaseResponseList, deptBaseResponse.getDeptId(), orgDimensionCfPO);
        if (CollectionUtils.isEmpty(deptBaseResponseList)) {
            return;
        }
        for (OapiV2DepartmentListsubResponse.DeptBaseResponse dept : deptBaseResponseList) {
            OrgStructMdSaveDTO orgStructMdSaveDTO = buildOrgStructMdSaveDTO(dept, orgDimensionCfPO, orgBusinessTypePO, null);
            orgStructMdService.save(orgStructMdSaveDTO);
            orgRecursion(dept, orgDimensionCfPO, orgBusinessTypePO);
        }
    }

    private OrgStructMdSaveDTO buildOrgStructMdSaveDTO(OapiV2DepartmentListsubResponse.DeptBaseResponse deptBaseResponse,
                                                       OrgDimensionCfPO orgDimensionCfPO, OrgBusinessTypePO orgBusinessTypePO,
                                                       OapiV2DepartmentGetResponse.DeptGetResponse deptGetResponse) {
        OrgStructMdSaveDTO orgStructMdSaveDTO = new OrgStructMdSaveDTO();
        OrgStructMdPO origin;
        if (Objects.nonNull(deptGetResponse)) {
            origin = orgStructMdRepo.selectByCode(orgDimensionCfPO.getId(), String.valueOf(deptGetResponse.getDeptId()));
        } else {
            origin = orgStructMdRepo.selectByCode(orgDimensionCfPO.getId(), String.valueOf(deptBaseResponse.getDeptId()));
        }
        if (Objects.nonNull(origin)) {
            orgStructMdSaveDTO = orgStructMdConverter.convertSaveDTO(origin);
        } else {
            orgStructMdSaveDTO.setOrgEnableDate(LocalDateTime.now());
            orgStructMdSaveDTO.setOrgStatus(OrgStatusDict.ENABLED);
        }
        orgStructMdSaveDTO.setOrgDimensionId(orgDimensionCfPO.getId());
        orgStructMdSaveDTO.setOrgDimensionCode(orgDimensionCfPO.getOrgDimensionCode());
        orgStructMdSaveDTO.setOrgBusinessTypeIds(Lists.newArrayList(orgBusinessTypePO.getId()));
        if (Objects.nonNull(deptGetResponse)) {
            orgStructMdSaveDTO.setOrgName(deptGetResponse.getName());
            orgStructMdSaveDTO.setOrgCode(String.valueOf(deptGetResponse.getDeptId()));
        } else {
            orgStructMdSaveDTO.setOrgName(deptBaseResponse.getName());
            orgStructMdSaveDTO.setOrgCode(String.valueOf(deptBaseResponse.getDeptId()));
            if (Objects.nonNull(deptBaseResponse.getParentId())) {
                OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectByCode(orgDimensionCfPO.getId(), String.valueOf(deptBaseResponse.getParentId()));
                if (Objects.isNull(orgStructMdPO)) {
                    orgStructMdSaveDTO.setOrgParentId(null);
                } else {
                    orgStructMdSaveDTO.setOrgParentId(orgStructMdPO.getId());
                }
            }
        }
        return orgStructMdSaveDTO;
    }

    /**
     * 同步钉钉员工信息
     */
    public void syncEmployee() {
        OrgChannelPO channelPO = orgChannelRepo.findChannelByCode(DingTalkConstant.DING_TALK_CHANNEL_CODE);
        if (Objects.isNull(channelPO)) {
            log.error("dingtalk.employee.sync: dingtalk channel is not exist");
            return;
        }
        OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode("ADM_ORG_GRP");
        if (Objects.isNull(orgDimensionCfPO)) {
            return;
        }

        Long offset = 0L;
        Long size = 50L;
        if (Objects.isNull(channelPO.getChannelConfig())) {
            log.error("dingtalk.employee.sync: dingtalk channel config is empty");
            return;
        }
        DingTalkConfigDTO dingTalkConfigDTO = JSONObject.parseObject(channelPO.getChannelConfig(), DingTalkConfigDTO.class);
        while (true) {
            OapiSmartworkHrmEmployeeQueryonjobResponse.PageResult pageResult = dingTalkService.queryAllEmployee(offset, size);
            if (CollectionUtils.isEmpty(pageResult.getDataList())) {
                break;
            }
            offset = pageResult.getNextCursor();
            List<String> userIdList = pageResult.getDataList();
            List<OapiSmartworkHrmEmployeeV2ListResponse.EmpRosterFieldVo> empRosterFieldVoList = dingTalkService.batchQueryEmployeeDetail(new HashSet<>(userIdList), dingTalkConfigDTO.getAgentId());
            if (CollectionUtils.isEmpty(empRosterFieldVoList)) {
                return;
            }
            for (OapiSmartworkHrmEmployeeV2ListResponse.EmpRosterFieldVo empRosterFieldVo : empRosterFieldVoList) {
                try {
                    EmployeeSaveDTO employeeSaveDTO = buildEmployeeSaveDTO(empRosterFieldVo, orgDimensionCfPO);
                    if (Objects.isNull(employeeSaveDTO.getId())) {
                        employeeService.create(employeeSaveDTO);
                    } else {
                        employeeService.update(employeeSaveDTO);
                    }
                } catch (Exception e) {
                    log.error("dingtalk.employee.sync: create employee error", e);
                }
            }
            if (Objects.isNull(pageResult.getNextCursor())) {
                break;
            }
        }
        // 离职员工处理
        handelResignedEmployee();
    }

    private void handelResignedEmployee() {
        Long offset = 0L;
        Integer pageSize = 50;
        while (true) {
            QueryDismissionStaffIdListResponseBody queryDismissionStaffIdListResponseBody = dingTalkService.queryResignedEmployeeIdList(offset, pageSize);
            if (CollectionUtils.isEmpty(queryDismissionStaffIdListResponseBody.getUserIdList())) {
                break;
            }
            List<EmployeePO> employeePOS = employeeRepo.queryDingTalkCodesAndStatus(queryDismissionStaffIdListResponseBody.getUserIdList(), EmployeeStatusDict.ENABLED);
            if (!CollectionUtils.isEmpty(employeePOS)) {
                for (EmployeePO employeePO : employeePOS) {
                    employeeService.delete(employeePO.getId());
                }
            }
            if (!queryDismissionStaffIdListResponseBody.getHasMore()) {
                break;
            }
            offset = queryDismissionStaffIdListResponseBody.getNextToken();
        }
    }

    private EmployeeSaveDTO buildEmployeeSaveDTO(OapiSmartworkHrmEmployeeV2ListResponse.EmpRosterFieldVo empRosterFieldVo, OrgDimensionCfPO orgDimensionCfPO) {
        EmployeeSaveDTO employeeSaveDTO = new EmployeeSaveDTO();
        String userId = empRosterFieldVo.getUserid();
        EmployeePO employeePO = employeeRepo.queryDingTalkCode(userId);
        if (Objects.nonNull(employeePO)) {
            employeeSaveDTO = employeeConverter.convertSaveDTO(employeePO);
        } else {
            employeeSaveDTO.setStatus(OrgStatusDict.ENABLED);
        }
        employeeSaveDTO.setCode(empRosterFieldVo.getUserid());
        employeeSaveDTO.setDingTalkCode(empRosterFieldVo.getUserid());
        if (Objects.nonNull(employeePO)) {
            List<EmployeeNoticeSceneLinkPO> employeeNoticeSceneLinkPOS = employeeNoticeSceneLinkRepo.queryByEmployeeId(employeePO.getId());
            employeeSaveDTO.setNoticeSceneList(employeeConverter.covert(employeeNoticeSceneLinkPOS));
        }
        List<OapiSmartworkHrmEmployeeV2ListResponse.EmpFieldDataVo> fieldDataList = empRosterFieldVo.getFieldDataList();
        for (OapiSmartworkHrmEmployeeV2ListResponse.EmpFieldDataVo empFieldDataVo : fieldDataList) {
            // 姓名
            if ("sys00-name".equals(empFieldDataVo.getFieldCode())) {
                if (!CollectionUtils.isEmpty(empFieldDataVo.getFieldValueList())) {
                    OapiSmartworkHrmEmployeeV2ListResponse.FieldValueVo fieldValueVo = empFieldDataVo.getFieldValueList().stream().findFirst().get();
                    employeeSaveDTO.setName(fieldValueVo.getValue());
                }
            }
            // 邮箱
            if ("sys00-email".equals(empFieldDataVo.getFieldCode())) {
                if (!CollectionUtils.isEmpty(empFieldDataVo.getFieldValueList())) {
                    OapiSmartworkHrmEmployeeV2ListResponse.FieldValueVo fieldValueVo = empFieldDataVo.getFieldValueList().stream().findFirst().get();
                    employeeSaveDTO.setEmail(fieldValueVo.getValue());
                }
            }
            // 主部门ID
            if ("sys00-mainDeptId".equals(empFieldDataVo.getFieldCode())) {
                if (!CollectionUtils.isEmpty(empFieldDataVo.getFieldValueList())) {
                    OapiSmartworkHrmEmployeeV2ListResponse.FieldValueVo fieldValueVo = empFieldDataVo.getFieldValueList().stream().findFirst().get();
                    if (String.valueOf(fieldValueVo.getValue()).equals("-1")) {
                        OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectByCode(orgDimensionCfPO.getId(), "1");
                        if (Objects.nonNull(orgStructMdPO)) {
                            employeeSaveDTO.setOrgStructId(orgStructMdPO.getId());
                        }
                    } else {
                        OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectByCode(orgDimensionCfPO.getId(), String.valueOf(fieldValueVo.getValue()));
                        if (Objects.nonNull(orgStructMdPO)) {
                            employeeSaveDTO.setOrgStructId(orgStructMdPO.getId());
                        }
                    }
                    // 处理组织和员工的关系
                    if (Objects.nonNull(employeeSaveDTO.getOrgStructId())) {
                        if (Objects.isNull(employeeSaveDTO.getId())) {
                            EmployeeOrgLinkDTO employeeOrgLinkDTO = new EmployeeOrgLinkDTO();
                            employeeOrgLinkDTO.setEmployeeId(employeeSaveDTO.getId());
                            employeeOrgLinkDTO.setOrgUnitId(employeeSaveDTO.getOrgStructId());
                            employeeOrgLinkDTO.setIsMainOrg(true);
                            employeeSaveDTO.setEmployeeOrgLinkList(Collections.singletonList(employeeOrgLinkDTO));
                        } else {
                            List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeId(employeeSaveDTO.getId());
                            if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
                                EmployeeOrgLinkDTO employeeOrgLinkDTO = new EmployeeOrgLinkDTO();
                                employeeOrgLinkDTO.setEmployeeId(employeeSaveDTO.getId());
                                employeeOrgLinkDTO.setOrgUnitId(employeeSaveDTO.getOrgStructId());
                                employeeOrgLinkDTO.setIsMainOrg(true);
                                employeeSaveDTO.setEmployeeOrgLinkList(Collections.singletonList(employeeOrgLinkDTO));
                            } else {
                                List<EmployeeOrgLinkDTO> employeeOrgLinkDTOList = new ArrayList<>(employeeOrgLinkPOS.size());
                                for (EmployeeOrgLinkPO employeeOrgLinkPO : employeeOrgLinkPOS) {
                                    if (employeeOrgLinkPO.getOrgUnitId().equals(employeePO.getOrgStructId())) {
                                        continue;
                                    }
                                    EmployeeOrgLinkDTO employeeOrgLinkDTO = new EmployeeOrgLinkDTO();
                                    employeeOrgLinkDTO.setEmployeeId(employeeSaveDTO.getId());
                                    employeeOrgLinkDTO.setOrgUnitId(employeeOrgLinkPO.getOrgUnitId());
                                    employeeOrgLinkDTO.setIsMainOrg(employeeOrgLinkPO.getIsMainOrg());
                                    employeeOrgLinkDTO.setIdentityId(employeeOrgLinkPO.getIdentityId());
                                    employeeOrgLinkDTOList.add(employeeOrgLinkDTO);
                                }
                                EmployeeOrgLinkDTO employeeOrgLinkDTO = new EmployeeOrgLinkDTO();
                                employeeOrgLinkDTO.setEmployeeId(employeeSaveDTO.getId());
                                employeeOrgLinkDTO.setOrgUnitId(employeeSaveDTO.getOrgStructId());
                                employeeOrgLinkDTO.setIsMainOrg(true);
                                employeeOrgLinkDTOList.add(employeeOrgLinkDTO);
                                employeeSaveDTO.setEmployeeOrgLinkList(employeeOrgLinkDTOList);
                            }
                        }
                    }
                }
            }
            // 手机号
            if ("sys00-mobile".equals(empFieldDataVo.getFieldCode())) {
                if (!CollectionUtils.isEmpty(empFieldDataVo.getFieldValueList())) {
                    OapiSmartworkHrmEmployeeV2ListResponse.FieldValueVo fieldValueVo = empFieldDataVo.getFieldValueList().stream().findFirst().get();
                    employeeSaveDTO.setMobile(fieldValueVo.getValue().substring(4));
                }
            }
            // 入职时间
            if ("sys00-confirmJoinTime".equals(empFieldDataVo.getFieldCode())) {
                if (!CollectionUtils.isEmpty(empFieldDataVo.getFieldValueList())) {
                    OapiSmartworkHrmEmployeeV2ListResponse.FieldValueVo fieldValueVo = empFieldDataVo.getFieldValueList().stream().findFirst().get();
//                    employeeSaveDTO.setEntryAt(fieldValueVo.getValue());
                }
            }
            // 员工类型
            if ("sys01-employeeType".equals(empFieldDataVo.getFieldCode())) {
                if (!CollectionUtils.isEmpty(empFieldDataVo.getFieldValueList())) {
                    OapiSmartworkHrmEmployeeV2ListResponse.FieldValueVo fieldValueVo = empFieldDataVo.getFieldValueList().stream().findFirst().get();
                    employeeSaveDTO.setType(EmployeeTypeDict.FORMAL);
                }
            }
            // 员工状态
            if ("sys01-employeeStatus".equals(empFieldDataVo.getFieldCode())) {
                if (!CollectionUtils.isEmpty(empFieldDataVo.getFieldValueList())) {
                    OapiSmartworkHrmEmployeeV2ListResponse.FieldValueVo fieldValueVo = empFieldDataVo.getFieldValueList().stream().findFirst().get();
                    employeeSaveDTO.setStatus(EmployeeStatusDict.ENABLED);
                    if ("1".equals(fieldValueVo.getValue())) {
                        employeeSaveDTO.setType(EmployeeTypeDict.PROBATION);
                    }
                }
            }
            // 工号
            if ("sys00-jobNumber".equals(empFieldDataVo.getFieldCode())) {
                if (!CollectionUtils.isEmpty(empFieldDataVo.getFieldValueList())) {
                    OapiSmartworkHrmEmployeeV2ListResponse.FieldValueVo fieldValueVo = empFieldDataVo.getFieldValueList().stream().findFirst().get();
                    if (StringUtils.hasText(fieldValueVo.getValue())) {
                        employeeSaveDTO.setCode(fieldValueVo.getValue());
                    }
                }
            }
        }
        return employeeSaveDTO;
    }
}

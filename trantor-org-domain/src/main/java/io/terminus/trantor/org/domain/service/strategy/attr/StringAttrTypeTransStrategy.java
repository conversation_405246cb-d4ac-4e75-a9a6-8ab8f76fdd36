package io.terminus.trantor.org.domain.service.strategy.attr;

import io.terminus.erp.strategy.Strategy;
import io.terminus.trantor.org.spi.dict.AttrDataTypeDict;
import io.terminus.trantor.org.spi.strategy.attr.AttrTypeTransStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * @author: 张博
 * @date: 2023-11-17 19:01
 */
@Component
@AllArgsConstructor
@Strategy(value = AttrTypeTransStrategy.class, implKey = AttrDataTypeDict.CHAR)
public class StringAttrTypeTransStrategy implements AttrTypeTransStrategy<String> {

    @Override
    public Boolean match(String attrDataType, Boolean isMulti) {
        return AttrDataTypeDict.CHAR.equals(attrDataType);
    }

    @Override
    public String trans(String value) {
        return value;
    }
}

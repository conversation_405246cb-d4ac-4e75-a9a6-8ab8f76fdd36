package io.terminus.trantor.org.domain.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.trantor.org.domain.constant.OrgConstants;
import io.terminus.trantor.org.domain.converter.BizTypeConverter;
import io.terminus.trantor.org.infrastructure.repo.BizTypeRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgBizTypeLimitLinkRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgBizTypeLinkRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgDimensionCfRepo;
import io.terminus.trantor.org.spi.model.dto.*;
import io.terminus.trantor.org.spi.model.po.BizTypePO;
import io.terminus.trantor.org.spi.model.po.OrgBizTypeLimitLinkPO;
import io.terminus.trantor.org.spi.model.po.OrgBizTypeLinkPO;
import io.terminus.trantor.org.spi.model.po.OrgDimensionCfPO;
import io.terminus.trantor.org.spi.msg.OrgMsg;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2023-07-18 10:28
 */
@Service
@RequiredArgsConstructor
public class BizTypeService {

    private final BizTypeRepo bizTypeRepo;
    private final OrgBizTypeLimitLinkRepo orgBizTypeLimitLinkRepo;
    private final OrgBizTypeLinkRepo orgBizTypeLinkRepo;
    private final IdGenerator idGenerator;
    private final BizTypeConverter bizTypeConverter;
    private final OrgDimensionCfRepo orgDimensionCfRepo;

    @Value("${org.hidde.filed:}")
    private String orgHiddeFiled;

    @Value("${trantor.org.version:v1}")
    private String version;

    public void save(BizTypeSaveDTO request) {
        if (Objects.isNull(request.getId())) {
            BizTypePO bizTypePO = bizTypeRepo.findOneByName(request.getName());
            if (Objects.nonNull(bizTypePO)) {
                throw new BusinessException(OrgMsg.ORG_BIZ_NAME_IS_EXIST);
            }
            if (StringUtils.hasText(request.getCode())) {
                BizTypePO bizTypeRepoOneByCode = bizTypeRepo.findOneByCode(request.getCode());
                if (Objects.nonNull(bizTypeRepoOneByCode)) {
                    throw new BusinessException(OrgMsg.ORG_BIZ_CODE_IS_EXIST);
                }
            }
            bizTypePO = new BizTypePO();
            bizTypePO.setId(idGenerator.nextId(BizTypePO.class));
            bizTypePO.setName(request.getName());
            bizTypePO.setCode(request.getCode());
            bizTypePO.setModelKey(request.getModelKey());
            bizTypeRepo.insert(bizTypePO);

            if (!CollectionUtils.isEmpty(request.getParentLimitBizTypeList())) {
                BizTypePO finalBizTypePO = bizTypePO;
                List<OrgBizTypeLimitLinkPO> orgBizTypeLimitLinkPOS = request.getParentLimitBizTypeList().stream().map(t -> {
                    OrgBizTypeLimitLinkPO orgBizTypeLimitLinkPO = new OrgBizTypeLimitLinkPO();
                    orgBizTypeLimitLinkPO.setBizTypeId(finalBizTypePO.getId());
                    orgBizTypeLimitLinkPO.setChildBizTypeId(t.getChildBizTypeId());
                    return orgBizTypeLimitLinkPO;
                }).collect(Collectors.toList());
                orgBizTypeLimitLinkRepo.insertBatch(orgBizTypeLimitLinkPOS);
            }
            return;
        }
        BizTypePO bizTypePO = bizTypeRepo.selectById(request.getId());
        if (Objects.isNull(bizTypePO)) {
            throw new BusinessException(OrgMsg.ORG_BIZ_TYPE_IS_NOT_EXIST);
        }
        BizTypePO bizType = bizTypeRepo.findOneByName(request.getName());
        if (Objects.nonNull(bizType) && !bizType.getId().equals(request.getId())) {
            throw new BusinessException(OrgMsg.ORG_BIZ_NAME_IS_EXIST);
        }
        BizTypePO bizTypeRepoOneByCode = bizTypeRepo.findOneByCode(request.getCode());
        if (Objects.nonNull(bizTypeRepoOneByCode) && !bizTypeRepoOneByCode.getId().equals(request.getId())) {
            throw new BusinessException(OrgMsg.ORG_BIZ_CODE_IS_EXIST);
        }
        bizTypePO.setModelKey(request.getModelKey());
        bizTypePO.setName(request.getName());
        bizTypePO.setCode(request.getCode());
        bizTypeRepo.updateById(bizTypePO);
        orgBizTypeLimitLinkRepo.deleteByBizTypeId(request.getId());
        if (!CollectionUtils.isEmpty(request.getParentLimitBizTypeList())) {
            List<OrgBizTypeLimitLinkPO> orgBizTypeLimitLinkPOS = request.getParentLimitBizTypeList().stream().map(t -> {
                OrgBizTypeLimitLinkPO orgBizTypeLimitLinkPO = new OrgBizTypeLimitLinkPO();
                orgBizTypeLimitLinkPO.setBizTypeId(bizTypePO.getId());
                orgBizTypeLimitLinkPO.setChildBizTypeId(t.getChildBizTypeId());
                return orgBizTypeLimitLinkPO;
            }).collect(Collectors.toList());
            orgBizTypeLimitLinkRepo.insertBatch(orgBizTypeLimitLinkPOS);
        }
    }

    public void delete(Long id) {
        BizTypePO bizTypePO = bizTypeRepo.selectById(id);
        if (Objects.isNull(bizTypePO)) {
            throw new BusinessException(OrgMsg.ORG_BIZ_TYPE_IS_NOT_EXIST);
        }
        OrgBizTypeLinkPO orgBizTypeLinkPO = orgBizTypeLinkRepo.queryByBizTypeId(id);
        if (Objects.nonNull(orgBizTypeLinkPO)) {
            throw new BusinessException(OrgMsg.ORG_BIZ_TYPE_HAS_BIND_ORG_CAN_NOT_DELETE);
        }
        orgBizTypeLimitLinkRepo.deleteByBizTypeId(id);
        bizTypeRepo.deleteById(id);
    }

    public List<BizTypeDTO> findAll(BizTypeQueryDTO request) {
        if (OrgConstants.ORG_VERSION_V1.equals(version)) {
            if (Objects.isNull(request.getPid())) {
                if (CollectionUtils.isEmpty(request.getCodes())) {
                    return bizTypeConverter.convertDTO(bizTypeRepo.findOrderedList());
                } else {
                    return bizTypeConverter.convertDTO(bizTypeRepo.findByCodes(request.getCodes()));
                }
            }
            List<OrgBizTypeLinkPO> orgBizTypeLinkPOS = orgBizTypeLinkRepo.queryByOrgUnitId(request.getPid());
            OrgBizTypeLinkPO orgBizTypeLinkPO = orgBizTypeLinkPOS.get(0);
            List<OrgBizTypeLimitLinkPO> orgBizTypeLimitLinkPOS = orgBizTypeLimitLinkRepo.queryByBizTypeId(orgBizTypeLinkPO.getBizTypeId());
            if (CollectionUtils.isEmpty(orgBizTypeLimitLinkPOS) && CollectionUtils.isEmpty(request.getCodes())) {
                return bizTypeConverter.convertDTO(bizTypeRepo.findOrderedList());
            }
            if (!CollectionUtils.isEmpty(orgBizTypeLimitLinkPOS) && CollectionUtils.isEmpty(request.getCodes())) {
                Set<Long> bizTypeIdSet = orgBizTypeLimitLinkPOS.stream().map(OrgBizTypeLimitLinkPO::getChildBizTypeId).filter(Objects::nonNull).collect(Collectors.toSet());
                return bizTypeConverter.convertDTO(bizTypeRepo.selectBatchIds(bizTypeIdSet));
            }
            if (CollectionUtils.isEmpty(orgBizTypeLimitLinkPOS)) {
                return bizTypeConverter.convertDTO(bizTypeRepo.findByCodes(request.getCodes()));
            }
            Set<Long> bizTypeIdSet = orgBizTypeLimitLinkPOS.stream().map(OrgBizTypeLimitLinkPO::getChildBizTypeId).filter(Objects::nonNull).collect(Collectors.toSet());
            List<BizTypePO> bizTypePOS = bizTypeRepo.findByCodes(request.getCodes());
            Set<Long> bizTypeIdSet1 = bizTypePOS.stream().map(BizTypePO::getId).collect(Collectors.toSet());
            bizTypeIdSet.retainAll(bizTypeIdSet1);
            if (CollectionUtils.isEmpty(bizTypeIdSet)) {
                return Collections.emptyList();
            }
            return bizTypeConverter.convertDTO(bizTypeRepo.selectBatchIds(bizTypeIdSet));
        }
        List<OrgDimensionCfPO> orgDimensionCfPOS = orgDimensionCfRepo.queryEnableList();
        if (CollectionUtils.isEmpty(orgDimensionCfPOS)) {
            return Collections.emptyList();
        }
        return orgDimensionCfPOS.stream().map(t -> {
            BizTypeDTO bizTypeDTO = new BizTypeDTO();
            bizTypeDTO.setId(t.getId());
            bizTypeDTO.setCode(t.getOrgDimensionCode());
            bizTypeDTO.setName(t.getOrgDimensionName());
            return bizTypeDTO;
        }).collect(Collectors.toList());
    }

    public BizTypeHideFileDTO queryHiddeFiled(BizTypeHiddeFiledQueryDTO request) {
        BizTypeHideFileDTO bizTypeHideFileDTO = new BizTypeHideFileDTO();
        if (Objects.isNull(request.getBizTypeId())) {
            bizTypeHideFileDTO.setHideFiledList(Collections.emptyList());
            return bizTypeHideFileDTO;
        }
        BizTypePO bizTypePO = bizTypeRepo.selectById(request.getBizTypeId());
        if (Objects.isNull(bizTypePO)) {
            bizTypeHideFileDTO.setHideFiledList(Collections.emptyList());
            return bizTypeHideFileDTO;
        }
        if (!StringUtils.hasText(orgHiddeFiled)) {
            bizTypeHideFileDTO.setHideFiledList(Collections.emptyList());
            return bizTypeHideFileDTO;
        }
        JSONObject jsonObject = JSONObject.parseObject(orgHiddeFiled);
        Object o = jsonObject.get(bizTypePO.getModelKey());
        if (Objects.isNull(o)) {
            bizTypeHideFileDTO.setHideFiledList(Collections.emptyList());
            return bizTypeHideFileDTO;
        }
        List<String> hiddeFiledList = JSONArray.parseArray(o.toString(), String.class);
        bizTypeHideFileDTO.setHideFiledList(hiddeFiledList);
        return bizTypeHideFileDTO;
    }
}

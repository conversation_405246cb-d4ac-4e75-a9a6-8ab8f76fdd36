package io.terminus.trantor.org.domain.service;

import io.terminus.trantor.org.infrastructure.repo.EmployeeOrgLinkRepo;
import io.terminus.trantor.org.infrastructure.repo.EmployeeRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgStructMdRepo;
import io.terminus.trantor.org.spi.model.po.EmployeeOrgLinkPO;
import io.terminus.trantor.org.spi.model.po.EmployeePO;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import io.terminus.trantor2.common.TrantorContext;
import lombok.RequiredArgsConstructor;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2024-09-04 16:52
 */
@Service
@RequiredArgsConstructor
public class OrgPermissionService {

    private final EmployeeRepo employeeRepo;
    private final OrgStructMdRepo orgStructMdRepo;
    private final EmployeeOrgLinkRepo employeeOrgLinkRepo;

    /**
     * 员工所在组织
     */
    public List<Long> queryEmployeeOrg() {
        Long userId = TrantorContext.getCurrentUserId();
        if (Objects.isNull(userId)) {
            return Lists.newArrayList(-1L);
        }
        EmployeePO employeePO = employeeRepo.queryByUserId(userId);
        if (Objects.isNull(employeePO)) {
            return Lists.newArrayList(-1L);
        }
        return Lists.newArrayList(employeePO.getOrgStructId());
    }

    /**
     * 员工所在组织及子组织
     */
    public List<Long> queryEmployeeOrgAndChildOrg() {
        Long userId = TrantorContext.getCurrentUserId();
        if (Objects.isNull(userId)) {
            return Lists.newArrayList(-1L);
        }
        EmployeePO employeePO = employeeRepo.queryByUserId(userId);
        if (Objects.isNull(employeePO)) {
            return Lists.newArrayList(-1L);
        }
        if (Objects.isNull(employeePO.getOrgStructId())) {
            return Lists.newArrayList(-1L);
        }
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryAllChild(employeePO.getOrgStructId());
        if (CollectionUtils.isEmpty(orgStructMdPOS)) {
            return Lists.newArrayList(-1L);
        }
        return orgStructMdPOS.stream().map(OrgStructMdPO::getId).collect(Collectors.toList());
    }

    /**
     * 员工管辖的组织
     */
    public List<Long> queryEmployeeJurisdictionOrg() {
        Long userId = TrantorContext.getCurrentUserId();
        if (Objects.isNull(userId)) {
            return Lists.newArrayList(-1L);
        }
        EmployeePO employeePO = employeeRepo.queryByUserId(userId);
        if (Objects.isNull(employeePO)) {
            return Lists.newArrayList(-1L);
        }
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeId(employeePO.getId());
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return Lists.newArrayList(-1L);
        }
        return employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toList());
    }

    /**
     * 员工管辖的组织及子组织
     */
    public List<Long> queryEmployeeJurisdictionOrgAndChildOrg() {
        Long userId = TrantorContext.getCurrentUserId();
        if (Objects.isNull(userId)) {
            return Lists.newArrayList(-1L);
        }
        EmployeePO employeePO = employeeRepo.queryByUserId(userId);
        if (Objects.isNull(employeePO)) {
            return Lists.newArrayList(-1L);
        }
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeId(employeePO.getId());
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return Lists.newArrayList(-1L);
        }
        List<Long> orgIds = new ArrayList<>(employeeOrgLinkPOS.size());
        for (EmployeeOrgLinkPO employeeOrgLinkPO : employeeOrgLinkPOS) {
            List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryAllChild(employeeOrgLinkPO.getOrgUnitId());
            if (!CollectionUtils.isEmpty(orgStructMdPOS)) {
                orgIds.addAll(orgStructMdPOS.stream().map(OrgStructMdPO::getId).collect(Collectors.toSet()));
            }
        }
        return orgIds;
    }
}

package io.terminus.trantor.org.domain.service.impt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.gei.service.api.CustomImportService;
import io.terminus.gei.service.api.entity.ImportResult;
import io.terminus.gei.service.api.entity.ImportSliceData;
import io.terminus.trantor.org.infrastructure.gateway.OrgMdGateWay;
import io.terminus.trantor.org.infrastructure.repo.OrgBusinessTypeRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgDimensionBusinessLinkRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgDimensionCfRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgStructMdRepo;
import io.terminus.trantor.org.spi.dict.AttrDataTypeDict;
import io.terminus.trantor.org.spi.dict.OrgStatusDict;
import io.terminus.trantor.org.spi.model.GenPartnerCodesQueryDTO;
import io.terminus.trantor.org.spi.model.PartnerDetailDTO;
import io.terminus.trantor.org.spi.model.dto.attr.GenAttrDTO;
import io.terminus.trantor.org.spi.model.dto.dict.GenDictItemDTO;
import io.terminus.trantor.org.spi.model.po.OrgBusinessTypePO;
import io.terminus.trantor.org.spi.model.po.OrgDimensionBusinessLinkPO;
import io.terminus.trantor.org.spi.model.po.OrgDimensionCfPO;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2023-12-04 11:13
 * 组织创建导入
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrgCreateImportService implements CustomImportService {

    private final OrgDimensionBusinessLinkRepo orgDimensionBusinessLinkRepo;
    private final OrgDimensionCfRepo orgDimensionCfRepo;
    private final OrgMdGateWay orgMdGateWay;
    private final OrgStructMdRepo orgStructMdRepo;
    private final IdGenerator idGenerator;
    private final OrgBusinessTypeRepo orgBusinessTypeRepo;
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    @Override
    public List<ImportResult> importData(ImportSliceData customImportSliceData) {
        log.info("组织新增导入的数据为:{}", JSON.toJSONString(customImportSliceData));
        JSONObject jsonObject = (JSONObject) JSON.parse(customImportSliceData.getImportContext());
        String orgDimensionCode = jsonObject.getString("orgDimensionCode");
        if (CollectionUtils.isEmpty(customImportSliceData.getSliceData())) {
            return Collections.emptyList();
        }
        // 组织维度
        OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(orgDimensionCode);
        // 组织业务类型关联表
        List<OrgDimensionBusinessLinkPO> orgDimensionBusinessLinkPOS = orgDimensionBusinessLinkRepo.queryGroupIdLinkByDimensionId(orgDimensionCfPO.getId());
        // 查询属性分组
        Set<Long> orgBusinessIdSet = orgDimensionBusinessLinkPOS.stream().map(OrgDimensionBusinessLinkPO::getOrgBusinessTypeId).collect(Collectors.toSet());
        List<OrgBusinessTypePO> orgBusinessTypePOS = orgBusinessTypeRepo.selectBatchIds(orgBusinessIdSet);
        Map<Long, OrgBusinessTypePO> orgBusinessTypeMap = orgBusinessTypePOS.stream().collect(Collectors.toMap(OrgBusinessTypePO::getId, Function.identity()));
        List<OrgBusinessTypePO> enabledOrgBusinessTypePOS = orgBusinessTypePOS.stream().filter(t -> OrgStatusDict.ENABLED.equals(t.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(enabledOrgBusinessTypePOS)) {
            return Collections.emptyList();
        }
        Map<String, OrgBusinessTypePO> orgBusinessTypePOMap = enabledOrgBusinessTypePOS.stream().collect(Collectors.toMap(OrgBusinessTypePO::getName, Function.identity()));
        Set<String> orgBusinessTypeNameSet = enabledOrgBusinessTypePOS.stream().map(OrgBusinessTypePO::getName).collect(Collectors.toSet());
        // 处理导入的数据
        List<Map<String, Object>> dataList = customImportSliceData.getSliceData();
        List<ImportResult> res = new ArrayList<>(dataList.size());
        log.info("新增组织导入的数据大小为:{}", dataList.size());
        // 按照sheet分组
        Set<String> parentCodeList = new HashSet<>(dataList.size());
        Set<String> orgCodeList = new HashSet<>(dataList.size());
        Set<String> comOrgCodeList = new HashSet<>(dataList.size());
        Set<String> partnerCodeList = new HashSet<>(dataList.size());
        Map<String, String> orgStructEnableDateMap = new HashMap<>();
        Set<Long> attrGroupIdSet = new HashSet<>(orgBusinessTypePOS.size());
        for (Map<String, Object> data : dataList) {
            if (Objects.nonNull(data.get("orgStructParentCode"))) {
                parentCodeList.add(String.valueOf(data.get("orgStructParentCode")));
            }
            if (Objects.nonNull(data.get("orgStructCode"))) {
                orgCodeList.add(String.valueOf(data.get("orgStructCode")));
            }
            if (Objects.nonNull(data.get("orgStructCode")) && Objects.nonNull(data.get("enableDate"))) {
                orgStructEnableDateMap.put(String.valueOf(data.get("orgStructCode")), String.valueOf(data.get("enableDate")));
            }
            if (Objects.nonNull(data.get("comOrgCode"))) {
                comOrgCodeList.add(String.valueOf(data.get("comOrgCode")));
            }
            if (Objects.nonNull(data.get("partnerCode"))) {
                partnerCodeList.add(String.valueOf(data.get("partnerCode")));
            }
            if (Objects.nonNull(data.get("orgBusinessTypeNames"))) {
                String orgBusinessTypeCodes = String.valueOf(data.get("orgBusinessTypeNames"));
                if (orgBusinessTypeCodes.contains(",") || orgBusinessTypeCodes.contains("，")) {
                    String[] split = new String[0];
                    if (orgBusinessTypeCodes.contains(",")) {
                        split = orgBusinessTypeCodes.split(",");
                    }
                    if (orgBusinessTypeCodes.contains("，")) {
                        split = orgBusinessTypeCodes.split("，");
                    }
                    for (String businessName : split) {
                        OrgBusinessTypePO orgBusinessTypePO = orgBusinessTypePOMap.get(businessName);
                        if (Objects.nonNull(orgBusinessTypePO)) {
                            attrGroupIdSet.add(orgBusinessTypePO.getAttrGroupId());
                        }
                    }
                } else {
                    OrgBusinessTypePO orgBusinessTypePO = orgBusinessTypePOMap.get(orgBusinessTypeCodes);
                    if (Objects.nonNull(orgBusinessTypePO)) {
                        attrGroupIdSet.add(orgBusinessTypePO.getAttrGroupId());
                    }
                }
            }
        }
        Map<Long, List<GenAttrDTO>> genAttrGroupByAttrGroupId = new HashMap<>(attrGroupIdSet.size());
        for (Long attrGroupId : attrGroupIdSet) {
            List<GenAttrDTO> genAttrDTOList = orgMdGateWay.findAttrByAttrGroupId(new IdRequest(attrGroupId));
            genAttrGroupByAttrGroupId.put(attrGroupId, genAttrDTOList);
        }
        List<String> repeatCodeList = orgCodeList.stream()
                .collect(Collectors.toMap(e -> e, e -> 1, Integer::sum)) // 获得元素出现频率的 Map，键为元素，值为元素出现的次数
                .entrySet().stream()                   // 所有 entry 对应的 Stream
                .filter(e -> e.getValue() > 1)         // 过滤出元素出现次数大于 1 (重复元素）的 entry
                .map(Map.Entry::getKey)              // 获得 entry 的键（重复元素）对应的 Stream
                .collect(Collectors.toList());
        // 处理父组织编码
        Map<String, OrgStructMdPO> orgStructMdParentMap = new HashMap<>(parentCodeList.size());
        Map<String, LocalDateTime> orgStructParentEnableDateMap = new HashMap<>(dataList.size());
        Map<String, Long> orgStructIdMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(parentCodeList)) {
            List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryByCodes(new HashSet<>(parentCodeList), orgDimensionCfPO.getId());
            if (!CollectionUtils.isEmpty(orgStructMdPOS)) {
                orgStructMdParentMap = orgStructMdPOS.stream().collect(Collectors.toMap(OrgStructMdPO::getOrgCode, Function.identity()));
                for (OrgStructMdPO orgStructMdPO : orgStructMdPOS) {
                    orgStructIdMap.put(orgStructMdPO.getOrgCode(), orgStructMdPO.getId());
                    orgStructParentEnableDateMap.put(orgStructMdPO.getOrgCode(), orgStructMdPO.getOrgEnableDate());
                }
            }
        }
        Map<String, OrgStructMdPO> orgStructMdExistMap = new HashMap<>(orgCodeList.size());
        if (!CollectionUtils.isEmpty(orgCodeList)) {
            List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryByCodes(new HashSet<>(orgCodeList), orgDimensionCfPO.getId());
            if (!CollectionUtils.isEmpty(orgStructMdPOS)) {
                orgStructMdExistMap = orgStructMdPOS.stream().collect(Collectors.toMap(OrgStructMdPO::getOrgCode, Function.identity()));
            }
            for (String code : orgCodeList) {
                OrgStructMdPO orgStructMdPO = orgStructMdExistMap.get(code);
                if (Objects.isNull(orgStructMdPO)) {
                    orgStructIdMap.put(code, idGenerator.nextId(OrgStructMdPO.class));
                } else {
                    orgStructIdMap.put(code, orgStructMdPO.getId());
                }
            }
        }
        Map<String, OrgStructMdPO> comOrgMap = new HashMap<>(orgCodeList.size());
        if (!CollectionUtils.isEmpty(comOrgCodeList)) {
            List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryByCodes(comOrgCodeList, orgDimensionCfPO.getId());
            if (!CollectionUtils.isEmpty(orgStructMdPOS)) {
                comOrgMap = orgStructMdPOS.stream().collect(Collectors.toMap(OrgStructMdPO::getOrgCode, Function.identity()));
            }
        }
        Map<String, PartnerDetailDTO> partnerDetailDTOMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(partnerCodeList)) {
            GenPartnerCodesQueryDTO genPartnerCodesQueryDTO = new GenPartnerCodesQueryDTO();
            genPartnerCodesQueryDTO.setCodes(new ArrayList<>(partnerCodeList));
            List<PartnerDetailDTO> partnerDetailDTOS = orgMdGateWay.queryPartnerByCodes(genPartnerCodesQueryDTO);
            if (!CollectionUtils.isEmpty(partnerDetailDTOS)) {
                partnerDetailDTOMap = partnerDetailDTOS.stream().collect(Collectors.toMap(PartnerDetailDTO::getCode, Function.identity()));
            }
        }
        List<GenAttrDTO> genAttrDTOList = new ArrayList<>();
        for (Map.Entry<Long, List<GenAttrDTO>> entry : genAttrGroupByAttrGroupId.entrySet()) {
            genAttrDTOList.addAll(entry.getValue());
        }
        List<Map<String, Object>> avilableDataList = new ArrayList<>(dataList.size());
        // 数据校验
        for (Map<String, Object> valueMap : dataList) {
            ImportResult customImportResult = new ImportResult();
            customImportResult.setSheetNo(Integer.valueOf(String.valueOf(valueMap.get("_sheetNo"))));
            customImportResult.setRowIndex(Integer.valueOf(String.valueOf(valueMap.get("_rowNo"))));
            customImportResult.setSuccessImported(false);
            try {
                // 父组织校验
                if (Objects.isNull(valueMap.get("orgStructParentCode"))) {
                    customImportResult.setErrMessage("父组织编码不能为空");
                    res.add(customImportResult);
                    continue;
                } else {
                    OrgStructMdPO orgStructMdPO = orgStructMdParentMap.get(valueMap.get("orgStructParentCode"));
                    if (Objects.nonNull(orgStructMdPO)) {
                        if (OrgStatusDict.DISABLED.equals(orgStructMdPO.getOrgStatus())) {
                            customImportResult.setErrMessage("父组织已禁用");
                            res.add(customImportResult);
                            continue;
                        }
                    } else {
                        if (!orgCodeList.contains(valueMap.get("orgStructParentCode"))) {
                            customImportResult.setErrMessage("父组织编码不存在");
                            res.add(customImportResult);
                            continue;
                        }
                    }
                }
                // 组织编码校验
                if (Objects.isNull(valueMap.get("orgStructCode"))) {
                    customImportResult.setErrMessage("组织编码不能为空");
                    res.add(customImportResult);
                    continue;
                } else {
                    if (repeatCodeList.contains(valueMap.get("orgStructCode"))) {
                        customImportResult.setErrMessage("表中存在编码重复的数据");
                        res.add(customImportResult);
                        continue;
                    }
                    OrgStructMdPO orgStructMdPO = orgStructMdExistMap.get(valueMap.get("orgStructCode"));
                    if (Objects.nonNull(orgStructMdPO)) {
                        customImportResult.setErrMessage("组织编码已存在");
                        res.add(customImportResult);
                        continue;
                    }
                }
                if (Objects.isNull(valueMap.get("orgStructName"))) {
                    customImportResult.setErrMessage("组织名称不能为空");
                    res.add(customImportResult);
                    continue;
                }
                if (Objects.isNull(valueMap.get("enableDate"))) {
                    customImportResult.setErrMessage("启用日期不能为空");
                    res.add(customImportResult);
                    continue;
                } else {
                    String enableDate1 = String.valueOf(valueMap.get("enableDate"));
                    if (enableDate1.contains("/")) {
                        String[] split = enableDate1.split("/");
                        String year = split[0];
                        String month = split[1].length() == 1 ? "0" + split[1] : split[1];
                        String day = split[2].length() == 1 ? "0" + split[2] : split[2];
                        enableDate1 = year + "-" + month + "-" + day;
                    }
                    try {
                        LocalDate.parse(enableDate1, dateTimeFormatter);
                    } catch (Exception e) {
                        customImportResult.setErrMessage("日期格式不符合规范");
                        res.add(customImportResult);
                        continue;
                    }
                    // 校验启用日期不能超过父组织的启用日期
                    LocalDate localDate = LocalDate.parse(enableDate1, dateTimeFormatter);
                    LocalDateTime enableDate = localDate.atStartOfDay();
                    String orgStructParentCode = String.valueOf(valueMap.get("orgStructParentCode"));
                    LocalDateTime existParentEnableDate = orgStructParentEnableDateMap.get(orgStructParentCode);
                    if (Objects.isNull(existParentEnableDate)) {
                        String parentEnableDate = orgStructEnableDateMap.get(orgStructParentCode);
                        if (!Objects.nonNull(parentEnableDate)) {
                            customImportResult.setErrMessage("父组织不存在");
                            res.add(customImportResult);
                            continue;
                        }
                        if (parentEnableDate.contains("/")) {
                            String[] split = parentEnableDate.split("/");
                            String year = split[0];
                            String month = split[1].length() == 1 ? "0" + split[1] : split[1];
                            String day = split[2].length() == 1 ? "0" + split[2] : split[2];
                            parentEnableDate = year + "-" + month + "-" + day;
                        }
                        LocalDate parentDate = LocalDate.parse(parentEnableDate, dateTimeFormatter);
                        LocalDateTime parentDateTime = parentDate.atStartOfDay();
                        if (enableDate.isBefore(parentDateTime)) {
                            customImportResult.setErrMessage("启用日期错误，必须大于或等于父组织的启用日期");
                            res.add(customImportResult);
                            continue;
                        }
                    } else {
                        if (enableDate.isBefore(existParentEnableDate)) {
                            customImportResult.setErrMessage("启用日期错误，必须大于或等于父组织的启用日期");
                            res.add(customImportResult);
                            continue;
                        }
                    }
                }
                List<Long> attrGroupIdList = new ArrayList<>(1);
                if (Objects.isNull(valueMap.get("orgBusinessTypeNames"))) {
                    customImportResult.setErrMessage("组织业务类型不能为空");
                    res.add(customImportResult);
                    continue;
                } else {
                    String orgBusinessTypeCodes = String.valueOf(valueMap.get("orgBusinessTypeNames"));
                    if (orgBusinessTypeCodes.contains(",") || orgBusinessTypeCodes.contains("，")) {
                        String[] split = new String[0];
                        if (orgBusinessTypeCodes.contains(",")) {
                            split = orgBusinessTypeCodes.split(",");
                        }
                        if (orgBusinessTypeCodes.contains("，")) {
                            split = orgBusinessTypeCodes.split("，");
                        }
                        Boolean checkFlag = Boolean.TRUE;
                        for (String orgBusinessTypeCode : split) {
                            if (!orgBusinessTypeNameSet.contains(orgBusinessTypeCode)) {
                                customImportResult.setErrMessage("当前组织维度不支持此种业务类型:" + orgBusinessTypeCode);
                                checkFlag = Boolean.FALSE;
                                break;
                            }
                            OrgBusinessTypePO orgBusinessTypePO = orgBusinessTypePOMap.get(orgBusinessTypeCode);
                            if (Objects.nonNull(orgBusinessTypePO) && Objects.nonNull(orgBusinessTypePO.getAttrGroupId())) {
                                attrGroupIdList.add(orgBusinessTypePO.getAttrGroupId());
                            }
                        }
                        if (!checkFlag) {
                            continue;
                        }
                        List<String> orgBusinessTypeCodeList = Arrays.asList(split);
                        if (!orgBusinessTypeCodeList.contains("公司组织")) {
                            if (orgBusinessTypeCodeList.contains("采购组织") || orgBusinessTypeCodeList.contains("销售组织") || orgBusinessTypeCodeList.contains("库存组织")) {
                                if (Objects.isNull(valueMap.get("comOrgCode"))) {
                                    customImportResult.setErrMessage("公司组织编码不能为空");
                                    res.add(customImportResult);
                                    continue;
                                } else {
                                    OrgStructMdPO comOrgCode = comOrgMap.get(String.valueOf(valueMap.get("comOrgCode")));
                                    boolean isContain = orgCodeList.contains(String.valueOf(valueMap.get("comOrgCode")));
                                    if (Objects.isNull(comOrgCode) && !isContain) {
                                        customImportResult.setErrMessage("公司组织编码不存在");
                                        res.add(customImportResult);
                                        continue;
                                    }
                                }
                            }
                        }
                    } else {
                        if (!orgBusinessTypeNameSet.contains(orgBusinessTypeCodes)) {
                            customImportResult.setErrMessage("当前组织维度不支持此种业务类型:" + orgBusinessTypeCodes);
                            res.add(customImportResult);
                            continue;
                        }
                        if (orgBusinessTypeCodes.equals("采购组织") || orgBusinessTypeCodes.equals("销售组织") || orgBusinessTypeCodes.equals("库存组织")) {
                            if (Objects.isNull(valueMap.get("comOrgCode"))) {
                                customImportResult.setErrMessage("公司组织编码不能为空");
                                res.add(customImportResult);
                                continue;
                            } else {
                                OrgStructMdPO comOrgCode = comOrgMap.get(String.valueOf(valueMap.get("comOrgCode")));
                                boolean isContain = orgCodeList.contains(String.valueOf(valueMap.get("comOrgCode")));
                                if (Objects.isNull(comOrgCode) && !isContain) {
                                    customImportResult.setErrMessage("公司组织编码不存在");
                                    res.add(customImportResult);
                                    continue;
                                }
                            }
                            OrgBusinessTypePO orgBusinessTypePO = orgBusinessTypePOMap.get(orgBusinessTypeCodes);
                            if (Objects.nonNull(orgBusinessTypePO) && Objects.nonNull(orgBusinessTypePO.getAttrGroupId())) {
                                attrGroupIdList.add(orgBusinessTypePO.getAttrGroupId());
                            }
                        }
                    }
                }
                if (Objects.nonNull(valueMap.get("sort"))) {
                    if (!isNumeric(String.valueOf(valueMap.get("sort")))) {
                        customImportResult.setErrMessage("排序字段类型不正确");
                        res.add(customImportResult);
                        continue;
                    }
                }
                if (Objects.nonNull(valueMap.get("partnerCode"))) {
                    PartnerDetailDTO partnerCode = partnerDetailDTOMap.get(String.valueOf(valueMap.get("partnerCode")));
                    if (Objects.isNull(partnerCode)) {
                        customImportResult.setErrMessage("合作伙伴编码不存在");
                        res.add(customImportResult);
                        continue;
                    }
                }
                // 扩展属性
                List<GenAttrDTO> genAttrList = new ArrayList<>(attrGroupIdList.size());
                for (Long attrGroupId : attrGroupIdList) {
                    List<GenAttrDTO> genAttrDTOList1 = genAttrGroupByAttrGroupId.get(attrGroupId);
                    if (!CollectionUtils.isEmpty(genAttrDTOList1)) {
                        genAttrList.addAll(genAttrDTOList1);
                    }
                }
                if (!CollectionUtils.isEmpty(genAttrList)) {
                    boolean attrRequireCheck = Boolean.TRUE;
                    for (GenAttrDTO genAttrDTO : genAttrList) {
                        if (genAttrDTO.getAttrDataType().equals(AttrDataTypeDict.OBJECT)) {
                            continue;
                        }
                        Object attrValue = valueMap.get(genAttrDTO.getAttrCode());
                        if (genAttrDTO.getAttrIsRequire()) {
                            if (Objects.isNull(attrValue)) {
                                customImportResult.setErrMessage(genAttrDTO.getAttrName() + "不能为空");
                                res.add(customImportResult);
                                attrRequireCheck = Boolean.FALSE;
                                break;
                            }
                        }
                        if (Objects.nonNull(attrValue)) {
                            String attrValueString = String.valueOf(attrValue);
                            if (AttrDataTypeDict.OBJECT.equals(genAttrDTO.getAttrDataType())) {
                                if (attrValueString.contains(";") && !genAttrDTO.getAttrIsMulti()) {
                                    customImportResult.setErrMessage(genAttrDTO.getAttrName() + "的值不能多选");
                                    res.add(customImportResult);
                                    attrRequireCheck = Boolean.FALSE;
                                    break;
                                }
                                if (attrValueString.contains(";")) {
                                    String[] split = attrValueString.split(";");
                                    for (String s : split) {
                                        if (!isNumeric(s)) {
                                            customImportResult.setErrMessage(genAttrDTO.getAttrName() + "的值类型不正确");
                                            res.add(customImportResult);
                                            attrRequireCheck = Boolean.FALSE;
                                            break;
                                        }
                                    }
                                } else {
                                    if (!isNumeric(attrValueString)) {
                                        customImportResult.setErrMessage(genAttrDTO.getAttrName() + "的值类型不正确");
                                        res.add(customImportResult);
                                        attrRequireCheck = Boolean.FALSE;
                                        break;
                                    }
                                }
                            }
                            if (AttrDataTypeDict.CHAR.equals(genAttrDTO.getAttrDataType()) || AttrDataTypeDict.MULTICHAR.equals(genAttrDTO.getAttrDataType())) {
                                if (Objects.nonNull(genAttrDTO.getAttrLength())) {
                                    if (attrValueString.length() > genAttrDTO.getAttrLength()) {
                                        customImportResult.setErrMessage(genAttrDTO.getAttrName() + "长度超过最大长度" + genAttrDTO.getAttrLength() + "的限制");
                                        res.add(customImportResult);
                                        attrRequireCheck = Boolean.FALSE;
                                        break;
                                    }
                                }
                            }
                            if (AttrDataTypeDict.NUMBER.equals(genAttrDTO.getAttrDataType())) {
                                if (!isNumeric2(attrValueString)) {
                                    customImportResult.setErrMessage(genAttrDTO.getAttrName() + "的值类型不正确");
                                    res.add(customImportResult);
                                    attrRequireCheck = Boolean.FALSE;
                                    break;
                                }
                            }
                            if (AttrDataTypeDict.DICTIONARY.equals(genAttrDTO.getAttrDataType())) {
                                if (attrValueString.contains(";") && !genAttrDTO.getAttrIsMulti()) {
                                    customImportResult.setErrMessage(genAttrDTO.getAttrName() + "的值不能多选");
                                    res.add(customImportResult);
                                    attrRequireCheck = Boolean.FALSE;
                                    break;
                                }
                                if (attrValueString.contains(";")) {
                                    String[] split = attrValueString.split(";");
                                    for (String s : split) {
                                        GenDictItemDTO query = new GenDictItemDTO();
                                        query.setName(s);
                                        query.setDictHeadId(genAttrDTO.getDictHeadId());
                                        GenDictItemDTO genDictItemDTO = orgMdGateWay.findDictItemByDictHeadIdAndName(query);
                                        if (Objects.isNull(genDictItemDTO)) {
                                            customImportResult.setErrMessage(genAttrDTO.getAttrName() + "的值不存在，请下拉选择正确的值");
                                            res.add(customImportResult);
                                            attrRequireCheck = Boolean.FALSE;
                                            break;
                                        }
                                    }
                                } else {
                                    GenDictItemDTO query = new GenDictItemDTO();
                                    query.setName(attrValueString);
                                    query.setDictHeadId(genAttrDTO.getDictHeadId());
                                    GenDictItemDTO genDictItemDTO = orgMdGateWay.findDictItemByDictHeadIdAndName(query);
                                    if (Objects.isNull(genDictItemDTO)) {
                                        customImportResult.setErrMessage(genAttrDTO.getAttrName() + "的值不存在，请下拉选择正确的值");
                                        res.add(customImportResult);
                                        attrRequireCheck = Boolean.FALSE;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    if (!attrRequireCheck) {
                        continue;
                    }
                }
            } catch (Exception e) {
                log.error("组织新增导入校验异常", e);
                customImportResult.setErrMessage("组织新增导入校验异常");
                res.add(customImportResult);
                continue;
            }
            avilableDataList.add(valueMap);
        }
        if (CollectionUtils.isEmpty(avilableDataList)) {
            return res;
        }
        if (avilableDataList.size() != dataList.size()) {
            for (Map<String, Object> value : avilableDataList) {
                ImportResult customImportResult = new ImportResult();
                customImportResult.setSheetNo(Integer.valueOf(String.valueOf(value.get("_sheetNo"))));
                customImportResult.setRowIndex(Integer.valueOf(String.valueOf(value.get("_rowNo"))));
                customImportResult.setSuccessImported(false);
                customImportResult.setErrMessage("表格中存在错误的数据，需要全部校验通过才能导入");
                res.add(customImportResult);
            }
            return res;
        }
        List<OrgStructMdPO> orgStructMdPOList = new ArrayList<>();
        for (Map<String, Object> value : avilableDataList) {
            ImportResult customImportResult = new ImportResult();
            customImportResult.setSheetNo(Integer.valueOf(String.valueOf(value.get("_sheetNo"))));
            customImportResult.setRowIndex(Integer.valueOf(String.valueOf(value.get("_rowNo"))));
            customImportResult.setSuccessImported(false);
            try {
                OrgStructMdPO orgStructMdPO = new OrgStructMdPO();
                Class<OrgStructMdPO> orgStructMdPOClass = OrgStructMdPO.class;
                // 父组织编码
                String orgStructParentCode = Objects.isNull(value.get("orgStructParentCode")) ? null : String.valueOf(value.get("orgStructParentCode"));
                if (Objects.nonNull(orgStructParentCode)) {
                    orgStructMdPO.setOrgParentId(orgStructIdMap.get(orgStructParentCode));
                }
                // 组织编码
                String orgStructCode = String.valueOf(value.get("orgStructCode"));
                if (Objects.nonNull(orgStructCode)) {
                    orgStructMdPO.setId(orgStructIdMap.get(orgStructCode));
                    orgStructMdPO.setOrgCode(orgStructCode);
                }
                // 组织名称
                orgStructMdPO.setOrgName(String.valueOf(value.get("orgStructName")));
                // 合作伙伴
                String partnerCode = Objects.isNull(value.get("partnerCode")) ? null : String.valueOf(value.get("partnerCode"));
                if (Objects.nonNull(partnerCode)) {
                    PartnerDetailDTO partnerDetailDTO = partnerDetailDTOMap.get(partnerCode);
                    if (Objects.nonNull(partnerDetailDTO)) {
                        orgStructMdPO.setPartnerId(partnerDetailDTO.getId());
                    }
                }
                // 组织业务类型
                String orgBusinessTypeNames = Objects.isNull(value.get("orgBusinessTypeNames")) ? null : String.valueOf(value.get("orgBusinessTypeNames"));
                if (Objects.nonNull(orgBusinessTypeNames)) {
                    List<String> businessTypeCodes = new ArrayList<>(1);
                    List<Long> businessTypeIds = new ArrayList<>(1);
                    if (orgBusinessTypeNames.contains(",") || orgBusinessTypeNames.contains("，")) {
                        String[] split = new String[0];
                        if (orgBusinessTypeNames.contains("，")) {
                            split = orgBusinessTypeNames.split("，");
                        }
                        if (orgBusinessTypeNames.contains(",")) {
                            split = orgBusinessTypeNames.split(",");
                        }
                        for (String businessCode : split) {
                            OrgBusinessTypePO orgBusinessTypePO = orgBusinessTypePOMap.get(businessCode);
                            businessTypeCodes.add(orgBusinessTypePO.getCode());
                            businessTypeIds.add(orgBusinessTypePO.getId());
                        }
                        List<String> list = new ArrayList<>(Arrays.asList(split));
                        if (list.contains("公司组织")) {
                            orgStructMdPO.setComOrgId(orgStructMdPO.getId());
                        } else {
                            if (Objects.nonNull(value.get("comOrgCode"))) {
                                OrgStructMdPO comOrgCode = comOrgMap.get(String.valueOf(value.get("comOrgCode")));
                                if (Objects.nonNull(comOrgCode)) {
                                    orgStructMdPO.setComOrgId(comOrgCode.getId());
                                } else {
                                    boolean isContain = orgCodeList.contains(String.valueOf(value.get("comOrgCode")));
                                    if (isContain) {
                                        orgStructMdPO.setComOrgId(orgStructIdMap.get(String.valueOf(value.get("comOrgCode"))));
                                    }
                                }
                            }
                        }
                    } else {
                        OrgBusinessTypePO orgBusinessTypePO = orgBusinessTypePOMap.get(orgBusinessTypeNames);
                        businessTypeCodes.add(orgBusinessTypePO.getCode());
                        businessTypeIds.add(orgBusinessTypePO.getId());
                        if (orgBusinessTypeNames.equals("公司组织")) {
                            orgStructMdPO.setComOrgId(orgStructMdPO.getId());
                        } else {
                            if (Objects.nonNull(value.get("comOrgCode"))) {
                                OrgStructMdPO comOrgCode = comOrgMap.get(String.valueOf(value.get("comOrgCode")));
                                if (Objects.nonNull(comOrgCode)) {
                                    orgStructMdPO.setComOrgId(comOrgCode.getId());
                                } else {
                                    boolean isContain = orgCodeList.contains(String.valueOf(value.get("comOrgCode")));
                                    if (isContain) {
                                        orgStructMdPO.setComOrgId(orgStructIdMap.get(String.valueOf(value.get("comOrgCode"))));
                                    }
                                }
                            }
                        }
                    }
                    orgStructMdPO.setOrgBusinessTypeIds(businessTypeIds);
                    orgStructMdPO.setOrgBusinessTypeCodes(businessTypeCodes);
                }
                // 启用日期
                String enableDate = Objects.isNull(value.get("enableDate")) ? null : String.valueOf(value.get("enableDate"));
                if (Objects.nonNull(enableDate)) {
                    if (enableDate.contains("/")) {
                        String[] split = enableDate.split("/");
                        String year = split[0];
                        String month = split[1].length() == 1 ? "0" + split[1] : split[1];
                        String day = split[2].length() == 1 ? "0" + split[2] : split[2];
                        enableDate = year + "-" + month + "-" + day;
                    }
                    LocalDate localDate = LocalDate.parse(enableDate, dateTimeFormatter);
                    orgStructMdPO.setOrgEnableDate(localDate.atStartOfDay());
                }
                // 排序
                String sort = Objects.isNull(value.get("sort")) ? null : String.valueOf(value.get("sort"));
                if (Objects.nonNull(sort)) {
                    if (Objects.nonNull(sort)) {
                        orgStructMdPO.setOrgSort(Integer.valueOf(sort));
                    } else {
                        orgStructMdPO.setOrgSort(1);
                    }
                }
                // 扩展属性
                List<Long> orgBusinessTypeIds = orgStructMdPO.getOrgBusinessTypeIds();
                List<GenAttrDTO> genAttrList = new ArrayList<>(orgBusinessTypeIds.size());
                for (Long orgBusinessTypeId : orgBusinessTypeIds) {
                    Long attrGroupId = orgBusinessTypeMap.get(orgBusinessTypeId).getAttrGroupId();
                    if (Objects.nonNull(attrGroupId)) {
                        List<GenAttrDTO> genAttrDTOList1 = genAttrGroupByAttrGroupId.get(attrGroupId);
                        if (!CollectionUtils.isEmpty(genAttrDTOList1)) {
                            genAttrList.addAll(genAttrDTOList1);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(genAttrList)) {
                    for (GenAttrDTO genAttrDTO : genAttrList) {
                        String attrValue = Objects.isNull(value.get(genAttrDTO.getAttrCode())) ? null : String.valueOf(value.get(genAttrDTO.getAttrCode()));
                        try {
                            Field field = orgStructMdPOClass.getDeclaredField(genAttrDTO.getAttrField());
                            field.setAccessible(Boolean.TRUE);
                            if (Objects.nonNull(attrValue)) {
                                if (AttrDataTypeDict.DICTIONARY.equals(genAttrDTO.getAttrDataType())) {
                                    if (genAttrDTO.getAttrIsMulti()) {
                                        List<Long> list = new ArrayList<>();
                                        if (attrValue.contains(";")) {
                                            String[] split = attrValue.split(";");
                                            for (String s : split) {
                                                GenDictItemDTO query = new GenDictItemDTO();
                                                query.setName(s);
                                                query.setDictHeadId(genAttrDTO.getDictHeadId());
                                                GenDictItemDTO genDictItemDTO = orgMdGateWay.findDictItemByDictHeadIdAndName(query);
                                                if (Objects.nonNull(genDictItemDTO)) {
                                                    list.add(genDictItemDTO.getId());
                                                }
                                            }
                                        } else {
                                            GenDictItemDTO query = new GenDictItemDTO();
                                            query.setName(attrValue);
                                            query.setDictHeadId(genAttrDTO.getDictHeadId());
                                            GenDictItemDTO genDictItemDTO = orgMdGateWay.findDictItemByDictHeadIdAndName(query);
                                            if (Objects.nonNull(genDictItemDTO)) {
                                                list.add(genDictItemDTO.getId());
                                            }
                                        }
                                        field.set(orgStructMdPO, JSON.toJSONString(list));
                                    } else {
                                        GenDictItemDTO query = new GenDictItemDTO();
                                        query.setName(attrValue);
                                        query.setDictHeadId(genAttrDTO.getDictHeadId());
                                        GenDictItemDTO genDictItemDTO = orgMdGateWay.findDictItemByDictHeadIdAndName(query);
                                        if (Objects.nonNull(genDictItemDTO)) {
                                            field.set(orgStructMdPO, String.valueOf(genDictItemDTO.getId()));
                                        }
                                    }
                                } else if (AttrDataTypeDict.ANNEX.equals(genAttrDTO.getAttrDataType())) {
                                    List<String> list = new ArrayList<>();
                                    if (attrValue.contains(";")) {
                                        String[] split = attrValue.split(";");
                                        list.addAll(Arrays.asList(split));
                                    } else {
                                        list.add(attrValue);
                                    }
                                    field.set(orgStructMdPO, JSON.toJSONString(list));
                                } else {
                                    if (AttrDataTypeDict.OBJECT.equals(genAttrDTO.getAttrDataType())) {
                                        continue;
                                    }
                                    if (AttrDataTypeDict.OBJECT.equals(genAttrDTO.getAttrDataType())) {
                                        if (genAttrDTO.getAttrIsMulti()) {
                                            List<Long> list = new ArrayList<>();
                                            if (attrValue.contains(";")) {
                                                String[] split = attrValue.split(";");
                                                for (String s : split) {
                                                    list.add(Long.valueOf(s));
                                                }
                                            } else {
                                                list.add(Long.valueOf(attrValue));
                                            }
                                            field.set(orgStructMdPO, JSON.toJSONString(list));
                                        } else {
                                            field.set(orgStructMdPO, attrValue);
                                        }
                                    } else {
                                        field.set(orgStructMdPO, attrValue);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.error("组织新增导入处理属性数据异常", e);
                        }
                    }
                }
                orgStructMdPO.setOrgStatus(OrgStatusDict.DRAFT);
                orgStructMdPO.setOrgDimensionId(orgDimensionCfPO.getId());
                orgStructMdPO.setOrgDimensionCode(orgDimensionCfPO.getOrgDimensionCode());
                if (parentCodeList.contains(orgStructMdPO.getOrgCode())) {
                    orgStructMdPO.setLeaf(Boolean.FALSE);
                } else {
                    orgStructMdPO.setLeaf(Boolean.TRUE);
                }
                customImportResult.setSuccessImported(true);
                res.add(customImportResult);
                // 数据插入
                orgStructMdPOList.add(orgStructMdPO);
            } catch (Exception e) {
                log.error("新增导入插入数据异常", e);
                customImportResult.setErrMessage(e.getMessage());
                res.add(customImportResult);
            }
        }
        try {
            List<List<OrgStructMdPO>> partition = Lists.partition(orgStructMdPOList, 100);
            for (List<OrgStructMdPO> orgStructMdPOS : partition) {
                orgStructMdRepo.insertBatch(orgStructMdPOS);
            }
            // 设置父节点为非叶子节点
            if (!CollectionUtils.isEmpty(orgStructMdParentMap)) {
                for (Map.Entry<String, OrgStructMdPO> entry1 : orgStructMdParentMap.entrySet()) {
                    OrgStructMdPO orgStructMdPO1 = entry1.getValue();
                    orgStructMdPO1.setLeaf(Boolean.FALSE);
                    orgStructMdRepo.updateById(orgStructMdPO1);
                }
            }
            // 构建path
            for (OrgStructMdPO orgStructMdPO : orgStructMdPOList) {
                if (Objects.isNull(orgStructMdPO.getOrgParentId())) {
                    orgStructMdPO.setPath(Lists.newArrayList(orgStructMdPO.getId()));
                } else {
                    OrgStructMdPO parent = orgStructMdRepo.selectById(orgStructMdPO.getOrgParentId());
                    if (CollectionUtils.isEmpty(parent.getPath())) {
                        List<OrgStructMdPO> orgStructMdPOS = queryPath(orgStructMdPO);
                        orgStructMdPO.setPath(orgStructMdPOS.stream().map(OrgStructMdPO::getId).collect(Collectors.toList()));
                    } else {
                        List<Long> path = parent.getPath();
                        path.add(orgStructMdPO.getId());
                        orgStructMdPO.setPath(path);
                        orgStructMdRepo.updateById(orgStructMdPO);
                    }
                }
            }
        } catch (Exception e) {
            log.info("组织创建更新出现异常");
        }
        return res;
    }

    public List<OrgStructMdPO> queryPath(OrgStructMdPO orgStructMdPO) {
        List<OrgStructMdPO> path = new ArrayList<>();
        if (Objects.isNull(orgStructMdPO.getOrgParentId())) {
            path.add(orgStructMdPO);
        } else {
            recursion(path, orgStructMdPO);
        }
        Collections.reverse(path);
        return path;
    }

    private void recursion(List<OrgStructMdPO> list, OrgStructMdPO orgStructMdPO) {
        list.add(orgStructMdPO);
        if (Objects.isNull(orgStructMdPO.getOrgParentId())) {
            return;
        }
        OrgStructMdPO parent = orgStructMdRepo.selectById(orgStructMdPO.getOrgParentId());
        if (Objects.nonNull(parent)) {
            recursion(list, parent);
        }
    }

    public boolean isNumeric(String str) {
        return str != null && str.chars().allMatch(Character::isDigit);
    }

    public boolean isNumeric2(String str) {
        return str != null && str.matches("-?\\d+(\\.\\d+)?");
    }

}

package io.terminus.trantor.org.domain.service.strategy.attr;

import io.terminus.erp.strategy.Strategy;
import io.terminus.trantor.org.spi.dict.AttrDataTypeDict;
import io.terminus.trantor.org.spi.strategy.attr.AttrTypeTransStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * @author: 张博
 * @date: 2023-11-17 19:01
 */
@Component
@AllArgsConstructor
@Strategy(value = AttrTypeTransStrategy.class, implKey = AttrDataTypeDict.BOOLEAN)
public class BooleanAttrTypeTransStrategy implements AttrTypeTransStrategy<Boolean> {

    @Override
    public Boolean match(String attrDataType, Boolean isMulti) {
        return AttrDataTypeDict.BOOLEAN.equals(attrDataType);
    }

    @Override
    public Boolean trans(String value) {
        if (StringUtils.hasText(value)) {
            return Boolean.parseBoolean(value);
        }
        return null;
    }
}

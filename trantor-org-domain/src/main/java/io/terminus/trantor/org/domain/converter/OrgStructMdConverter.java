package io.terminus.trantor.org.domain.converter;


import io.terminus.trantor.org.spi.model.dto.*;
import io.terminus.trantor.org.spi.model.po.EmployeeOrgLinkPO;
import io.terminus.trantor.org.spi.model.po.OrgStructAdjustHisMdPO;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface OrgStructMdConverter {
    List<OrgStructMdTreeDTO> convertDTO(List<OrgStructMdPO> orgStructMdPO);

    List<OrgStructMdTreePathDTO> convertPathDTO(List<OrgStructMdPO> orgStructMdPO);

    OrgStructMdDetailDTO convertDetailDTO(OrgStructMdPO orgStructMdPO);

    List<OrgStructMdDetailDTO> convertDetialDTOList(List<OrgStructMdPO> list);

    OrgStructMdDetailWithTypeDTO convert(OrgStructMdPO orgStructMdPO);

    OrgStructMdPO convertSaveDTO(OrgStructMdSaveDTO orgStructMdSaveDTO);

    OrgStructMdSaveDTO convertSaveDTO(OrgStructMdPO orgStructMdPO);

    List<OrgStructAdjustHisMdPO> converHistory(List<OrgStructMdPO> list);

    List<EmployeeOrgLinkPO> convert(List<EmployeeOrgLinkDTO> list);

    List<EmployeeOrgLinkDTO> convertToDTO(List<EmployeeOrgLinkPO> list);

    EmployeeOrgLinkPO convertEmployeeOrgLinkPO(EmployeeOrgLinkDTO employeeOrgLinkDTO);

}

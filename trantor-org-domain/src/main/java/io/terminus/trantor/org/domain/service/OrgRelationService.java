package io.terminus.trantor.org.domain.service;

import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.trantor.org.domain.converter.OrgBusinessTypeConverter;
import io.terminus.trantor.org.domain.converter.OrgRelationCfConverter;
import io.terminus.trantor.org.domain.converter.OrgRelationRuleCfConverter;
import io.terminus.trantor.org.infrastructure.repo.OrgBusinessTypeRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgDimensionBusinessLinkRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgRelationCfRepo;
import io.terminus.trantor.org.infrastructure.repo.OrgRelationRuleCfRepo;
import io.terminus.trantor.org.spi.dict.OrgRelationRuleDict;
import io.terminus.trantor.org.spi.dict.OrgStatusDict;
import io.terminus.trantor.org.spi.model.dto.OrgRelationRuleCfDTO;
import io.terminus.trantor.org.spi.model.dto.OrgRelationSaveDTO;
import io.terminus.trantor.org.spi.model.dto.attr.OrgBusinessTypeDTO;
import io.terminus.trantor.org.spi.model.po.OrgBusinessTypePO;
import io.terminus.trantor.org.spi.model.po.OrgDimensionBusinessLinkPO;
import io.terminus.trantor.org.spi.model.po.OrgRelationCfPO;
import io.terminus.trantor.org.spi.model.po.OrgRelationRuleCfPO;
import io.terminus.trantor.org.spi.msg.OrgMsg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrgRelationService {
    private final OrgRelationCfRepo orgRelationCfRepo;
    private final OrgRelationRuleCfRepo orgRelationRuleCfRepo;
    private final OrgRelationCfConverter orgRelationCfConverter;
    private final OrgDimensionBusinessLinkRepo orgDimensionBusinessLinkRepo;
    private final OrgBusinessTypeRepo orgBusinessTypeRepo;
    private final OrgBusinessTypeConverter orgBusinessTypeConverter;
    private final IdGenerator idGenerator;
    private final OrgRelationRuleCfConverter orgRelationRuleCfConverter;

    public void save(OrgRelationSaveDTO request) {
        OrgRelationCfPO relationCfPO = orgRelationCfConverter.convert(request);
        OrgRelationCfPO orgRelationCfPO = orgRelationCfRepo.queryOne(request.getOrgHeadUnitId(), request.getOrgRelationUnitId());
        if (Objects.nonNull(request.getId())) {
            if (Objects.nonNull(orgRelationCfPO) && !orgRelationCfPO.getId().equals(request.getId())) {
                throw new BusinessException(OrgMsg.ORG_RELATION_IS_EXIST);
            }
            orgRelationCfRepo.updateById(relationCfPO);
        } else {
            if (Objects.nonNull(orgRelationCfPO)) {
                throw new BusinessException(OrgMsg.ORG_RELATION_IS_EXIST);
            }
            relationCfPO.setId(idGenerator.nextId(OrgRelationCfPO.class));
            orgRelationCfRepo.insert(relationCfPO);
        }
    }

    public void enabled(IdRequest request) {
        OrgRelationCfPO orgRelationCfPO = orgRelationCfRepo.selectById(request.getId());
        if (Objects.nonNull(orgRelationCfPO)) {
            orgRelationCfPO.setOrgRelationStatus(OrgStatusDict.ENABLED);
            orgRelationCfRepo.updateById(orgRelationCfPO);
        }
    }

    private void checkRule(Long orgHeadUnitTypeId, Long orgHeadDimensionId, Long orgRelationUnitTypeId, Long orgRelationDimensionId, Long id, String status) {
        OrgRelationRuleCfPO orgRelationRuleCfPO = orgRelationRuleCfRepo.selectRule(orgHeadDimensionId, orgHeadUnitTypeId
                , orgRelationDimensionId, orgRelationUnitTypeId);
        if (Objects.isNull(orgRelationRuleCfPO)) {
            throw new BusinessException(OrgMsg.ORG_RELATION_RULE_CF_IS_NULL);
        }
        if (OrgRelationRuleDict.ONE.equals(orgRelationRuleCfPO.getOrgRelationRuleType())) {
            if (OrgStatusDict.ENABLED.equals(status)) {
                List<OrgRelationCfPO> check = orgRelationCfRepo.check(orgHeadDimensionId, orgHeadUnitTypeId
                        , orgRelationDimensionId, orgRelationUnitTypeId, id, OrgStatusDict.ENABLED);
                if (!CollectionUtils.isEmpty(check)) {
                    throw new BusinessException(OrgMsg.ORG_RELATION_RULE_CHECK);
                }
            }
        }
    }

    private void checkEnable(Long orgHeadUnitTypeId, Long orgHeadDimensionId, Long orgRelationUnitTypeId, Long orgRelationDimensionId, Long id, String status) {
        OrgRelationRuleCfPO orgRelationRuleCfPO = orgRelationRuleCfRepo.selectRule(orgHeadDimensionId, orgHeadUnitTypeId
                , orgRelationDimensionId, orgRelationUnitTypeId);
        if (Objects.isNull(orgRelationRuleCfPO)) {
            throw new BusinessException(OrgMsg.ORG_RELATION_RULE_CF_CHECK_FAIL);
        }
        if (OrgRelationRuleDict.ONE.equals(orgRelationRuleCfPO.getOrgRelationRuleType())) {
            if (OrgStatusDict.ENABLED.equals(status)) {
                List<OrgRelationCfPO> check = orgRelationCfRepo.check(orgHeadDimensionId, orgHeadUnitTypeId
                        , orgRelationDimensionId, orgRelationUnitTypeId, id, OrgStatusDict.ENABLED);
                if (!CollectionUtils.isEmpty(check)) {
                    throw new BusinessException(OrgMsg.ORG_RELATION_RULE_CHECK);
                }
            }
        }
    }

    public void batchEnabled(IdsRequest request) {
        List<OrgRelationCfPO> orgRelationCfPOS = orgRelationCfRepo.selectBatchIds(request.getIds());
        for (OrgRelationCfPO orgRelationCfPO : orgRelationCfPOS) {
            orgRelationCfPO.setOrgRelationStatus(OrgStatusDict.ENABLED);
            orgRelationCfRepo.updateById(orgRelationCfPO);
        }
    }

    public List<OrgBusinessTypeDTO> queryByDimensionId(IdRequest request) {
        List<OrgDimensionBusinessLinkPO> orgDimensionBusinessLinkPOS = orgDimensionBusinessLinkRepo.queryGroupIdLinkByDimensionId(request.getId());
        if (CollectionUtils.isEmpty(orgDimensionBusinessLinkPOS)) {
            return null;
        }
        Set<Long> collect = orgDimensionBusinessLinkPOS.stream().map(OrgDimensionBusinessLinkPO::getOrgBusinessTypeId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<OrgBusinessTypePO> orgBusinessTypePOS = orgBusinessTypeRepo.selectByIds(collect, OrgStatusDict.ENABLED);
        return orgBusinessTypeConverter.convert(orgBusinessTypePOS);
    }

    public void OrgRelationRuleSave(OrgRelationRuleCfDTO request) {
        OrgRelationRuleCfPO orgRelationRuleCfPO = orgRelationRuleCfRepo.check(request.getOrgHeadDimensionId(), request.getOrgHeadUnitTypeId()
                , request.getOrgRelationDimensionId(), request.getOrgRelationUnitTypeId(), request.getId());
        if (Objects.nonNull(orgRelationRuleCfPO)) {
            throw new BusinessException(OrgMsg.ORG_RELATION_RULE_CF_IS_EXIST);
        }
        if (Objects.nonNull(request.getId())) {
            orgRelationRuleCfRepo.updateById(orgRelationRuleCfConverter.convert(request));
        } else {
            orgRelationRuleCfRepo.insert(orgRelationRuleCfConverter.convert(request));
        }
    }
}

package io.terminus.trantor.org.domain.service.impt;

import com.alibaba.fastjson.JSON;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.runtime.util.I18nUtils;
import io.terminus.gei.api.dict.ExtraFieldDict;
import io.terminus.gei.service.api.ImportService;
import io.terminus.gei.service.api.entity.ImportSliceData;
import io.terminus.trantor.org.domain.service.EmployeeService;
import io.terminus.trantor.org.infrastructure.gateway.OrgMdGateWay;
import io.terminus.trantor.org.spi.model.dto.EmployeeOrgLinkDTO;
import io.terminus.trantor.org.spi.model.dto.EmployeeSaveDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: 张博
 * @date: 2024-09-06 10:19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmployeeImportService implements ImportService {
    private final OrgMdGateWay orgMdGateWay;
    private final EmployeeService employeeService;

    @Override
    public List<Map<String, Object>> importData(ImportSliceData importSliceData) {
        log.info("员工导入的数据为:{}", JSON.toJSONString(importSliceData));
        List<Map<String, Object>> sliceData = importSliceData.getSliceData();
        if (CollectionUtils.isEmpty(sliceData)) {
            return Collections.emptyList();
        }
        for (Map<String, Object> valueMap : sliceData) {
            try {
                EmployeeSaveDTO employeeSaveDTO = buildEmployeeSaveDTO(valueMap);
                employeeService.create(employeeSaveDTO);
            } catch (BusinessException ex) {
                String message = I18nUtils.getMessage(ex.getMessage());
                if (StringUtils.hasText(message)) {
                    valueMap.put(ExtraFieldDict.ERR_MSG, message);
                } else {
                    valueMap.put(ExtraFieldDict.ERR_MSG, ex.getMessage());
                }
            } catch (Exception ex) {
                valueMap.put(ExtraFieldDict.ERR_MSG, ex.getMessage());
                log.error("导入员工数据失败", ex);
            }
        }
        return sliceData;
    }

    private EmployeeSaveDTO buildEmployeeSaveDTO(Map<String, Object> valueMap) {
        EmployeeSaveDTO employeeSaveDTO = JSON.parseObject(JSON.toJSONString(valueMap), EmployeeSaveDTO.class);
        if (Objects.nonNull(valueMap.get("employeeOrgLinkList"))) {
            List<Map<String, Object>> employeeOrgLinkList = (List<Map<String, Object>>) valueMap.get("employeeOrgLinkList");
            List<EmployeeOrgLinkDTO> employeeOrgLinkDTOS = JSON.parseArray(employeeOrgLinkList.toString(), EmployeeOrgLinkDTO.class);
            employeeSaveDTO.setEmployeeOrgLinkList(employeeOrgLinkDTOS);
        }
        return employeeSaveDTO;
    }
}

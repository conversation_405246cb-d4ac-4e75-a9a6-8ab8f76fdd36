package io.terminus.trantor.org.domain.service.strategy.attr;


import com.alibaba.fastjson.JSONArray;
import io.terminus.erp.strategy.Strategy;
import io.terminus.trantor.org.spi.dict.AttrDataTypeDict;
import io.terminus.trantor.org.spi.strategy.attr.AttrTypeTransStrategy;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-11-17 19:00
 */
@Component
@AllArgsConstructor
@Strategy(value = AttrTypeTransStrategy.class, implKey = AttrDataTypeDict.OBJECT)
public class ObjectMultiAttrTypeTransStrategy implements AttrTypeTransStrategy<List<Long>> {

    @Override
    public Boolean match(String attrDataType, Boolean isMulti) {
        return AttrDataTypeDict.OBJECT.equals(attrDataType) && isMulti;
    }

    @Override
    public List<Long> trans(String value) {
        if (StringUtils.hasText(value)) {
            return JSONArray.parseArray(value, Long.class);
        }
        return null;
    }
}

package io.terminus.trantor.org.domain.service;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.toolkit.SqlRunner;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.util.StrUtils;
import io.terminus.common.runtime.helper.SpringContextHelper;
import io.terminus.trantor.org.spi.msg.OrgMsg;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.jdbc.SQL;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: 张博
 * @date: 2023-03-23 13:47
 */
@Service
@RequiredArgsConstructor
public class BizTypeExtraService {

    public Map<String, Object> queryByOrgUnitId(String modelKey, Long orgUnitId) {
        String newModelKey = handleModelKey(modelKey);
        String sql = buildQuerySql(newModelKey);
        Map<String, Object> map = sqlRunner().selectOne(sql, orgUnitId);
        if (Objects.isNull(map)) {
            return null;
        }
        return map;
    }

    public void create(String modelKey, Map<String, Object> params) {
        String newModelKey = handleModelKey(modelKey);
        String sql = buildInsertSql(params, newModelKey);
        List<Object> param = buildParam(params);
        boolean result = sqlRunner().insert(sql, param.toArray());
        if (!result) {
            throw new BusinessException(OrgMsg.ORG_CREATE_BIZ_TYPE_EXTRA_FAIL);
        }
    }

    public void deleteByOrgUnitId(String modelKey, Long orgUnitId) {
        String newModelKey = handleModelKey(modelKey);
        String sql = buildDeleteByOrgUnitId(newModelKey);
        boolean result = sqlRunner().delete(sql, orgUnitId);
        if (!result) {
            throw new BusinessException(OrgMsg.ORG_DELETE_BIZ_TYPE_EXTRA_FAIL);
        }
    }

    public void update(String modelKey, Map<String, Object> params) {
        String newModelKey = handleModelKey(modelKey);
        Map<String, Object> newParam = filter(params);
        String sql = buildUpdateSql(newParam, newModelKey);
        List<Object> param = buildParam(newParam);
        boolean result = sqlRunner().update(sql, param.toArray());
        if (!result) {
            throw new BusinessException(OrgMsg.ORG_UPDATE_BIZ_TYPE_EXTRA_FAIL);
        }
    }


    public String buildQuerySql(String tableName) {
        return "select * from " + tableName + " where id = {0}";
    }

    public String handleModelKey(String modelKey) {
        int len = modelKey.indexOf("$");
        return modelKey.substring(len + 1);
    }

    public String buildDeleteByOrgUnitId(String tableName) {
        return "update " + tableName + " set deleted=1" + " where id = {0}";
    }

    private Map<String, Object> filter(Map<String, Object> params) {
        params.remove("createdAt");
        params.remove("updatedAt");
        params.remove("created_at");
        params.remove("updated_at");
        return params;
    }

    public String buildInsertSql(Map<String, Object> map, String tableName) {
        //存入key的字符串数组
        //存入value的字符串数组
        ArrayList<Object> arrValue = new ArrayList<>();
        //拼接sql
        ArrayList<Object> arrKey = new ArrayList<>(map.keySet());
        for (String keys : map.keySet()) {
            arrValue.add(map.get(keys));
        }
        StringBuilder strKey = new StringBuilder();
        StringBuilder strVal = new StringBuilder();
        //遍历存的key字符串数组拼接sql
        for (Object o : arrKey) {
            strKey.append(StrUtils.toUnderlineCase(o.toString()));
            strKey.append(",");
        }
        strKey.append("created_at");
        strKey.append(",");
        strKey.append("updated_at");
        //遍历存的value字符串数组拼接sql
        for (int j = 0; j < arrValue.size(); j++) {
            strVal.append("{").append(j).append("}");
            strVal.append(",");
        }
        strVal.append("now()");
        strVal.append(",");
        strVal.append("now()");
        return new SQL().INSERT_INTO(tableName).INTO_COLUMNS(strKey.toString()).INTO_VALUES(strVal.toString()).toString();
    }

    public String buildUpdateSql(Map<String, Object> map, String tableName) {
        Object id = map.get("id");
        //拼接sql
        ArrayList<Object> arrKey = new ArrayList<>(map.keySet());
        StringBuilder strKey = new StringBuilder();
        //遍历存的key字符串数组拼接sql
        for (int j = 0; j < arrKey.size(); j++) {
            strKey.append(StrUtils.toUnderlineCase(arrKey.get(j).toString()));
            strKey.append(" = ");
            strKey.append("{").append(j).append("}");
            if (j != arrKey.size() - 1) {//拼上","最后一个不拼
                strKey.append(",");
            }
        }
        return "update " + tableName + " set " + strKey + ",updated_at = now(),version = version + 1" + " where id = " + id;
    }

    public List<Object> buildParam(Map<String, Object> map) {
        ArrayList<Object> arrValue = new ArrayList<>();
        //拼接sql
        for (String keys : map.keySet()) {
            arrValue.add(map.get(keys));
        }
        return arrValue;
    }

    private SqlRunner sqlRunner() {
        try {
            SqlHelper.FACTORY = SpringContextHelper.getBean("erpSqlSessionFactory", SqlSessionFactory.class);
        } catch (Exception ignore) {

        }
        return new SqlRunner();
    }
}

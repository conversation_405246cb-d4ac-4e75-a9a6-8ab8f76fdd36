package io.terminus.trantor.org.domain.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.common.runtime.context.RequestContext;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.erp.strategy.StrategyLoader;
import io.terminus.iam.api.response.user.User;
import io.terminus.iam.sdk.client.IAMClient;
import io.terminus.trantor.org.domain.cache.OrgBusinessTypeCache;
import io.terminus.trantor.org.domain.cache.OrgStructCache;
import io.terminus.trantor.org.domain.constant.OrgConstants;
import io.terminus.trantor.org.domain.converter.OrgStructMdConverter;
import io.terminus.trantor.org.infrastructure.gateway.OrgMdGateWay;
import io.terminus.trantor.org.infrastructure.repo.*;
import io.terminus.trantor.org.spi.dict.OrgStatusDict;
import io.terminus.trantor.org.spi.model.PartnerDetailDTO;
import io.terminus.trantor.org.spi.model.dto.*;
import io.terminus.trantor.org.spi.model.dto.attr.GenAttrAndGroupDTO;
import io.terminus.trantor.org.spi.model.dto.attr.GenAttrDTO;
import io.terminus.trantor.org.spi.model.dto.attr.GenAttrGroupDTO;
import io.terminus.trantor.org.spi.model.dto.attr.OrgBusinessTypeDTO;
import io.terminus.trantor.org.spi.model.po.*;
import io.terminus.trantor.org.spi.msg.OrgMsg;
import io.terminus.trantor.org.spi.strategy.attr.AttrTypeTransStrategy;
import io.terminus.trantor.org.spi.utils.OrgFiledUtils;
import io.terminus.trantor2.common.TrantorContext;
import io.terminus.trantor2.common.iam.IamClientFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrgStructMdService {

    private final OrgStructMdRepo orgStructMdRepo;
    private final OrgDimensionCfRepo orgDimensionCfRepo;
    private final OrgStructMdConverter orgStructMdConverter;
    private final OrgStructAdjustHisMdRepo orgStructAdjustHisMdRepo;
    private final EmployeeOrgLinkRepo employeeOrgLinkRepo;
    private final IdGenerator idGenerator;
    private final OrgMdGateWay orgMdGateWay;
    private final IamClientFactory iamClientFactory;
    private final OrgStructEditionMdRepo orgStructEditionMdRepo;
    private final EmployeeRepo employeeRepo;
    private final OrgIdentityRepo orgIdentityRepo;
    private final OrgDimensionBusinessLinkRepo orgDimensionBusinessLinkRepo;
    private final RedisTemplate<String, Object> orgRedisTemplate;
    private final OrgBusinessTypeRepo orgBusinessTypeRepo;
    private final OrgStructCache orgStructCache;
    private final OrgBusinessTypeCache orgBusinessTypeCache;
    private final OrgStructRuleCfRepo orgStructRuleCfRepo;
    private final OrgStructBusinessTypeLinkRepo orgStructBusinessTypeLinkRepo;
    ScheduledExecutorService executor = Executors.newScheduledThreadPool(1, new ThreadFactoryBuilder().setNameFormat("org-build-path-pool-%d").build());


    //根据pid查询左侧组织树展开
    public List<OrgStructMdTreeDTO> findByPid(OrgStructMdQueryDTO request) {
        if (!StringUtils.hasText(request.getOrgDimensionCode())) {
            throw new BusinessException(OrgMsg.ORG_DIMENSION_CF_CODE_IS_NULL);
        }
        OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(request.getOrgDimensionCode());
        if (orgDimensionCfPO == null) {
            throw new BusinessException(OrgMsg.ORG_DIMENSION_CF_IS_NULL);
        }
        request.setOrgDimensionId(orgDimensionCfPO.getId());
        return orgStructMdConverter.convertDTO(orgStructMdRepo.findByPid(request));
    }

    public List<OrgStructMdDetailDTO> search(OrgStructMdQueryTreeDTO orgStructMdQueryTreeDTO) {
        if (!StringUtils.hasText(orgStructMdQueryTreeDTO.getOrgDimensionCode())) {
            throw new BusinessException(OrgMsg.ORG_PLEASE_CHOOSE_DIMENSION_FIRST);
        }
        OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(orgStructMdQueryTreeDTO.getOrgDimensionCode());
        orgStructMdQueryTreeDTO.setOrgDimensionId(orgDimensionCfPO.getId());
        Set<Long> orgStructIdSet = new HashSet<>();
        if (orgStructMdQueryTreeDTO.getOrgEditionDate() != null) {
            OrgStructMdHistoryQueryDTO orgStructMdHistoryQueryDTO = new OrgStructMdHistoryQueryDTO();
            orgStructMdHistoryQueryDTO.setOrgEditionDate(orgStructMdQueryTreeDTO.getOrgEditionDate().plusDays(+1));
            OrgStructEditionMdPO po = orgStructEditionMdRepo.selectTime(orgStructMdHistoryQueryDTO, orgDimensionCfPO.getId());
            if (po == null) {
                return null;
            }
            LocalDateTime time = po.getOrgEditionDate();
            LocalDateTime endOfDay = time.withHour(23).withMinute(59).withSecond(59);
            LocalDateTime startOfDay = time.withHour(0).withMinute(0).withSecond(0);
            List<OrgStructEditionMdPO> orgStructMdPOList = orgStructEditionMdRepo.search(orgStructMdQueryTreeDTO, startOfDay, endOfDay);
            if (CollectionUtils.isEmpty(orgStructMdPOList)) {
                return null;
            }
            for (OrgStructEditionMdPO orgStructMdPO : orgStructMdPOList) {
                List<OrgStructEditionMdPO> pathList = queryHisPath(orgStructMdPO, startOfDay, endOfDay);
                orgStructIdSet.addAll(pathList.stream().map(OrgStructEditionMdPO::getOrgStructId).collect(Collectors.toList()));
            }
        } else {
            List<OrgStructMdPO> orgStructMdPOList = orgStructMdRepo.search(orgStructMdQueryTreeDTO);
            if (CollectionUtils.isEmpty(orgStructMdPOList)) {
                return null;
            }
            for (OrgStructMdPO orgStructMdPO : orgStructMdPOList) {
                orgStructIdSet.addAll(orgStructMdPO.getPath());
            }
        }
        List<OrgStructMdPO> orgStructMdPOList1 = orgStructMdRepo.selectBatchIds(orgStructIdSet);
        List<OrgStructMdPO> rootList = orgStructMdPOList1.stream().filter(t -> Objects.isNull(t.getOrgParentId())).collect(Collectors.toList());
        List<Long> rootIdList = rootList.stream().map(OrgStructMdPO::getId).collect(Collectors.toList());
        orgStructMdPOList1.removeIf(next -> rootIdList.contains(next.getId()));
        List<OrgStructMdDetailDTO> orgStructMdDetailDTOS = orgStructMdConverter.convertDetialDTOList(orgStructMdPOList1);
        Map<Long, List<OrgStructMdDetailDTO>> groupByParent = orgStructMdDetailDTOS.stream().collect(Collectors.groupingBy(OrgStructMdDetailDTO::getOrgParentId));
        List<OrgStructMdDetailDTO> orgStructMdDetailDTOList = new ArrayList<>(rootIdList.size());
        for (OrgStructMdPO orgStructMdPO : rootList) {
            OrgStructMdDetailDTO orgStructMdDetailDTO = orgStructMdConverter.convertDetailDTO(orgStructMdPO);
            orgStructMdDetailDTO.setChild(getChildren(groupByParent, orgStructMdPO.getId()));
            orgStructMdDetailDTOList.add(orgStructMdDetailDTO);
        }
        return orgStructMdDetailDTOList;
    }

    private List<OrgStructMdDetailDTO> getChildren(Map<Long, List<OrgStructMdDetailDTO>> groupByParent, Long parentId) {
        List<OrgStructMdDetailDTO> orgStructMdPOList = groupByParent.get(parentId);
        if (CollectionUtils.isEmpty(orgStructMdPOList)) {
            return Collections.emptyList();
        }
        for (OrgStructMdDetailDTO orgStructMdDetailDTO : orgStructMdPOList) {
            orgStructMdDetailDTO.setChild(getChildren(groupByParent, orgStructMdDetailDTO.getId()));
        }
        return orgStructMdPOList;
    }

    public List<OrgStructMdTreePathDTO> findTreeByName(OrgStructMdQueryTreeDTO request) {
        if (!StringUtils.hasText(request.getOrgDimensionCode())) {
            throw new BusinessException(OrgMsg.ORG_DIMENSION_CF_CODE_IS_NULL);
        }
        OrgDimensionCfPO po = orgDimensionCfRepo.findByCode(request.getOrgDimensionCode());
        if (po == null) {
            throw new BusinessException(OrgMsg.ORG_DIMENSION_CF_IS_NULL);
        }
        List<OrgStructMdPO> treeByNameList = orgStructMdRepo.findTreeByName(request, po.getId());
        if (CollectionUtils.isEmpty(treeByNameList)) {
            return Collections.emptyList();
        }
        List<OrgStructMdTreePathDTO> convertedPathDTO = orgStructMdConverter.convertPathDTO(treeByNameList);

        for (OrgStructMdTreePathDTO currentOrg : convertedPathDTO) {
            Long searchPid = currentOrg.getOrgParentId() != null ? currentOrg.getOrgParentId() : null;
            ArrayList<Long> idList = new ArrayList<>();
            ArrayList<String> nameList = new ArrayList<>();
            idList.add(currentOrg.getId());
            nameList.add(currentOrg.getOrgName());

            buildPathRecursive(searchPid, idList, nameList, currentOrg);

            Collections.reverse(idList);
            Collections.reverse(nameList);
            currentOrg.setPathIdList(idList);
            currentOrg.setPathNameList(nameList);
        }

        return convertedPathDTO;
    }

    private void buildPathRecursive(Long searchPid, ArrayList<Long> idList, ArrayList<String> nameList, OrgStructMdTreePathDTO currentOrg) {
        if (searchPid == null) {
            return;
        }
        OrgStructMdPO parentOrg = orgStructMdRepo.selectById(searchPid);
        idList.add(parentOrg.getId());
        nameList.add(parentOrg.getOrgName());
        buildPathRecursive(parentOrg.getOrgParentId(), idList, nameList, currentOrg);
    }

    public List<OrgStructMdPO> queryPath(OrgStructMdPO orgStructMdPO) {
        List<OrgStructMdPO> path = new ArrayList<>();
        if (Objects.isNull(orgStructMdPO.getOrgParentId())) {
            path.add(orgStructMdPO);
        } else {
            recursion(path, orgStructMdPO);
        }
        Collections.reverse(path);
        return path;
    }

    private void recursion(List<OrgStructMdPO> list, OrgStructMdPO orgStructMdPO) {
        list.add(orgStructMdPO);
        if (Objects.isNull(orgStructMdPO.getOrgParentId())) {
            return;
        }
        OrgStructMdPO parent = orgStructMdRepo.selectById(orgStructMdPO.getOrgParentId());
        if (Objects.nonNull(parent)) {
            recursion(list, parent);
        }
    }

    private List<OrgStructEditionMdPO> queryHisPath(OrgStructEditionMdPO orgStructMdPO, LocalDateTime start, LocalDateTime end) {
        List<OrgStructEditionMdPO> path = new ArrayList<>();
        if (Objects.isNull(orgStructMdPO.getOrgEditionParentId())) {
            path.add(orgStructMdPO);
        } else {
            hisRecursion(path, orgStructMdPO, start, end);
        }
        Collections.reverse(path);
        return path;
    }

    private void hisRecursion(List<OrgStructEditionMdPO> list, OrgStructEditionMdPO orgStructMdPO, LocalDateTime start, LocalDateTime end) {
        list.add(orgStructMdPO);
        OrgStructEditionMdPO parent = orgStructEditionMdRepo.selectByStructId(orgStructMdPO.getOrgEditionParentId(), start, end);
        if (Objects.nonNull(parent)) {
            hisRecursion(list, parent, start, end);
        }
    }


    public OrgStructMdDetailWithTypeDTO detail(OrgStructMdHistoryDetialDTO request) {
        OrgStructMdPO structMdPO = orgStructMdRepo.selectById(request.getId());
        List<OrgBusinessTypePO> orgBusinessTypePOList = orgBusinessTypeRepo.selectBatchIds(structMdPO.getOrgBusinessTypeIds());
        Map<Long, OrgBusinessTypePO> orgBusinessTypePOMap = orgBusinessTypePOList.stream().collect(Collectors.toMap(OrgBusinessTypePO::getId, Function.identity()));
        Set<Long> attrGroupIdSet = orgBusinessTypePOList.stream().map(OrgBusinessTypePO::getAttrGroupId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<GenAttrAndGroupDTO> genAttrAndGroupDTOList = queryOrgAttrByGroupIds(attrGroupIdSet);
        // 查询成员信息
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryOrgUnitByUnitId(structMdPO.getId());
        Map<Long, EmployeePO> employeePOMap = null;
        Map<Long, OrgIdentityPO> orgIdentityPOMap = null;
        List<EmployeeOrgLinkDTO> employeeOrgLinkDTOS = null;
        if (!CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            Set<Long> employeeIdSet = employeeOrgLinkPOS.stream().filter(Objects::nonNull).map(EmployeeOrgLinkPO::getEmployeeId).collect(Collectors.toSet());
            List<EmployeePO> employeePOS = employeeRepo.selectBatchIds(employeeIdSet);
            if (!CollectionUtils.isEmpty(employeePOS)) {
                employeePOMap = employeePOS.stream().collect(Collectors.toMap(EmployeePO::getId, Function.identity()));
            }
            Set<Long> identityIdSet = employeeOrgLinkPOS.stream().filter(Objects::nonNull).map(EmployeeOrgLinkPO::getIdentityId).collect(Collectors.toSet());
            List<OrgIdentityPO> orgIdentityPOS = orgIdentityRepo.selectBatchIds(identityIdSet);
            if (!CollectionUtils.isEmpty(orgIdentityPOS)) {
                orgIdentityPOMap = orgIdentityPOS.stream().collect(Collectors.toMap(OrgIdentityPO::getId, Function.identity()));
            }
            employeeOrgLinkDTOS = orgStructMdConverter.convertToDTO(employeeOrgLinkPOS);
            for (EmployeeOrgLinkDTO employeeOrgLinkDTO : employeeOrgLinkDTOS) {
                if (!CollectionUtils.isEmpty(employeePOMap)) {
                    EmployeePO employeePO = employeePOMap.get(employeeOrgLinkDTO.getEmployeeId());
                    if (Objects.nonNull(employeePO)) {
                        employeeOrgLinkDTO.setEmployeeName(employeePO.getName());
                    }
                }
                if (!CollectionUtils.isEmpty(orgIdentityPOMap)) {
                    OrgIdentityPO orgIdentityPO = orgIdentityPOMap.get(employeeOrgLinkDTO.getIdentityId());
                    if (Objects.nonNull(orgIdentityPO)) {
                        employeeOrgLinkDTO.setIdentityName(orgIdentityPO.getName());
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(genAttrAndGroupDTOList)) {
            Class<OrgStructMdDetailWithTypeDTO> orgStructMdDetailWithTypeDTOClass = OrgStructMdDetailWithTypeDTO.class;
            OrgStructMdDetailWithTypeDTO orgStructMdDetailWithTypeDTO;
            try {
                orgStructMdDetailWithTypeDTO = OrgStructMdDetailWithTypeDTO.class.getDeclaredConstructor().newInstance();
                for (GenAttrAndGroupDTO genAttrAndGroupDTO : genAttrAndGroupDTOList) {
                    if (!CollectionUtils.isEmpty(genAttrAndGroupDTO.getGenAttrCfDTOS())) {
                        for (GenAttrDTO genAttrDTO : genAttrAndGroupDTO.getGenAttrCfDTOS()) {
                            Field field = orgStructMdDetailWithTypeDTOClass.getDeclaredField(genAttrDTO.getAttrField());
                            field.setAccessible(Boolean.TRUE);
                            Object fieldValue = OrgFiledUtils.getFieldValue(structMdPO, genAttrDTO.getAttrField());
                            if (fieldValue != null) {
                                AttrTypeTransStrategy attrTypeTransStrategy = StrategyLoader.load(AttrTypeTransStrategy.class, alg -> alg.match(genAttrDTO.getAttrDataType(), Objects.isNull(genAttrDTO.getAttrIsMulti()) ? Boolean.FALSE : genAttrDTO.getAttrIsMulti()));
                                field.set(orgStructMdDetailWithTypeDTO, attrTypeTransStrategy.trans(String.valueOf(fieldValue)));
                            }
                        }
                    }
                }
                orgStructMdDetailWithTypeDTO.setOrgCode(structMdPO.getOrgCode());
                orgStructMdDetailWithTypeDTO.setOrgName(structMdPO.getOrgName());
                orgStructMdDetailWithTypeDTO.setOrgParentId(structMdPO.getOrgParentId());
                orgStructMdDetailWithTypeDTO.setOrgEnableDate(structMdPO.getOrgEnableDate());
                orgStructMdDetailWithTypeDTO.setId(structMdPO.getId());
                orgStructMdDetailWithTypeDTO.setOrgStatus(structMdPO.getOrgStatus());
                orgStructMdDetailWithTypeDTO.setOrgBusinessTypeIds(structMdPO.getOrgBusinessTypeIds());
                orgStructMdDetailWithTypeDTO.setOrgBusinessTypeCodes(structMdPO.getOrgBusinessTypeCodes());
                orgStructMdDetailWithTypeDTO.setCreatedAt(structMdPO.getCreatedAt());
                orgStructMdDetailWithTypeDTO.setUpdatedAt(structMdPO.getUpdatedAt());
                orgStructMdDetailWithTypeDTO.setOrgSort(structMdPO.getOrgSort());
                orgStructMdDetailWithTypeDTO.setCreatedBy(structMdPO.getCreatedBy());
                orgStructMdDetailWithTypeDTO.setUpdatedBy(structMdPO.getUpdatedBy());
                orgStructMdDetailWithTypeDTO.setComOrgId(structMdPO.getComOrgId());
                orgStructMdDetailWithTypeDTO.setPartnerId(structMdPO.getPartnerId());
                if (Objects.nonNull(structMdPO.getPartnerId())) {
                    PartnerDetailDTO partnerDetailDTO = orgMdGateWay.queryPartnerDetailById(new IdRequest(structMdPO.getPartnerId()));
                    if (Objects.nonNull(partnerDetailDTO)) {
                        orgStructMdDetailWithTypeDTO.setPartnerName(partnerDetailDTO.getName());
                    }
                }
                if (Objects.nonNull(structMdPO.getComOrgId())) {
                    OrgStructMdPO comOrg = orgStructMdRepo.selectById(structMdPO.getComOrgId());
                    if (Objects.nonNull(comOrg)) {
                        orgStructMdDetailWithTypeDTO.setComOrgName(comOrg.getOrgName());
                    }
                }
                orgStructMdDetailWithTypeDTO.setEmployeeOrgLinkList(employeeOrgLinkDTOS);
                if (structMdPO.getOrgParentId() != null) {
                    OrgStructMdPO parentOrg = orgStructMdRepo.selectById(structMdPO.getOrgParentId());
                    orgStructMdDetailWithTypeDTO.setOrgParentName(parentOrg.getOrgName());
                    orgStructMdDetailWithTypeDTO.setOrgParentCode(parentOrg.getOrgCode());
                }
                Map<String, String> orgBusinessNameMap = new HashMap<>(orgBusinessTypePOList.size());
                for (Long orgBusinessTypeId : structMdPO.getOrgBusinessTypeIds()) {
                    OrgBusinessTypePO orgBusinessTypePO = orgBusinessTypePOMap.get(orgBusinessTypeId);
                    if (Objects.nonNull(orgBusinessTypePO)) {
                        orgBusinessNameMap.put(orgBusinessTypePO.getCode(), orgBusinessTypePO.getName());
                    }
                }
                orgStructMdDetailWithTypeDTO.setOrgBusinessTypeNameMap(orgBusinessNameMap);
                assembleUserInfo(orgStructMdDetailWithTypeDTO);
                if (Objects.nonNull(request.getHistoryId())) {
                    OrgStructEditionMdPO history = orgStructEditionMdRepo.selectById(request.getHistoryId());
                    orgStructMdDetailWithTypeDTO.setOrgParentId(history.getOrgEditionParentId());
                    orgStructMdDetailWithTypeDTO.setOrgStatus(history.getOrgEditionStatus());
                }
                orgStructMdDetailWithTypeDTO.setGenAttrAndGroupList(genAttrAndGroupDTOList);
                return orgStructMdDetailWithTypeDTO;
            } catch (Exception e) {
                log.error("属性映射转换异常", e);
                throw new BusinessException(e.getMessage());
            }
        }
        OrgStructMdDetailWithTypeDTO detailDTO = orgStructMdConverter.convert(structMdPO);
        detailDTO.setEmployeeOrgLinkList(employeeOrgLinkDTOS);
        if (structMdPO.getOrgParentId() != null) {
            OrgStructMdPO parentOrg = orgStructMdRepo.selectById(structMdPO.getOrgParentId());
            detailDTO.setOrgParentName(parentOrg.getOrgName());
            detailDTO.setOrgParentCode(parentOrg.getOrgCode());
        }
        if (Objects.nonNull(structMdPO.getPartnerId())) {
            PartnerDetailDTO partnerDetailDTO = orgMdGateWay.queryPartnerDetailById(new IdRequest(structMdPO.getPartnerId()));
            if (Objects.nonNull(partnerDetailDTO)) {
                detailDTO.setPartnerName(partnerDetailDTO.getName());
            }
        }
        if (Objects.nonNull(structMdPO.getComOrgId())) {
            OrgStructMdPO comOrg = orgStructMdRepo.selectById(structMdPO.getComOrgId());
            if (Objects.nonNull(comOrg)) {
                detailDTO.setComOrgName(comOrg.getOrgName());
            }
        }
        assembleUserInfo(detailDTO);
        detailDTO.setGenAttrAndGroupList(genAttrAndGroupDTOList);
        //历史记录的详情查询
        if (Objects.nonNull(request.getHistoryId())) {
            OrgStructEditionMdPO history = orgStructEditionMdRepo.selectById(request.getHistoryId());
            detailDTO.setOrgParentId(history.getOrgEditionParentId());
            detailDTO.setOrgStatus(history.getOrgEditionStatus());
        }
        return detailDTO;
    }

    public void assembleUserInfo(OrgStructMdDetailWithTypeDTO detailDTO) {
        if (Objects.nonNull(detailDTO.getCreatedBy())) {
            try {
                IAMClient iamClient = iamClientFactory.getIamClient();
                User user = iamClient.users().findById(detailDTO.getCreatedBy()).execute();
                if (Objects.nonNull(user)) {
                    if (StringUtils.hasText(user.getNickname())) {
                        detailDTO.setCreateUserName(user.getNickname());
                    } else {
                        detailDTO.setCreateUserName(user.getUsername());
                    }
                }
            } catch (Exception e) {
                log.error("查询用户信息出错", e);
            }
        }
        if (Objects.nonNull(detailDTO.getUpdatedBy())) {
            try {
                IAMClient iamClient = iamClientFactory.getIamClient();
                User user = iamClient.users().findById(detailDTO.getUpdatedBy()).execute();
                if (Objects.nonNull(user)) {
                    if (StringUtils.hasText(user.getNickname())) {
                        detailDTO.setUpdateUserName(user.getNickname());
                    } else {
                        detailDTO.setUpdateUserName(user.getUsername());
                    }
                }
            } catch (Exception e) {
                log.error("查询用户信息出错", e);
            }
        }
    }

    public List<OrgBusinessTypeDTO> findType(OrgDimensionDTO request) {
        //调接口查询属性  传维度编码
        OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(request.getOrgDimensionCode());
        if (Objects.isNull(orgDimensionCfPO)) {
            return Collections.emptyList();
        }
        return queryAttrByDimensionId(orgDimensionCfPO.getId());
    }

    public GenAttrAndGroupDTO queryOrgAttrByGroupId(Long attrGroupId) {
        GenAttrGroupDTO genAttrGroupDTO = orgMdGateWay.findAttrGroupById(new IdRequest(attrGroupId));
        if (Objects.isNull(genAttrGroupDTO)) {
            return null;
        }
        GenAttrAndGroupDTO attrAndGroupDTO = new GenAttrAndGroupDTO();
        attrAndGroupDTO.setCode(genAttrGroupDTO.getCode());
        attrAndGroupDTO.setName(genAttrGroupDTO.getName());
        attrAndGroupDTO.setAttrClass(genAttrGroupDTO.getAttrClass());
        attrAndGroupDTO.setStatus(genAttrGroupDTO.getStatus());
        attrAndGroupDTO.setRemark(genAttrGroupDTO.getRemark());
        attrAndGroupDTO.setAttrGroupId(genAttrGroupDTO.getId());

        List<GenAttrDTO> genAttrDTOS = orgMdGateWay.findAttrByAttrGroupId(new IdRequest(attrGroupId));
        if (CollectionUtils.isEmpty(genAttrDTOS)) {
            return attrAndGroupDTO;
        }
        List<GenAttrDTO> enableGenAttrDTOS = genAttrDTOS.stream()
                .filter(genAttrCfPO -> genAttrCfPO.getStatus().equals(OrgStatusDict.ENABLED))
                .collect(Collectors.toList());
        attrAndGroupDTO.setGenAttrCfDTOS(enableGenAttrDTOS);
        return attrAndGroupDTO;
    }

    public List<GenAttrAndGroupDTO> queryOrgAttrByGroupIds(Set<Long> attrGroupIds) {
        if (CollectionUtils.isEmpty(attrGroupIds)) {
            return Collections.emptyList();
        }
        List<GenAttrGroupDTO> genAttrGroupDTOList = orgMdGateWay.findAttrGroupByIds(new IdsRequest(attrGroupIds));
        if (CollectionUtils.isEmpty(genAttrGroupDTOList)) {
            return Collections.emptyList();
        }
        List<GenAttrAndGroupDTO> genAttrAndGroupDTOList = new ArrayList<>(genAttrGroupDTOList.size());
        for (GenAttrGroupDTO genAttrGroupDTO : genAttrGroupDTOList) {
            GenAttrAndGroupDTO attrAndGroupDTO = new GenAttrAndGroupDTO();
            attrAndGroupDTO.setCode(genAttrGroupDTO.getCode());
            attrAndGroupDTO.setName(genAttrGroupDTO.getName());
            attrAndGroupDTO.setAttrClass(genAttrGroupDTO.getAttrClass());
            attrAndGroupDTO.setStatus(genAttrGroupDTO.getStatus());
            attrAndGroupDTO.setRemark(genAttrGroupDTO.getRemark());
            attrAndGroupDTO.setAttrGroupId(genAttrGroupDTO.getId());
            List<GenAttrDTO> genAttrDTOS = orgMdGateWay.findAttrByAttrGroupId(new IdRequest(genAttrGroupDTO.getId()));
            if (!CollectionUtils.isEmpty(genAttrDTOS)) {
                List<GenAttrDTO> enableGenAttrDTOS = genAttrDTOS.stream()
                        .filter(genAttrCfPO -> genAttrCfPO.getStatus().equals(OrgStatusDict.ENABLED))
                        .collect(Collectors.toList());
                attrAndGroupDTO.setGenAttrCfDTOS(enableGenAttrDTOS);
            }
            genAttrAndGroupDTOList.add(attrAndGroupDTO);
        }
        return genAttrAndGroupDTOList;
    }

    public List<OrgBusinessTypeDTO> queryAttrByDimensionId(Long orgDimensionId) {
        List<OrgDimensionBusinessLinkPO> orgDimensionBusinessLinkPOS = orgDimensionBusinessLinkRepo.queryGroupIdLinkByDimensionId(orgDimensionId);
        if (CollectionUtils.isEmpty(orgDimensionBusinessLinkPOS)) {
            return Collections.emptyList();
        }
        Set<Long> orgBusinessTypeIdSet = orgDimensionBusinessLinkPOS.stream().map(OrgDimensionBusinessLinkPO::getOrgBusinessTypeId).collect(Collectors.toSet());
        List<OrgBusinessTypePO> orgBusinessTypePOList = orgBusinessTypeRepo.selectBatchIds(orgBusinessTypeIdSet);
        if (CollectionUtils.isEmpty(orgBusinessTypePOList)) {
            return Collections.emptyList();
        }
        List<OrgBusinessTypePO> enableOrgBusinessTypeList = orgBusinessTypePOList.stream().filter(t -> OrgStatusDict.ENABLED.equals(t.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(enableOrgBusinessTypeList)) {
            return Collections.emptyList();
        }
        Set<Long> attrGroupIdSet = enableOrgBusinessTypeList.stream().map(OrgBusinessTypePO::getAttrGroupId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, GenAttrGroupDTO> genAttrGroupDTOMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(attrGroupIdSet)) {
            List<GenAttrGroupDTO> genAttrGroupDTOList = orgMdGateWay.findAttrGroupByIds(new IdsRequest(attrGroupIdSet));
            if (!CollectionUtils.isEmpty(genAttrGroupDTOList)) {
                List<GenAttrGroupDTO> enableGenAttrGroupList = genAttrGroupDTOList.stream().filter(t -> OrgStatusDict.ENABLED.equals(t.getStatus())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(enableGenAttrGroupList)) {
                    genAttrGroupDTOMap = enableGenAttrGroupList.stream().collect(Collectors.toMap(GenAttrGroupDTO::getId, Function.identity(), (o1, o2) -> o1));
                }
            }
        }
        List<OrgBusinessTypeDTO> orgBusinessTypeDTOList = new ArrayList<>(enableOrgBusinessTypeList.size());
        for (OrgBusinessTypePO orgBusinessTypePO : enableOrgBusinessTypeList) {
            OrgBusinessTypeDTO orgBusinessTypeDTO = new OrgBusinessTypeDTO();
            orgBusinessTypeDTO.setId(orgBusinessTypePO.getId());
            orgBusinessTypeDTO.setCode(orgBusinessTypePO.getCode());
            orgBusinessTypeDTO.setName(orgBusinessTypePO.getName());
            orgBusinessTypeDTO.setStatus(orgBusinessTypePO.getStatus());
            if (Objects.nonNull(orgBusinessTypePO.getAttrGroupId())) {
                GenAttrGroupDTO genAttrGroupDTO = genAttrGroupDTOMap.get(orgBusinessTypePO.getAttrGroupId());
                if (Objects.nonNull(genAttrGroupDTO)) {
                    List<GenAttrDTO> genAttrDTOList = orgMdGateWay.findAttrByAttrGroupId(new IdRequest(genAttrGroupDTO.getId()));
                    if (CollectionUtils.isEmpty(genAttrDTOList)) {
                        orgBusinessTypeDTOList.add(orgBusinessTypeDTO);
                        continue;
                    }
                    List<GenAttrDTO> enableGenAttrDTOS = genAttrDTOList.stream()
                            .filter(genAttrCfPO -> genAttrCfPO.getStatus().equals(OrgStatusDict.ENABLED))
                            .collect(Collectors.toList());
                    orgBusinessTypeDTO.setGenAttrCfDTOS(enableGenAttrDTOS);
                }
            }
            orgBusinessTypeDTOList.add(orgBusinessTypeDTO);
        }
        return orgBusinessTypeDTOList;
    }

    public void delete(IdRequest request) {
        Set<Long> list = new HashSet<>();
        list.add(request.getId());
        OrgStructMdPO structMdPO = orgStructMdRepo.selectById(request.getId());
        getChildrenDisableByParentId(list, structMdPO.getId());
        orgStructMdRepo.deleteBatchIds(list);
        if (!Objects.isNull(structMdPO.getOrgParentId())) {
            List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.selectChildren(structMdPO.getOrgParentId());
            if (CollectionUtils.isEmpty(orgStructMdPOS)) {
                OrgStructMdPO parent = orgStructMdRepo.selectById(structMdPO.getOrgParentId());
                if (Boolean.TRUE != parent.getLeaf()) {
                    parent.setLeaf(Boolean.TRUE);
                    orgStructMdRepo.updateById(parent);
                }
            }
        }
        // 删除组织和员工的关联关系
        employeeOrgLinkRepo.deleteByOrgIds(list);
    }

    private void getChildrenDisableByParentId(Set<Long> list, Long id) {
        List<OrgStructMdPO> childrenOrg = orgStructMdRepo.selectChildren(id);
        if (CollectionUtils.isEmpty(childrenOrg)) {
            return;
        }
        Set<Long> ids = childrenOrg.stream().map(OrgStructMdPO::getId).collect(Collectors.toSet());
        for (Long childrenId : ids) {
            list.add(childrenId);
            getChildrenDisableByParentId(list, childrenId);
        }
    }

    @Transactional
    public OrgStructMdSaveDTO save(OrgStructMdSaveDTO request) {
        // 数据校验
        this.baseCheck(request);
        OrgStructMdPO structMdPO = orgStructMdConverter.convertSaveDTO(request);
        if (Objects.isNull(request.getId())) {
            structMdPO.setId(idGenerator.nextId(OrgStructMdPO.class));
        }
        if (Objects.isNull(structMdPO.getOrgSort())) {
            structMdPO.setOrgSort(1);
        }
        // 如果当前组织的类型包含公司组织，则关联的公司组织就是他自己
        if (structMdPO.getOrgBusinessTypeCodes().contains("COM_ORG")) {
            structMdPO.setComOrgId(structMdPO.getId());
        }
        if (Objects.isNull(request.getId())) {
            structMdPO.setLeaf(Boolean.TRUE);
            if (!StringUtils.hasText(request.getOrgStatus())) {
                structMdPO.setOrgStatus(OrgStatusDict.DRAFT);
            }
            this.buildCreatePath(structMdPO);
            orgStructMdRepo.insert(structMdPO);
        } else {
            //不能选择自己跟自己下级节点为父节点
            OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectById(structMdPO.getId());
            List<Long> path = orgStructMdPO.getPath();
            int i = path.indexOf(structMdPO.getId());
            List<Long> subPath = path.subList(i, path.size());
            if (subPath.contains(request.getOrgParentId())) {
                throw new BusinessException(OrgMsg.ORG_STRUCT_PARENT_ERROR);
            }
            orgStructMdRepo.updateNull(orgStructMdPO);
            if (orgStructMdPO.getOrgParentId() != null && !orgStructMdPO.getOrgParentId().equals(structMdPO.getOrgParentId())) {
                if (OrgStatusDict.ENABLED.equals(orgStructMdPO.getOrgStatus()) || OrgStatusDict.DISABLED.equals(orgStructMdPO.getOrgStatus())) {
                    OrgStructAdjustHisMdPO hisMdPO = new OrgStructAdjustHisMdPO();
                    hisMdPO.setOrgStructId(structMdPO.getId());
                    hisMdPO.setOrgFormerAdjustParentId(orgStructMdPO.getOrgParentId());
                    hisMdPO.setOrgAdjustParentId(structMdPO.getOrgParentId());
                    hisMdPO.setOrgFormerAdjustStatus(orgStructMdPO.getOrgStatus());
                    hisMdPO.setOrgAdjustStatus(structMdPO.getOrgStatus());
                    hisMdPO.setOrgHisAdjustEnableDate(LocalDateTime.now());
                    orgStructAdjustHisMdRepo.insert(hisMdPO);
                }
            }
            orgStructMdRepo.updateById(structMdPO);
            if (Objects.nonNull(orgStructMdPO.getOrgParentId())) {
                List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.selectChildren(orgStructMdPO.getOrgParentId());
                if (CollectionUtils.isEmpty(orgStructMdPOS)) {
                    orgStructMdRepo.updateLeaf(orgStructMdPO.getOrgParentId());
                }
            }
            // 构建新路径
            if (Objects.nonNull(structMdPO.getOrgParentId())) {
                if (!structMdPO.getOrgParentId().equals(orgStructMdPO.getOrgParentId())) {
                    String appKey = RequestContext.getAppKey();
                    Long originOrgId = RequestContext.getOriginOrgId();
                    Long tenantId = RequestContext.getTenantId();
                    String portalCode = TrantorContext.getPortalCode();
                    executor.schedule(() -> {
                        RequestContext.setAppKey(appKey);
                        RequestContext.setOriginOrgId(originOrgId);
                        RequestContext.setTenantId(tenantId);
                        TrantorContext.init();
                        TrantorContext.setTeamId(tenantId);
                        TrantorContext.setOriginOrgId(originOrgId);
                        TrantorContext.setPortalCode(portalCode);
                        this.buildUpdatePath(structMdPO);
                    }, 10, TimeUnit.SECONDS);
                }
            }
        }
        if (!Objects.isNull(structMdPO.getOrgParentId())) {
            OrgStructMdPO parent = orgStructMdRepo.selectById(structMdPO.getOrgParentId());
            if (Boolean.TRUE == parent.getLeaf()) {
                parent.setLeaf(Boolean.FALSE);
                orgStructMdRepo.updateById(parent);
            }
        }
        // 处理成员信息
//        handleOrgStructEmployee(request, structMdPO);
        // 处理业务类型和组织单元的关联关系
        handleOrgBusinessTypeLink(request, structMdPO, request.getOrgBusinessTypeIds());
        request.setId(structMdPO.getId());
        orgStructCache.clear(structMdPO.getId());
        return request;
    }

    private void buildCreatePath(OrgStructMdPO orgStructMdPO) {
        if (Objects.isNull(orgStructMdPO.getOrgParentId())) {
            orgStructMdPO.setPath(Lists.newArrayList(orgStructMdPO.getId()));
        } else {
            OrgStructMdPO parent = orgStructMdRepo.selectById(orgStructMdPO.getOrgParentId());
            if (CollectionUtils.isEmpty(parent.getPath())) {
                List<OrgStructMdPO> orgStructMdPOS = queryPath(orgStructMdPO);
                orgStructMdPO.setPath(orgStructMdPOS.stream().map(OrgStructMdPO::getId).collect(Collectors.toList()));
            } else {
                List<Long> path = new ArrayList<>(parent.getPath());
                path.add(orgStructMdPO.getId());
                orgStructMdPO.setPath(path);
            }
        }
    }

    private void buildUpdatePath(OrgStructMdPO orgStructMdPO) {
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryAllChild(orgStructMdPO.getId());
        for (OrgStructMdPO structMdPO : orgStructMdPOS) {
            List<OrgStructMdPO> orgStructMdPOList = queryPath(structMdPO);
            structMdPO.setPath(orgStructMdPOList.stream().map(OrgStructMdPO::getId).collect(Collectors.toList()));
            orgStructMdRepo.updateById(structMdPO);
        }
    }

    /**
     * 基础数据校验
     *
     * @param request
     */
    private void baseCheck(OrgStructMdSaveDTO request) {
        if (!StringUtils.hasText(request.getOrgDimensionCode())) {
            throw new BusinessException(OrgMsg.ORG_DIMENSION_CF_CODE_IS_NULL);
        }
        if (StringUtils.isEmpty(request.getOrgBusinessTypeIds())) {
            throw new BusinessException(OrgMsg.ORG_BUSINESS_TYPE_IS_NULL);
        }
        OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(request.getOrgDimensionCode());
        if (orgDimensionCfPO == null) {
            throw new BusinessException(OrgMsg.ORG_DIMENSION_CF_IS_NULL);
        }
        request.setOrgDimensionId(orgDimensionCfPO.getId());
        // 判断是否支持多根组织
        if (Objects.isNull(request.getId()) && Objects.isNull(request.getOrgParentId())) {
            if (Objects.nonNull(orgDimensionCfPO.getIsSupMultiRoot()) && !orgDimensionCfPO.getIsSupMultiRoot()) {
                OrgStructMdPO orgStructMdPO = orgStructMdRepo.queryRoot(orgDimensionCfPO.getId());
                if (Objects.nonNull(orgStructMdPO)) {
                    throw new BusinessException(OrgMsg.ORG_STRUCT_CURRENT_NOT_SUP_MULTI_ROOT);
                }
            }
        }
        if (Objects.nonNull(request.getId()) && Objects.isNull(request.getOrgParentId())) {
            if (Objects.nonNull(orgDimensionCfPO.getIsSupMultiRoot()) && !orgDimensionCfPO.getIsSupMultiRoot()) {
                OrgStructMdPO orgStructMdPO = orgStructMdRepo.queryRoot(orgDimensionCfPO.getId());
                if (Objects.nonNull(orgStructMdPO) && !orgStructMdPO.getId().equals(request.getId())) {
                    throw new BusinessException(OrgMsg.ORG_STRUCT_CURRENT_NOT_SUP_MULTI_ROOT);
                }
            }
        }
        OrgStructMdPO byCode = orgStructMdRepo.selectByCode(orgDimensionCfPO.getId(), request.getOrgCode());
        if (Objects.nonNull(request.getId())) {
            if (Objects.nonNull(byCode)) {
                if (!byCode.getId().equals(request.getId())) {
                    throw new BusinessException(OrgMsg.ORG_CODE_IS_EXIST);
                }
            }
        } else if (Objects.nonNull(byCode)) {
            throw new BusinessException(OrgMsg.ORG_CODE_IS_EXIST);
        }
        if (Objects.nonNull(request.getOrgParentId())) {
            OrgStructMdPO parentStruct = orgStructMdRepo.selectById(request.getOrgParentId());
            if (OrgStatusDict.DISABLED.equals(parentStruct.getOrgStatus())) {
                throw new BusinessException(OrgMsg.ORG_STRUCT_IS_DISABLED);
            }
            if (parentStruct.getOrgEnableDate().isAfter(request.getOrgEnableDate()) && !OrgStatusDict.ENABLED.equals(parentStruct.getOrgStatus())) {
                throw new BusinessException(OrgMsg.ORG_STRUCT_ENABLE_DATE_IS_BEFORE_PARENT);
            }
        }
        List<OrgBusinessTypePO> orgBusinessTypePOList = orgBusinessTypeRepo.selectBatchIds(request.getOrgBusinessTypeIds());
        List<String> orgBusinessCodeList = orgBusinessTypePOList.stream().map(OrgBusinessTypePO::getCode).collect(Collectors.toList());
        List<Long> orgbusinessTypeIdList = orgBusinessTypePOList.stream().map(OrgBusinessTypePO::getId).collect(Collectors.toList());
        request.setOrgBusinessTypeIds(orgbusinessTypeIdList);
        request.setOrgBusinessTypeCodes(orgBusinessCodeList);
        if (!orgBusinessCodeList.contains("COM_ORG")) {
            /**
             * 如果当前组织是采购组织，销售组织或者库存组织时，更新操作需要校验
             * 当前的公司组织是否为当前组织的上级组织中的节点
             */
            if (orgBusinessCodeList.contains("SLS_ORG") || orgBusinessCodeList.contains("INV_ORG") || orgBusinessCodeList.contains("PUR_ORG")) {
                if (Objects.nonNull(request.getComOrgId())) {
                    if (Objects.nonNull(request.getId())) {
                        if (Objects.nonNull(request.getOrgParentId())) {
                            OrgParentQueryDTO orgParentQueryDTO = new OrgParentQueryDTO();
                            orgParentQueryDTO.setId(request.getOrgParentId());
                            orgParentQueryDTO.setOrgBusinessTypeCode("COM_ORG");
                            OrgStructMdDetailDTO orgStructMdDetailDTO = queryParentOrgByType(orgParentQueryDTO);
                            if (Objects.nonNull(orgStructMdDetailDTO)) {
                                if (!orgStructMdDetailDTO.getId().equals(request.getComOrgId())) {
                                    throw new BusinessException(OrgMsg.ORG_CURRENT_COM_ORG_IS_NOT_CORRECT);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private void handleOrgBusinessTypeLink(OrgStructMdSaveDTO request, OrgStructMdPO structMdPO, List<Long> orgbusinessTypeIdList) {
        // 处理关联表
        if (Objects.nonNull(request.getId())) {
            orgStructBusinessTypeLinkRepo.deleteByOrgStructId(request.getId());
        }
        List<OrgStructBusinessTypeLinkPO> orgStructBusinessTypeLinkPOS = new ArrayList<>(orgbusinessTypeIdList.size());
        for (Long orgBusinessTypeId : orgbusinessTypeIdList) {
            OrgStructBusinessTypeLinkPO orgStructBusinessTypeLinkPO = new OrgStructBusinessTypeLinkPO();
            orgStructBusinessTypeLinkPO.setOrgStructId(structMdPO.getId());
            orgStructBusinessTypeLinkPO.setOrgBusinessTypeId(orgBusinessTypeId);
            orgStructBusinessTypeLinkPOS.add(orgStructBusinessTypeLinkPO);
        }
        if (!CollectionUtils.isEmpty(orgStructBusinessTypeLinkPOS)) {
            orgStructBusinessTypeLinkRepo.insertBatch(orgStructBusinessTypeLinkPOS);
        }
    }

    private void handleOrgStructEmployee(OrgStructMdSaveDTO request, OrgStructMdPO structMdPO) {
        if (Objects.nonNull(request.getId())) {
            List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryOrgUnitByUnitId(structMdPO.getId());
            if (!CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
                for (EmployeeOrgLinkPO employeeOrgLinkPO : employeeOrgLinkPOS) {
                    orgRedisTemplate.delete(OrgConstants.ORG_REC_USER_CHARGE_ORG_PREFIX + employeeOrgLinkPO.getEmployeeId());
                }
            }
            employeeOrgLinkRepo.deleteByOrgId(request.getId());
        }
        if (!CollectionUtils.isEmpty(request.getEmployeeOrgLinkList())) {
            Set<String> key = request.getEmployeeOrgLinkList().stream().map(t -> t.getEmployeeId().toString() + t.getIdentityId()).collect(Collectors.toSet());
            if (key.size() != request.getEmployeeOrgLinkList().size()) {
                throw new BusinessException(OrgMsg.ORG_STRUCT_MEMBER_REPEAT_RECORD);
            }
            for (EmployeeOrgLinkDTO employeeOrgLinkDTO : request.getEmployeeOrgLinkList()) {
                EmployeeOrgLinkPO employeeOrgLinkPO = new EmployeeOrgLinkPO();
                employeeOrgLinkPO.setId(null);
                employeeOrgLinkPO.setOrgUnitId(structMdPO.getId());
                employeeOrgLinkPO.setIdentityId(employeeOrgLinkDTO.getIdentityId());
                employeeOrgLinkPO.setEmployeeId(employeeOrgLinkDTO.getEmployeeId());
                employeeOrgLinkRepo.insert(employeeOrgLinkPO);

                orgRedisTemplate.delete(OrgConstants.ORG_REC_USER_CHARGE_ORG_PREFIX + employeeOrgLinkDTO.getEmployeeId());
            }
        }
    }

    @Transactional
    public void enabled(IdRequest request) {
        OrgStructMdPO structMdPO = orgStructMdRepo.selectById(request.getId());
        if (Objects.nonNull(structMdPO.getOrgParentId())) {
            OrgStructMdPO parent = orgStructMdRepo.selectById(structMdPO.getOrgParentId());
            if (!OrgStatusDict.ENABLED.equals(parent.getOrgStatus())) {
                throw new BusinessException(OrgMsg.ORG_PARENT_IS_NOT_ENABLE);
            }
        }
        if (structMdPO.getOrgEnableDate().isBefore(LocalDateTime.now())) {
            orgStructMdRepo.updateStatusById(structMdPO.getId());
            OrgStructAdjustHisMdPO hisMdPO = new OrgStructAdjustHisMdPO();
            hisMdPO.setOrgStructId(structMdPO.getId());
            hisMdPO.setOrgFormerAdjustParentId(structMdPO.getOrgParentId());
            hisMdPO.setOrgAdjustParentId(structMdPO.getOrgParentId());
            hisMdPO.setOrgFormerAdjustStatus(structMdPO.getOrgStatus());
            hisMdPO.setOrgAdjustStatus(OrgStatusDict.ENABLED);
            hisMdPO.setOrgHisAdjustEnableDate(LocalDateTime.now());
            orgStructAdjustHisMdRepo.insert(hisMdPO);
            getChildrenByParentId(structMdPO.getId());
        } else {
            structMdPO.setOrgStatus(OrgStatusDict.INACTIVE);
            orgStructMdRepo.updateById(structMdPO);
        }
        //查询草稿状态的子组织
        getChildrenByParentId(structMdPO.getId());


    }

    private void getChildrenByParentId(Long searchPid) {
        List<OrgStructMdPO> childrenOrg = orgStructMdRepo.selectChildren(searchPid);
        if (CollectionUtils.isEmpty(childrenOrg)) {
            return;
        }
        for (OrgStructMdPO po : childrenOrg) {
            //必须草稿状态才加
            if (OrgStatusDict.DRAFT.equals(po.getOrgStatus())) {
                //启动时间等于当前时间 启动 否则修改为待启动状态
                if (po.getOrgEnableDate().isBefore(LocalDateTime.now())) {
                    orgStructMdRepo.updateStatusById(po.getId());
                    OrgStructAdjustHisMdPO hisMdPO = new OrgStructAdjustHisMdPO();
                    hisMdPO.setOrgStructId(po.getId());
                    hisMdPO.setOrgFormerAdjustParentId(po.getOrgParentId());
                    hisMdPO.setOrgAdjustParentId(po.getOrgParentId());
                    hisMdPO.setOrgFormerAdjustStatus(po.getOrgStatus());
                    hisMdPO.setOrgAdjustStatus(OrgStatusDict.ENABLED);
                    hisMdPO.setOrgHisAdjustEnableDate(LocalDateTime.now());
                    orgStructAdjustHisMdRepo.insert(hisMdPO);
                } else {
                    po.setOrgStatus(OrgStatusDict.INACTIVE);
                    orgStructMdRepo.updateById(po);
                }

            }
            getChildrenByParentId(po.getId());
        }
    }

    public void disabled(IdRequest request) {
//        a. 将已启用状态的组织及其所有子组织全部改成停用状态，立即生效。
//        b. 如果子组织中有草稿/待启用组织，就会把这些组织全部删掉。
        Set<OrgStructMdPO> enableList = new HashSet<>();
        Set<Long> draftList = new HashSet<>();
        OrgStructMdPO structMdPO = orgStructMdRepo.selectById(request.getId());
        OrgStructAdjustHisMdPO hisMdPO = new OrgStructAdjustHisMdPO();
        hisMdPO.setOrgStructId(structMdPO.getId());
        hisMdPO.setOrgFormerAdjustParentId(structMdPO.getOrgParentId());
        hisMdPO.setOrgAdjustParentId(structMdPO.getOrgParentId());
        hisMdPO.setOrgFormerAdjustStatus(structMdPO.getOrgStatus());
        hisMdPO.setOrgAdjustStatus(OrgStatusDict.DISABLED);
        hisMdPO.setOrgHisAdjustEnableDate(LocalDateTime.now());
        orgStructAdjustHisMdRepo.insert(hisMdPO);
        getChildrenStatusByParentId(enableList, draftList, structMdPO.getId());
        if (!CollectionUtils.isEmpty(enableList)) {
            for (OrgStructMdPO orgStructMdPO : enableList) {
                orgStructMdPO.setOrgStatus(OrgStatusDict.DISABLED);
                orgStructMdRepo.updateById(orgStructMdPO);
            }
        }
        if (!CollectionUtils.isEmpty(draftList)) {
            orgStructMdRepo.deleteBatchIds(draftList);
        }
        for (OrgStructMdPO orgStructMdPO : enableList) {
            List<OrgStructMdPO> dto = orgStructMdRepo.selectChildren(orgStructMdPO.getId());
            if (CollectionUtils.isEmpty(dto)) {
                OrgStructMdPO mdPO = orgStructMdRepo.selectById(orgStructMdPO.getId());
                if (Boolean.TRUE != mdPO.getLeaf()) {
                    mdPO.setLeaf(Boolean.TRUE);
                    orgStructMdRepo.updateById(mdPO);
                }
            }
        }
    }

    private void getChildrenStatusByParentId(Set<OrgStructMdPO> enableList, Set<Long> draftList, Long id) {
        List<OrgStructMdPO> childrenOrg = orgStructMdRepo.queryAllChild(id);
        if (CollectionUtils.isEmpty(childrenOrg)) {
            return;
        }
        for (OrgStructMdPO po : childrenOrg) {
            //必须草稿状态才加
            if (OrgStatusDict.DRAFT.equals(po.getOrgStatus()) || OrgStatusDict.INACTIVE.equals(po.getOrgStatus())) {
                draftList.add(po.getId());
            }
            if (OrgStatusDict.ENABLED.equals(po.getOrgStatus())) {
                enableList.add(po);
                OrgStructAdjustHisMdPO hisMdPO = new OrgStructAdjustHisMdPO();
                hisMdPO.setOrgStructId(po.getId());
                hisMdPO.setOrgFormerAdjustParentId(po.getOrgParentId());
                hisMdPO.setOrgAdjustParentId(po.getOrgParentId());
                hisMdPO.setOrgFormerAdjustStatus(OrgStatusDict.ENABLED);
                hisMdPO.setOrgAdjustStatus(OrgStatusDict.DISABLED);
                hisMdPO.setOrgHisAdjustEnableDate(LocalDateTime.now());
                orgStructAdjustHisMdRepo.insert(hisMdPO);
            }
        }
    }

    public void enabledJob() {
        LocalDateTime startTime = LocalDateTime.now().with(LocalTime.MIN);
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryNeedEnableRecord(startTime);
        log.info("需要启用的组织的数量为:{}", orgStructMdPOS.size());
        if (CollectionUtils.isEmpty(orgStructMdPOS)) {
            return;
        }
        for (OrgStructMdPO orgStructMdPO : orgStructMdPOS) {
            // 查询下级节点
            List<OrgStructMdPO> childList = new ArrayList<>();
            getChildren(childList, orgStructMdPO.getId());
            if (!CollectionUtils.isEmpty(childList)) {
                for (OrgStructMdPO structMdPO : childList) {
                    if (OrgStatusDict.DRAFT.equals(structMdPO.getOrgStatus()) || OrgStatusDict.INACTIVE.equals(structMdPO.getOrgStatus())) {
                        structMdPO.setOrgStatus(OrgStatusDict.ENABLED);
                        orgStructMdRepo.updateById(structMdPO);
                        // 插入历史记录
                        OrgStructAdjustHisMdPO hisMdPO = new OrgStructAdjustHisMdPO();
                        hisMdPO.setOrgStructId(structMdPO.getId());
                        hisMdPO.setOrgFormerAdjustParentId(structMdPO.getOrgParentId());
                        hisMdPO.setOrgAdjustParentId(structMdPO.getOrgParentId());
                        hisMdPO.setOrgFormerAdjustStatus(structMdPO.getOrgStatus());
                        hisMdPO.setOrgAdjustStatus(OrgStatusDict.ENABLED);
                        hisMdPO.setOrgHisAdjustEnableDate(LocalDateTime.now());
                        orgStructAdjustHisMdRepo.insert(hisMdPO);
                    }
                }
            }
            orgStructMdPO.setOrgStatus(OrgStatusDict.ENABLED);
            orgStructMdRepo.updateById(orgStructMdPO);
            // 插入历史记录
            OrgStructAdjustHisMdPO hisMdPO = new OrgStructAdjustHisMdPO();
            hisMdPO.setOrgStructId(orgStructMdPO.getId());
            hisMdPO.setOrgFormerAdjustParentId(orgStructMdPO.getOrgParentId());
            hisMdPO.setOrgAdjustParentId(orgStructMdPO.getOrgParentId());
            hisMdPO.setOrgFormerAdjustStatus(OrgStatusDict.INACTIVE);
            hisMdPO.setOrgAdjustStatus(OrgStatusDict.ENABLED);
            hisMdPO.setOrgHisAdjustEnableDate(LocalDateTime.now());
            orgStructAdjustHisMdRepo.insert(hisMdPO);
        }
    }

    public void historyJob() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime historyDateTime = now.plusDays(-1);
        LocalDateTime endOfDay = historyDateTime.withHour(23).withMinute(59).withSecond(59);
        LocalDateTime startOfDay = historyDateTime.withHour(0).withMinute(0).withSecond(0);
        Long aLong = orgStructAdjustHisMdRepo.selectEditCount(startOfDay, endOfDay);
        if (aLong > 0) {
//            组织的状态不是草稿、待启用，才插入到这2个表中，即这2个表只记录已启用+已停用的组织
            List<OrgStructMdPO> list = orgStructMdRepo.selectSaveInfo();
            ArrayList<OrgStructEditionMdPO> insertHisList = new ArrayList<>();
            for (OrgStructMdPO po : list) {
                OrgStructEditionMdPO mdPO = new OrgStructEditionMdPO();
                mdPO.setOrgStructId(po.getId());
                mdPO.setOrgEditionParentId(po.getOrgParentId());
                mdPO.setOrgEditionStatus(po.getOrgStatus());
                mdPO.setOrgEditionDate(LocalDateTime.now().plusDays(-1));
                mdPO.setOrgDimensionId(po.getOrgDimensionId());
                mdPO.setOrgHisStructName(po.getOrgName());
                insertHisList.add(mdPO);
            }
            orgStructEditionMdRepo.insertBatch(insertHisList);
        }
    }

    public List<OrgStructMdHistoryTreeDTO> historyDetail(OrgStructMdHistoryQueryDTO request) {
        ArrayList<OrgStructMdHistoryTreeDTO> orgStructMdHistoryTreeDTOS = new ArrayList<>();
        if (!StringUtils.hasText(request.getOrgDimensionCode())) {
            throw new BusinessException(OrgMsg.ORG_DIMENSION_CF_CODE_IS_NULL);
        }
        OrgDimensionCfPO dimensionCfPO = orgDimensionCfRepo.findByCode(request.getOrgDimensionCode());
        if (dimensionCfPO == null) {
            throw new BusinessException(OrgMsg.ORG_DIMENSION_CF_IS_NULL);
        }
        request.setOrgEditionDate(request.getOrgEditionDate().plusDays(+1));
        OrgStructEditionMdPO po = orgStructEditionMdRepo.selectTime(request, dimensionCfPO.getId());
        if (po == null) {
            return orgStructMdHistoryTreeDTOS;
        }
        LocalDateTime time = po.getOrgEditionDate();
        LocalDateTime endOfDay = time.withHour(23).withMinute(59).withSecond(59);
        LocalDateTime startOfDay = time.withHour(00).withMinute(00).withSecond(00);
        List<OrgStructEditionMdPO> result = orgStructEditionMdRepo.selectHistory(startOfDay, endOfDay, request.getOrgParentId(), dimensionCfPO.getId());
        if (CollectionUtils.isEmpty(result)) {
            return orgStructMdHistoryTreeDTOS;
        }
        for (OrgStructEditionMdPO temp : result) {
            OrgStructMdHistoryTreeDTO dto = new OrgStructMdHistoryTreeDTO();
            OrgStructMdPO structMdPO = orgStructMdRepo.selectById(temp.getOrgStructId());
            dto.setOrgCode(structMdPO.getOrgCode());
            dto.setOrgName(structMdPO.getOrgName());
            dto.setLeaf(structMdPO.getLeaf());
            dto.setOrgStatus(temp.getOrgEditionStatus());
            dto.setOrgParentId(structMdPO.getOrgParentId());
            dto.setId(structMdPO.getId());
            dto.setOrgEditionDate(temp.getOrgEditionDate());
            dto.setHistoryId(temp.getId());
            orgStructMdHistoryTreeDTOS.add(dto);
        }
        return orgStructMdHistoryTreeDTOS;
    }

    public Paging<OrgStructMdDetailDTO> paging(OrgStructPageQueryDTO request) {
        if (StringUtils.hasText(request.getOrgDimensionCode())) {
            OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(request.getOrgDimensionCode());
            if (Objects.nonNull(orgDimensionCfPO)) {
                request.setOrgDimensionId(orgDimensionCfPO.getId());
            }
        }
        // 处理扩展属性查询的数据
        Map<String, Object> queryParams = queryExtQueryParams(request.getQueryParams());
        Paging<OrgStructMdPO> paging = orgStructMdRepo.paging(request, queryParams);
        if (CollectionUtils.isEmpty(paging.getData())) {
            return Paging.empty();
        }
        Paging<OrgStructMdDetailDTO> detailDTOPaging = new Paging<>();
        detailDTOPaging.setData(orgStructMdConverter.convertDetialDTOList(paging.getData()));
        detailDTOPaging.setTotal(paging.getTotal());
        return detailDTOPaging;
    }

    private Map<String, Object> queryExtQueryParams(Map<String, Object> queryParams) {
        if (CollectionUtils.isEmpty(queryParams)) {
            return Collections.emptyMap();
        }
        Set<String> set = queryParams.keySet();
        GenAttrCodesQueryDTO genAttrCodesQueryDTO = new GenAttrCodesQueryDTO();
        genAttrCodesQueryDTO.setCodes(new ArrayList<>(set));
        List<GenAttrCfDTO> attrByCodes = orgMdGateWay.findAttrByCodes(genAttrCodesQueryDTO);
        if (CollectionUtils.isEmpty(attrByCodes)) {
            return Collections.emptyMap();
        }
        Map<Object, GenAttrCfDTO> genAttrCfDTOMap = attrByCodes.stream().collect(Collectors.toMap(GenAttrCfDTO::getAttrCode, Function.identity()));
        Map<String, Object> result = new HashMap<>();
        for (String key : queryParams.keySet()) {
            Object value = queryParams.get(key);
            GenAttrCfDTO genAttrCfDTO = genAttrCfDTOMap.get(key);
            if (Objects.nonNull(genAttrCfDTO)) {
                result.put(genAttrCfDTO.getAttrField(), value);
            }
        }
        return result;
    }

    public Paging<Map<String, Object>> pagingMap(OrgStructPageQueryDTO request) {
        if (StringUtils.hasText(request.getOrgDimensionCode())) {
            OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(request.getOrgDimensionCode());
            if (Objects.nonNull(orgDimensionCfPO)) {
                request.setOrgDimensionId(orgDimensionCfPO.getId());
            }
        }
        Map<String, Object> queryParams = queryExtQueryParams(request.getQueryParams());
        Paging<OrgStructMdPO> paging = orgStructMdRepo.paging(request, queryParams);
        if (CollectionUtils.isEmpty(paging.getData())) {
            return Paging.empty();
        }
        Set<Long> orgBusinessIdSet = paging.getData().stream().map(OrgStructMdPO::getOrgBusinessTypeIds).flatMap(Collection::stream).collect(Collectors.toSet());
        List<OrgBusinessTypePO> orgBusinessTypePOS = orgBusinessTypeRepo.selectBatchIds(orgBusinessIdSet);
        Map<Long, OrgBusinessTypePO> orgBusinessTypePOMap = orgBusinessTypePOS.stream().collect(Collectors.toMap(OrgBusinessTypePO::getId, Function.identity()));
        Set<Long> attrGroupIdSet = orgBusinessTypePOS.stream().map(OrgBusinessTypePO::getAttrGroupId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, List<GenAttrDTO>> attrGroupAttrMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(attrGroupIdSet)) {
            for (Long attrGroupId : attrGroupIdSet) {
                List<GenAttrDTO> genAttrDTOList = orgMdGateWay.findAttrByAttrGroupId(new IdRequest(attrGroupId));
                if (!CollectionUtils.isEmpty(genAttrDTOList)) {
                    attrGroupAttrMap.put(attrGroupId, genAttrDTOList);
                }
            }
        }
        Paging<Map<String, Object>> detailDTOPaging = new Paging<>();
        List<Map<String, Object>> dataList = new ArrayList<>(paging.getData().size());
        for (OrgStructMdPO orgStructMdPO : paging.getData()) {
            List<OrgBusinessTypePO> orgBusinessTypePOList = orgStructMdPO.getOrgBusinessTypeIds().stream().map(t -> orgBusinessTypePOMap.get(t)).filter(Objects::nonNull).collect(Collectors.toList());
            Map<String, GenAttrDTO> genAttrDTOMap = Collections.emptyMap();
            if (!CollectionUtils.isEmpty(orgBusinessTypePOList)) {
                Set<GenAttrDTO> genAttrDTOList = new HashSet<>(orgBusinessTypePOList.size());
                for (OrgBusinessTypePO orgBusinessTypePO : orgBusinessTypePOList) {
                    if (Objects.nonNull(orgBusinessTypePO.getAttrGroupId())) {
                        List<GenAttrDTO> genAttrList = attrGroupAttrMap.get(orgBusinessTypePO.getAttrGroupId());
                        if (!CollectionUtils.isEmpty(genAttrList)) {
                            genAttrDTOList.addAll(genAttrList);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(genAttrDTOList)) {
                    genAttrDTOMap = genAttrDTOList.stream().collect(Collectors.toMap(GenAttrDTO::getAttrField, Function.identity()));
                }
            }
            Map<String, Object> orgStructMdMap = new HashMap<>();
            Field[] declaredFields = OrgFiledUtils.getAllFields(OrgStructMdPO.class);
            for (Field field : declaredFields) {
                try {
                    field.setAccessible(Boolean.TRUE);
                    if (CollectionUtils.isEmpty(genAttrDTOMap)) {
                        orgStructMdMap.put(field.getName(), field.get(orgStructMdPO));
                    } else {
                        GenAttrDTO genAttrDTO = genAttrDTOMap.get(field.getName());
                        if (Objects.isNull(genAttrDTO)) {
                            orgStructMdMap.put(field.getName(), field.get(orgStructMdPO));
                        } else {
                            Object fieldValue = field.get(orgStructMdPO);
                            if (fieldValue != null) {
                                AttrTypeTransStrategy attrTypeTransStrategy = StrategyLoader.load(AttrTypeTransStrategy.class, alg -> alg.match(genAttrDTO.getAttrDataType(), Objects.isNull(genAttrDTO.getAttrIsMulti()) ? Boolean.FALSE : genAttrDTO.getAttrIsMulti()));
                                orgStructMdMap.put(genAttrDTO.getAttrCode(), attrTypeTransStrategy.trans(String.valueOf(fieldValue)));
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("组织分页数据查询转换出现异常", e);
                }
            }
            dataList.add(orgStructMdMap);
        }
        detailDTOPaging.setData(dataList);
        detailDTOPaging.setTotal(paging.getTotal());
        return detailDTOPaging;
    }

    public OrgStructMdDetailDTO queryById(IdRequest request) {
        OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectById(request.getId());
        return orgStructMdConverter.convertDetailDTO(orgStructMdPO);
    }

    public Map<String, Object> queryDetailMapById(IdRequest request) {
        return orgStructCache.findById(request.getId());
    }

    public List<OrgStructMdDetailDTO> queryPath(IdRequest request) {
        OrgStructMdPO orgStructPO = orgStructMdRepo.selectById(request.getId());
        if (Objects.isNull(orgStructPO)) {
            return Collections.emptyList();
        }
        List<OrgStructMdPO> orgStructMdPOS = queryPath(orgStructPO);
        if (CollectionUtils.isEmpty(orgStructMdPOS)) {
            return Collections.emptyList();
        }
        return orgStructMdConverter.convertDetialDTOList(orgStructMdPOS);
    }

    public List<Map<String, Object>> queryDetailMapByIds(IdsRequest request) {
        return orgStructCache.getByIds(new ArrayList<>(request.getIds()));
    }

    public TreePathDTO findTreeById(IdRequest request) {
        TreePathDTO dto = new TreePathDTO();
        OrgStructMdPO mdPO = orgStructMdRepo.selectById(request.getId());
        if (mdPO == null) {
            return dto;
        }
        ArrayList<String> pathList = new ArrayList<>();
        pathList.add(mdPO.getOrgName());
        buildPath(mdPO.getOrgParentId(), pathList);
        Collections.reverse(pathList);

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < pathList.size(); i++) {
            sb.append(pathList.get(i));
            if (i < pathList.size() - 1) {
                sb.append("/");
            }
        }
        dto.setPath(sb.toString());
        return dto;
    }

    private void buildPath(Long id, ArrayList<String> pathList) {
        if (id == null) {
            return;
        }
        OrgStructMdPO parent = orgStructMdRepo.selectById(id);
        pathList.add(parent.getOrgName());
        buildPath(parent.getOrgParentId(), pathList);
    }

    public void findChildrenById(Long id, List<Long> ids) {

        List<OrgStructMdPO> children = orgStructMdRepo.selectChildren(id);
        if (children == null) {
            return;
        }
        for (OrgStructMdPO childrenId : children) {
            ids.add(childrenId.getId());
            findChildrenById(childrenId.getId(), ids);
        }
    }

    public List<OrgStructMdDetailDTO> queryUserOrgByUserId(IdRequest request) {
        EmployeePO employeePO = employeeRepo.queryByUserId(request.getId());
        if (Objects.isNull(employeePO)) {
            return Collections.emptyList();
        }
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeId(employeePO.getId());
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return Collections.emptyList();
        }
        Set<Long> orgStructIdSet = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toSet());
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.selectBatchIds(orgStructIdSet);
        return orgStructMdConverter.convertDetialDTOList(orgStructMdPOS);
    }

    public List<Long> queryUserChargeOrg() {
        Long currentUserId = TrantorContext.getCurrentUserId();
        if (Objects.isNull(currentUserId)) {
            return Collections.emptyList();
        }
        EmployeePO employeePO = employeeRepo.queryByUserId(currentUserId);
        if (Objects.isNull(employeePO)) {
            return Collections.emptyList();
        }
        Object orgIdList = orgRedisTemplate.opsForValue().get(OrgConstants.ORG_REC_USER_CHARGE_ORG_PREFIX + employeePO.getId());
        if (Objects.nonNull(orgIdList)) {
            return JSON.parseObject(orgIdList.toString(), new TypeReference<List<Long>>() {
            });
        }
        // 查询成员群组
        OrgIdentityPO orgIdentityPO = orgIdentityRepo.findByCode("RECADMIN");
        if (Objects.isNull(orgIdentityPO)) {
            return Collections.emptyList();
        }
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeIdAndRankId(employeePO.getId(), orgIdentityPO.getId());
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return Collections.emptyList();
        }
        List<OrgStructMdPO> orgStructMdPOList = new ArrayList<>();
        for (EmployeeOrgLinkPO employeeOrgLinkPO : employeeOrgLinkPOS) {
            getChildren(orgStructMdPOList, employeeOrgLinkPO.getOrgUnitId());
        }
        List<Long> orgStructIdSet = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toList());
        orgStructIdSet.addAll(orgStructMdPOList.stream().map(OrgStructMdPO::getId).collect(Collectors.toSet()));
        orgRedisTemplate.opsForValue().set(OrgConstants.ORG_REC_USER_CHARGE_ORG_PREFIX + employeePO.getId(), orgStructIdSet, 15, TimeUnit.DAYS);
        return orgStructIdSet;
    }

    public OrgStructMdDetailDTO buildOrgStructTree(OrgStructBuildTreeQueryDTO request) {
        if (StringUtils.hasText(request.getOrgDimensionCode())) {
            OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(request.getOrgDimensionCode());
            if (Objects.nonNull(orgDimensionCfPO)) {
                request.setOrgDimensionId(orgDimensionCfPO.getId());
            }
        }
        if (Objects.isNull(request.getOrgDimensionId())) {
            return null;
        }
        List<OrgStructMdPO> orgStructMdPOList = orgStructMdRepo.queryByPid(null, request.getOrgDimensionId(), request.getOrgStatusSet());
        if (CollectionUtils.isEmpty(orgStructMdPOList)) {
            return null;
        }
        OrgStructMdPO root = orgStructMdPOList.get(0);
        OrgStructMdDetailDTO orgStructMdDetailDTO = orgStructMdConverter.convertDetailDTO(root);
        getChildren(orgStructMdDetailDTO, request.getOrgStatusSet(), request.getOrgDimensionId());
        return orgStructMdDetailDTO;
    }

    private void getChildren(OrgStructMdDetailDTO orgStructMdDetailDTO, Set<String> status, Long orgDimensionId) {
        List<OrgStructMdPO> orgStructMdPOList = orgStructMdRepo.queryByPid(orgStructMdDetailDTO.getId(), orgDimensionId, status);
        if (CollectionUtils.isEmpty(orgStructMdPOList)) {
            return;
        }
        List<OrgStructMdDetailDTO> orgStructMdDetailDTOS = orgStructMdConverter.convertDetialDTOList(orgStructMdPOList);
        orgStructMdDetailDTO.setChild(orgStructMdDetailDTOS);
        for (OrgStructMdDetailDTO orgStructMdPO : orgStructMdDetailDTOS) {
            getChildren(orgStructMdPO, status, orgDimensionId);
        }
    }

    private void getChildren(List<OrgStructMdPO> orgStructMdPOList, Long parentId) {
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryByPid(parentId);
        if (!CollectionUtils.isEmpty(orgStructMdPOS)) {
            for (OrgStructMdPO orgStructMdPO : orgStructMdPOS) {
                orgStructMdPOList.add(orgStructMdPO);
                getChildren(orgStructMdPOList, orgStructMdPO.getId());
            }
        }
    }

    public List<TreePathDTO> queryPathByIds(IdsRequest request) {
        ArrayList<TreePathDTO> list = new ArrayList<>(request.getIds().size());
        List<OrgStructMdPO> mdPOs = orgStructMdRepo.selectBatchIds(request.getIds());
        if (CollectionUtils.isEmpty(mdPOs)) {
            return Collections.emptyList();
        }
        for (OrgStructMdPO mdPO : mdPOs) {
            TreePathDTO dto = new TreePathDTO();
            dto.setId(mdPO.getId());
            dto.setOrgName(mdPO.getOrgName());
            ArrayList<String> pathList = new ArrayList<>();
            pathList.add(mdPO.getOrgName());
            buildPath(mdPO.getOrgParentId(), pathList);
            Collections.reverse(pathList);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < pathList.size(); i++) {
                sb.append(pathList.get(i));
                if (i < pathList.size() - 1) {
                    sb.append("/");
                }
            }
            dto.setPath(sb.toString());
            list.add(dto);
        }
        return list;
    }

    public List<OrgStructMdDetailDTO> queryStructByCodes(OrgQueryCodesDTO request) {
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryStructByCodes(request);
        return orgStructMdConverter.convertDetialDTOList(orgStructMdPOS);
    }

    public List<OrgStructMdDetailDTO> queryStructByNames(OrgQueryNamesDTO request) {
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryStructByNames(request);
        return orgStructMdConverter.convertDetialDTOList(orgStructMdPOS);
    }

    public OrgStructMdDetailDTO queryParentOrgByType(OrgParentQueryDTO request) {
        if (Objects.isNull(request.getId()) || !StringUtils.hasText(request.getOrgBusinessTypeCode())) {
            return null;
        }
        OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectById(request.getId());
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.selectBatchIds(orgStructMdPO.getPath());
        if (CollectionUtils.isEmpty(orgStructMdPOS)) {
            return null;
        }
        Map<Long, OrgStructMdPO> orgStructMdPOMap = orgStructMdPOS.stream().collect(Collectors.toMap(OrgStructMdPO::getId, Function.identity()));
        List<OrgStructMdPO> orgStructMdPOList = new ArrayList<>(orgStructMdPOS.size());
        List<Long> path = orgStructMdPO.getPath();
        for (Long id : path) {
            OrgStructMdPO orgStructMdPO1 = orgStructMdPOMap.get(id);
            orgStructMdPOList.add(orgStructMdPO1);
        }
        Collections.reverse(orgStructMdPOList);
        for (OrgStructMdPO structMdPO : orgStructMdPOList) {
            if (structMdPO.getOrgBusinessTypeCodes().contains(request.getOrgBusinessTypeCode())) {
                return orgStructMdConverter.convertDetailDTO(structMdPO);
            }
        }
        return null;
    }

    public List<Map<String, Object>> queryDetailMapByCodes(OrgQueryCodesDTO request) {
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryStructByCodes(request);
        if (CollectionUtils.isEmpty(orgStructMdPOS)) {
            return Collections.emptyList();
        }
        Set<Long> orgBusinessIdSet = orgStructMdPOS.stream().map(OrgStructMdPO::getOrgBusinessTypeIds).flatMap(Collection::stream).collect(Collectors.toSet());
        List<OrgBusinessTypePO> orgBusinessTypePOS = orgBusinessTypeRepo.selectBatchIds(orgBusinessIdSet);
        Map<Long, OrgBusinessTypePO> orgBusinessTypePOMap = orgBusinessTypePOS.stream().collect(Collectors.toMap(OrgBusinessTypePO::getId, Function.identity()));
        Set<Long> attrGroupIdSet = orgBusinessTypePOS.stream().map(OrgBusinessTypePO::getAttrGroupId).collect(Collectors.toSet());
        Map<Long, List<GenAttrDTO>> attrGroupAttrMap = new HashMap<>();
        for (Long attrGroupId : attrGroupIdSet) {
            List<GenAttrDTO> genAttrDTOList = orgMdGateWay.findAttrByAttrGroupId(new IdRequest(attrGroupId));
            if (!CollectionUtils.isEmpty(genAttrDTOList)) {
                attrGroupAttrMap.put(attrGroupId, genAttrDTOList);
            }
        }
        List<Map<String, Object>> dataList = new ArrayList<>(orgStructMdPOS.size());
        for (OrgStructMdPO orgStructMdPO : orgStructMdPOS) {
            List<OrgBusinessTypePO> orgBusinessTypePOList = orgStructMdPO.getOrgBusinessTypeIds().stream().map(t -> orgBusinessTypePOMap.get(t)).collect(Collectors.toList());
            Field[] declaredFields = OrgFiledUtils.getAllFields(OrgStructMdPO.class);
            Map<String, Object> orgStructMdMap = new HashMap<>();
            List<GenAttrDTO> genAttrDTOList = new ArrayList<>(orgBusinessTypePOList.size());
            for (OrgBusinessTypePO orgBusinessTypePO : orgBusinessTypePOList) {
                List<GenAttrDTO> genAttrList = attrGroupAttrMap.get(orgBusinessTypePO.getAttrGroupId());
                if (!CollectionUtils.isEmpty(genAttrList)) {
                    genAttrList.addAll(genAttrList);
                }
            }
            if (!CollectionUtils.isEmpty(genAttrDTOList)) {
                Map<String, GenAttrDTO> genAttrDTOMap = genAttrDTOList.stream().collect(Collectors.toMap(GenAttrDTO::getAttrField, Function.identity()));
                for (Field field : declaredFields) {
                    try {
                        field.setAccessible(Boolean.TRUE);
                        if (CollectionUtils.isEmpty(genAttrDTOMap)) {
                            orgStructMdMap.put(field.getName(), field.get(orgStructMdPO));
                        } else {
                            GenAttrDTO genAttrDTO = genAttrDTOMap.get(field.getName());
                            if (Objects.isNull(genAttrDTO)) {
                                orgStructMdMap.put(field.getName(), field.get(orgStructMdPO));
                            } else {
                                Object fieldValue = field.get(orgStructMdPO);
                                if (fieldValue != null) {
                                    AttrTypeTransStrategy attrTypeTransStrategy = StrategyLoader.load(AttrTypeTransStrategy.class, alg -> alg.match(genAttrDTO.getAttrDataType(), Objects.isNull(genAttrDTO.getAttrIsMulti()) ? Boolean.FALSE : genAttrDTO.getAttrIsMulti()));
                                    orgStructMdMap.put(genAttrDTO.getAttrCode(), attrTypeTransStrategy.trans(String.valueOf(fieldValue)));
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("组织分页数据查询转换出现异常", e);
                    }
                    dataList.add(orgStructMdMap);
                }
            } else {
                try {
                    for (Field field : declaredFields) {
                        field.setAccessible(Boolean.TRUE);
                        orgStructMdMap.put(field.getName(), field.get(orgStructMdPO));
                    }
                } catch (Exception e) {
                    log.error("组织分页数据查询转换出现异常", e);
                }
                dataList.add(orgStructMdMap);
            }
        }
        return dataList;
    }

    public OrgStructMdDetailDTO queryCurrentOrgComOrg(IdRequest request) {
        OrgStructMdPO orgStructMdPO = orgStructMdRepo.selectById(request.getId());
        if (Objects.isNull(orgStructMdPO)) {
            return null;
        }
        if (orgStructMdPO.getOrgBusinessTypeCodes().contains("COM_ORG")) {
            return orgStructMdConverter.convertDetailDTO(orgStructMdPO);
        }
        List<Long> path = orgStructMdPO.getPath();
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.selectBatchIds(path);
        if (CollectionUtils.isEmpty(orgStructMdPOS)) {
            return null;
        }
        orgStructMdPOS.sort(Comparator.comparingInt(o -> path.indexOf(o.getId())));
        orgStructMdPOS.remove(orgStructMdPOS.size() - 1);
        Collections.reverse(orgStructMdPOS);
        for (OrgStructMdPO structMdPO : orgStructMdPOS) {
            if (structMdPO.getOrgBusinessTypeCodes().contains("COM_ORG")) {
                return orgStructMdConverter.convertDetailDTO(structMdPO);
            }
        }
        return null;
    }

    public OrgStructMdDetailDTO queryCurrentUserOrgByType(OrgParentQueryDTO request) {
        if (!StringUtils.hasText(request.getOrgBusinessTypeCode())) {
            return null;
        }
        EmployeePO employeePO = employeeRepo.queryByUserId(TrantorContext.getCurrentUserId());
        if (Objects.isNull(employeePO)) {
            return null;
        }
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeId(employeePO.getId());
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return null;
        }
        Set<Long> orgStructIdSet = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toSet());
        OrgStructPageQueryDTO orgStructPageQueryDTO = new OrgStructPageQueryDTO();
        orgStructPageQueryDTO.setIds(orgStructIdSet);
        orgStructPageQueryDTO.setOrgBusinessTypeCode(request.getOrgBusinessTypeCode());
        orgStructPageQueryDTO.setOrgStatusSet(Sets.newHashSet(OrgStatusDict.ENABLED));
        Paging<OrgStructMdPO> paging = orgStructMdRepo.paging(orgStructPageQueryDTO, null);
        if (CollectionUtils.isEmpty(paging.getData())) {
            return null;
        }
        List<OrgStructMdPO> data = paging.getData();
        return orgStructMdConverter.convertDetailDTO(data.get(0));
    }

    public List<OrgStructMdDetailDTO> queryAllChildById(IdRequest request) {
        List<OrgStructMdPO> orgStructMdPOList = orgStructMdRepo.queryAllChild(request.getId());
        return orgStructMdConverter.convertDetialDTOList(orgStructMdPOList);
    }

    public List<OrgStructMdDetailDTO> queryInvLocByWhNumIds(IdsRequest request) {
        return orgStructMdConverter.convertDetialDTOList(orgStructMdRepo.queryInvLocByWhNumIds(new HashSet<>(request.getIds())));
    }

    public List<OrgStructMdDetailDTO> batchQueryOrgAllChild(IdsRequest request) {
        List<OrgStructMdPO> orgStructMdPOList = new ArrayList<>();
        for (Long id : request.getIds()) {
            List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryAllChild(id);
            if (CollectionUtils.isEmpty(orgStructMdPOS)) {
                continue;
            }
            orgStructMdPOList.addAll(orgStructMdPOS);
        }
        if (CollectionUtils.isEmpty(orgStructMdPOList)) {
            return Collections.emptyList();
        }
        Iterator<OrgStructMdPO> iterator = orgStructMdPOList.iterator();
        while (iterator.hasNext()) {
            OrgStructMdPO orgStructMdPO = iterator.next();
            if (request.getIds().contains(orgStructMdPO.getId())) {
                iterator.remove();
            }
        }
        return orgStructMdConverter.convertDetialDTOList(orgStructMdPOList);
    }
}

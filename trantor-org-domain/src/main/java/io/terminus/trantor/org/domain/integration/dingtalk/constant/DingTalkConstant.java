package io.terminus.trantor.org.domain.integration.dingtalk.constant;

/**
 * @author: 张博
 * @date: 2024-09-02 11:00
 */
public interface DingTalkConstant {

    String DING_TALK_CHANNEL_CODE = "DING_TALK";

    // accessToken缓存key
    String ACCESS_TOKEN_CACHE_KEY = "dingtalk:accessToken:cache";

    // accessToken缓存时间
    Integer ACCESS_TOKEN_EXPIRE_TIME = 7200;

    // 获取accessToken地址
    String ACCESS_TOKEN_URL = "https://oapi.dingtalk.com/gettoken";

    // 查询部门列表地址
    String QUERY_DEPARTMENT_LIST_URL = "https://oapi.dingtalk.com/topapi/v2/department/listsub";

    // 查询部门想去地址
    String QUERY_DEPARTMENT_DETAIL_URL = "https://oapi.dingtalk.com/topapi/v2/department/get";

    // 查询员工userid列表地址
    String QUERY_EMPLOYEE_USER_ID_LIST_URL = "https://oapi.dingtalk.com/topapi/smartwork/hrm/employee/queryonjob";

    // 查询员工详情地址
    String QUERY_EMPLOYEE_DETAIL_URL = "https://oapi.dingtalk.com/topapi/smartwork/hrm/employee/v2/list";
}

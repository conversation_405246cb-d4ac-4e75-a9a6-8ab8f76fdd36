package io.terminus.trantor.org.domain.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.event.producer.EventProducer;
import io.terminus.common.rocketmq.common.TerminusMessage;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.iam.api.enums.role.RoleRelationBizType;
import io.terminus.iam.api.request.role.RoleByIdsFindParams;
import io.terminus.iam.api.request.role.RoleRelationCreateParams;
import io.terminus.iam.api.request.user.UserGroupParams;
import io.terminus.iam.api.response.role.Role;
import io.terminus.iam.api.response.role.RoleRelation;
import io.terminus.iam.api.response.user.UserGroup;
import io.terminus.iam.sdk.client.IAMClient;
import io.terminus.trantor.org.domain.constant.OrgConstants;
import io.terminus.trantor.org.domain.converter.OrgUnitConverter;
import io.terminus.trantor.org.infrastructure.repo.*;
import io.terminus.trantor.org.spi.dict.OrgStatusDict;
import io.terminus.trantor.org.spi.model.OrgQueryDTO;
import io.terminus.trantor.org.spi.model.dto.*;
import io.terminus.trantor.org.spi.model.dto.event.OrgUnitEnableEventDTO;
import io.terminus.trantor.org.spi.model.po.*;
import io.terminus.trantor.org.spi.msg.OrgMsg;
import io.terminus.trantor2.common.iam.IamClientFactory;
import io.terminus.trantor2.permission.cache.PermissionCacheCleanser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: 张博
 * @date: 2023-02-28 16:36
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrgUnitService {

    private final IamClientFactory iamClientFactory;
    private final OrgUnitRepo orgUnitRepo;
    private final BizTypeRepo bizTypeRepo;
    private final IdGenerator idGenerator;
    private final EmployeeRepo employeeRepo;
    private final EventProducer eventProducer;
    private final OrgUnitConverter orgUnitConverter;
    private final OrgBizTypeLinkRepo orgBizTypeLinkRepo;
    private final EmployeeOrgLinkRepo employeeOrgLinkRepo;
    private final BizTypeExtraService bizTypeExtraService;
    private final OrgBizTypeLimitLinkRepo orgBizTypeLimitLinkRepo;
    private final PermissionCacheCleanser permissionCacheCleanser;
    private final OrgStructMdRepo orgStructMdRepo;
    private final OrgDimensionCfRepo orgDimensionCfRepo;

    @Value("${trantor.org.version:v1}")
    private String version;

    /**
     * 创建组织单元
     */
    @Transactional
    public OrgUnitDTO create(OrgUnitSaveDTO request) {
        // 校验名称是否重复
        OrgUnitPO orgUnit = orgUnitRepo.checkName(request.getName(), request.getPid());
        if (Objects.nonNull(orgUnit)) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_NAME_IS_REPEAT);
        }
        OrgUnitPO orgUnitPO1 = orgUnitRepo.queryByCode(request.getCode());
        if (Objects.nonNull(orgUnitPO1)) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_CODE_IS_REPEAT);
        }
        // 校验父类允许创建的类型
        if (Objects.nonNull(request.getPid())) {
            OrgUnitPO parent = orgUnitRepo.selectById(request.getPid());
            if (Objects.isNull(parent)) {
                throw new BusinessException(OrgMsg.ORG_ORG_UNIT_PARENT_IS_NOT_EXIST);
            }
            // 判断父类状态
            if (!parent.getStatus().equals(OrgStatusDict.ENABLED)) {
                throw new BusinessException(OrgMsg.ORG_ORG_UNIT_PARENT_STATUS_IS_NOT_ENABLE);
            }
            parent.setHasChild(Boolean.TRUE);
            orgUnitRepo.updateById(parent);
        }

        OrgUnitPO orgUnitPO = orgUnitConverter.convert(request);
        orgUnitPO.setId(idGenerator.nextId(OrgUnitPO.class));
        orgUnitPO.setStatus(OrgStatusDict.DRAFT);
        orgUnitPO.setHasChild(Boolean.FALSE);
        orgUnitPO.setPath(JSON.toJSONString(queryPath(orgUnitPO)));
        // 创建用户组
        UserGroup userGroup = createUserGroup(orgUnitPO);
        orgUnitPO.setUserGroup(userGroup.getId());
        orgUnitRepo.insert(orgUnitPO);
        // 处理角色
        createUserGroupRole(request, Boolean.TRUE, orgUnitPO);
        // 处理业务组织关系
        if (!CollectionUtils.isEmpty(request.getParams())) {
            createOrgBizLink(orgUnitPO, request.getParams());
        }
        return orgUnitConverter.convert(orgUnitPO);
    }

    /**
     * 更新组织单元数据
     */
    @Transactional
    public OrgUnitDTO update(OrgUnitSaveDTO request) {
        OrgUnitPO origin = orgUnitRepo.selectById(request.getId());
        if (Objects.isNull(origin)) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_IS_NOT_EXIST);
        }
        // 校验名称是否重复
        OrgUnitPO orgUnit = orgUnitRepo.checkName(request.getName(), request.getPid());
        if (Objects.nonNull(orgUnit) && !orgUnit.getId().equals(request.getId())) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_NAME_IS_REPEAT);
        }
        OrgUnitPO orgUnitPO1 = orgUnitRepo.queryByCode(request.getCode());
        if (Objects.nonNull(orgUnitPO1) && !orgUnitPO1.getId().equals(request.getId())) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_CODE_IS_REPEAT);
        }
        // 校验父类允许创建的类型
        if (Objects.nonNull(request.getPid())) {
            if (request.getId().equals(request.getPid())) {
                throw new BusinessException(OrgMsg.ORG_ORG_PARENT_ID_IS_ILLEGAL);
            }
            this.parentCheck(request.getPid());
            OrgUnitPO parent = orgUnitRepo.selectById(request.getPid());
            if (Objects.nonNull(parent)) {
                parent.setHasChild(Boolean.TRUE);
                orgUnitRepo.updateById(parent);
            }
        } else {
            if (Objects.nonNull(origin.getPid())) {
                List<OrgUnitPO> orgUnitPOS = orgUnitRepo.queryByPid(origin.getPid());
                if (CollectionUtils.isEmpty(orgUnitPOS)) {
                    OrgUnitPO parent = orgUnitRepo.selectById(origin.getPid());
                    if (Objects.nonNull(parent)) {
                        parent.setHasChild(Boolean.FALSE);
                        orgUnitRepo.updateById(parent);
                    }
                }
            }
        }
        OrgUnitPO orgUnitPO = orgUnitConverter.convert(request);
        orgUnitPO.setPath(JSON.toJSONString(queryPath(orgUnitPO)));
        orgUnitPO.setStatus(origin.getStatus());
        // 没有用户组创建用户组
        if (Objects.isNull(origin.getUserGroup())) {
            UserGroup userGroup = createUserGroup(origin);
            orgUnitPO.setUserGroup(userGroup.getId());
            origin.setUserGroup(userGroup.getId());
        }
        orgUnitRepo.updateById(orgUnitPO);
        // 处理业务组织关系
        updateOrgBizTypeLink(orgUnitPO, request);
        // 用户组绑定角色
        createUserGroupRole(request, Boolean.FALSE, origin);
        return orgUnitConverter.convert(orgUnitPO);
    }

    private List<Long> queryPath(OrgUnitPO orgUnitPO) {
        List<Long> path = new ArrayList<>();
        if (Objects.isNull(orgUnitPO.getPid())) {
            path.add(orgUnitPO.getId());
        } else {
            recursion(path, orgUnitPO);
        }
        Collections.reverse(path);
        return path;
    }

    private void recursion(List<Long> list, OrgUnitPO orgUnitPO) {
        list.add(orgUnitPO.getId());
        OrgUnitPO parent = orgUnitRepo.selectById(orgUnitPO.getPid());
        if (Objects.nonNull(parent)) {
            recursion(list, parent);
        }
    }

    public void createOrgBizLink(OrgUnitPO orgUnitPO, List<BizTypeParamDTO> params) {
        List<OrgBizTypeLinkPO> orgBizTypeLinkPOList = new ArrayList<>(params.size());
        for (BizTypeParamDTO bizTypeParam : params) {
            BizTypePO bizTypePO = bizTypeRepo.selectById(bizTypeParam.getBizTypeId());
            OrgBizTypeLinkPO orgBizTypeLinkPO = new OrgBizTypeLinkPO();
            orgBizTypeLinkPO.setId(idGenerator.nextId(OrgBizTypeLinkPO.class));
            orgBizTypeLinkPO.setOrgUnitId(orgUnitPO.getId());
            orgBizTypeLinkPO.setBizTypeId(bizTypeParam.getBizTypeId());
            orgBizTypeLinkPOList.add(orgBizTypeLinkPO);
            bizTypeParam.getParam().put("id", orgUnitPO.getId());
            bizTypeParam.getParam().put("code", orgUnitPO.getCode());
            bizTypeParam.getParam().put("name", orgUnitPO.getName());
            if (bizTypeParam.getParam().containsKey("status")) {
                bizTypeParam.getParam().put("status", orgUnitPO.getStatus());
            }
            if (bizTypeParam.getParam().containsKey("org_id")) {
                bizTypeParam.getParam().put("org_id", orgUnitPO.getPid());
            }
            bizTypeExtraService.create(bizTypePO.getModelKey(), bizTypeParam.getParam());
        }
        orgBizTypeLinkRepo.insertBatch(orgBizTypeLinkPOList);
    }

    public Paging<OrgUnitDTO> paging(OrgUnitPageDTO request) {
        Set<Long> ids = null;
        if (Objects.nonNull(request.getBizTypeId())) {
            ids = orgBizTypeLinkRepo.queryOrgUnitIdsByBizTypeId(request.getBizTypeId());
        }
        Paging<OrgUnitPO> paging = orgUnitRepo.paging(request, ids);
        if (CollectionUtils.isEmpty(paging.getData())) {
            return Paging.empty();
        }
        List<OrgUnitDTO> orgUnitDTOS = new ArrayList<>(paging.getData().size());
        for (OrgUnitPO orgUnitPO : paging.getData()) {
            OrgUnitDTO orgUnitDTO = orgUnitConverter.convert(orgUnitPO);
            // 查询关联关系
            List<OrgBizTypeLinkPO> orgBizTypeLinkPOS = orgBizTypeLinkRepo.queryByOrgUnitId(orgUnitDTO.getId());
            orgUnitDTO.setBizTypes(orgUnitConverter.convertLink(orgBizTypeLinkPOS));
            orgUnitDTOS.add(orgUnitDTO);
        }
        Paging<OrgUnitDTO> orgUnitDTOPaging = new Paging<>();
        orgUnitDTOPaging.setData(orgUnitDTOS);
        orgUnitDTOPaging.setTotal(paging.getTotal());
        return orgUnitDTOPaging;
    }

    public Paging<OrgUnitDTO> parentPaging(OrgUnitPageDTO request) {
        if (CollectionUtils.isEmpty(request.getBizTypeCodes())) {
            Paging<OrgUnitPO> paging = orgUnitRepo.paging(request, Collections.emptySet());
            Paging<OrgUnitDTO> orgUnitDTOPaging = new Paging<>();
            orgUnitDTOPaging.setData(orgUnitConverter.convert(paging.getData()));
            orgUnitDTOPaging.setTotal(paging.getTotal());
            return orgUnitDTOPaging;
        }
        List<BizTypePO> bizTypePOList = bizTypeRepo.findByCodes(request.getBizTypeCodes());
        if (CollectionUtils.isEmpty(bizTypePOList)) {
            return Paging.empty();
        }
        Set<Long> bizTypeIdSet = bizTypePOList.stream().map(BizTypePO::getId).collect(Collectors.toSet());
        // 查询组织单元关联表
        List<OrgBizTypeLinkPO> orgBizTypeLinkPOS = orgBizTypeLinkRepo.queryByBizTypeIds(bizTypeIdSet);
        if (CollectionUtils.isEmpty(orgBizTypeLinkPOS)) {
            return Paging.empty();
        }
        Set<Long> orgUnitIdSet = orgBizTypeLinkPOS.stream().map(OrgBizTypeLinkPO::getOrgUnitId).collect(Collectors.toSet());
        Paging<OrgUnitPO> paging = orgUnitRepo.paging(request, orgUnitIdSet);
        if (CollectionUtils.isEmpty(paging.getData())) {
            return Paging.empty();
        }
        Paging<OrgUnitDTO> orgUnitDTOPaging = new Paging<>();
        orgUnitDTOPaging.setData(orgUnitConverter.convert(paging.getData()));
        orgUnitDTOPaging.setTotal(paging.getTotal());
        return orgUnitDTOPaging;
    }

    public List<OrgBizTypeCountDTO> queryListAndCount() {
        List<BizTypePO> bizTypePOS = bizTypeRepo.findOrderedList();
        if (CollectionUtils.isEmpty(bizTypePOS)) {
            return Collections.emptyList();
        }
        Set<Long> ids = bizTypePOS.stream().map(BizTypePO::getId).collect(Collectors.toSet());
        List<OrgBizTypeLinkPO> orgBizTypeLinkPOS = orgBizTypeLinkRepo.queryCountByBizTypeIds(ids);
        List<OrgBizTypeCountDTO> bizTypeVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orgBizTypeLinkPOS)) {
            for (BizTypePO bizTypePO : bizTypePOS) {
                OrgBizTypeCountDTO bizTypeVO = new OrgBizTypeCountDTO();
                bizTypeVO.setId(bizTypePO.getId());
                bizTypeVO.setCount(0);
                bizTypeVO.setName(bizTypePO.getName());
                bizTypeVOList.add(bizTypeVO);
            }
        } else {
            Map<Long, OrgBizTypeLinkPO> orgBizTypeLinkPOMap = orgBizTypeLinkPOS.stream().collect(Collectors.toMap(OrgBizTypeLinkPO::getBizTypeId, Function.identity()));
            for (BizTypePO bizTypePO : bizTypePOS) {
                OrgBizTypeCountDTO bizTypeVO = new OrgBizTypeCountDTO();
                bizTypeVO.setName(bizTypePO.getName());
                bizTypeVO.setId(bizTypePO.getId());
                OrgBizTypeLinkPO orgBizTypeLinkPO = orgBizTypeLinkPOMap.get(bizTypePO.getId());
                if (Objects.nonNull(orgBizTypeLinkPO)) {
                    bizTypeVO.setCount(orgBizTypeLinkPO.getTotal());
                } else {
                    bizTypeVO.setCount(0);
                }
                bizTypeVOList.add(bizTypeVO);
            }
        }
        return bizTypeVOList;
    }

    private void parentCheck(Long pid) {
        OrgUnitPO parent = orgUnitRepo.selectById(pid);
        if (Objects.isNull(parent)) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_PARENT_IS_NOT_EXIST);
        }
        // 判断父类状态
        if (!parent.getStatus().equals(OrgStatusDict.ENABLED)) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_PARENT_STATUS_IS_NOT_ENABLE);
        }
    }

    public List<OrgUnitDTO> queryOrgUnitByEmployeeCode(EmployeeQueryDTO request) {
        // 先查询员工
        EmployeePO employeePO = employeeRepo.queryByCode(request.getCode());
        if (Objects.isNull(employeePO)) {
            throw new BusinessException(OrgMsg.ORG_EMPLOYEE_IS_NOT_EXIST);
        }
        // 查询员工组织关联表
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByEmployeeId(employeePO.getId());
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return Collections.emptyList();
        }
        Set<Long> orgUnitIdSet = employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getOrgUnitId).collect(Collectors.toSet());
        List<OrgUnitPO> orgUnitPOS = orgUnitRepo.selectBatchIds(orgUnitIdSet);
        List<OrgUnitDTO> orgUnitDTOList = orgUnitConverter.convert(orgUnitPOS);
        if (!CollectionUtils.isEmpty(orgUnitDTOList)) {
            Map<Long, EmployeeOrgLinkPO> employeeOrgLinkPOMap = employeeOrgLinkPOS.stream().collect(Collectors.toMap(EmployeeOrgLinkPO::getOrgUnitId, Function.identity()));
            for (OrgUnitDTO orgUnitDTO : orgUnitDTOList) {
                EmployeeOrgLinkPO employeeOrgLinkPO = employeeOrgLinkPOMap.get(orgUnitDTO.getId());
                orgUnitDTO.setIsMainOrg(employeeOrgLinkPO.getIsMainOrg());
                orgUnitDTO.setRankId(employeeOrgLinkPO.getIdentityId());
            }
        }
        return orgUnitDTOList;
    }

    public OrgUnitDTO queryDetail(Long id) {
        OrgUnitPO origin = orgUnitRepo.selectById(id);
        if (Objects.isNull(origin)) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_IS_NOT_EXIST);
        }
        OrgUnitDTO orgUnitDTO = orgUnitConverter.convert(origin);
        List<OrgBizTypeLinkPO> orgBizTypeLinkPOS = orgBizTypeLinkRepo.queryByOrgUnitId(id);
        if (!CollectionUtils.isEmpty(orgBizTypeLinkPOS)) {
            Set<Long> bizTypeIdSet = orgBizTypeLinkPOS.stream().map(OrgBizTypeLinkPO::getBizTypeId).collect(Collectors.toSet());
            List<BizTypePO> bizTypePOS = bizTypeRepo.selectBatchIds(bizTypeIdSet);
            if (!CollectionUtils.isEmpty(bizTypePOS)) {
                List<OrgBizTypeLinkDTO> orgBizTypeLinkVOList = new ArrayList<>(bizTypePOS.size());
                for (BizTypePO bizTypePO : bizTypePOS) {
                    OrgBizTypeLinkDTO orgBizTypeLinkVO = new OrgBizTypeLinkDTO();
                    orgBizTypeLinkVO.setBizTypeId(bizTypePO.getId());
                    orgBizTypeLinkVO.setBizTypeName(bizTypePO.getName());
                    orgBizTypeLinkVO.setModelKey(bizTypePO.getModelKey());
                    orgBizTypeLinkVO.setExtra(bizTypeExtraService.queryByOrgUnitId(bizTypePO.getModelKey(), orgUnitDTO.getId()));
                    orgBizTypeLinkVOList.add(orgBizTypeLinkVO);
                }
                orgUnitDTO.setBizTypes(orgBizTypeLinkVOList);
            }
        }
        if (Objects.nonNull(orgUnitDTO.getPid())) {
            OrgUnitPO parent = orgUnitRepo.selectById(orgUnitDTO.getPid());
            orgUnitDTO.setParent(orgUnitConverter.convert(parent));
        }
        // 处理用户组角色
        if (Objects.nonNull(origin.getUserGroup())) {
            try {
                IAMClient iamClient = iamClientFactory.getIamClient();
                List<RoleRelation> relationList = iamClient.rolerelationClient().findRoleRelationByBizObject(RoleRelationBizType.USER_GROUP_ROLE, String.valueOf(origin.getUserGroup())).execute();
                if (!CollectionUtils.isEmpty(relationList)) {
                    List<Long> roleIdList = relationList.stream().map(RoleRelation::getRoleId).collect(Collectors.toList());
                    RoleByIdsFindParams roleByIdsFindParams = new RoleByIdsFindParams();
                    roleByIdsFindParams.setIds(roleIdList);
                    List<Role> roleList = iamClient.roleClient().findRoleByIds(roleByIdsFindParams).execute();
                    if (!CollectionUtils.isEmpty(roleList)) {
                        List<OrgRoleLinkDTO> orgRoleLinkDTOList = roleList.stream().map(t -> {
                            OrgRoleLinkDTO orgRoleLinkDTO = new OrgRoleLinkDTO();
                            orgRoleLinkDTO.setRoleId(t.getId());
                            orgRoleLinkDTO.setRoleName(t.getName());
                            return orgRoleLinkDTO;
                        }).collect(Collectors.toList());
                        orgUnitDTO.setOrgRoleLinkList(orgRoleLinkDTOList);
                    }
                }
            } catch (Exception e) {
                throw new BusinessException(e.getMessage());
            }
        }
        return orgUnitDTO;
    }

    @Transactional
    public Boolean delete(Long id) {
        // 查询组织单元是否存在
        OrgUnitPO origin = orgUnitRepo.selectById(id);
        if (Objects.isNull(origin)) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_IS_NOT_EXIST);
        }
        if (!origin.getStatus().equals(OrgStatusDict.DRAFT)) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_STATUS_IS_NOT_DRAFT_CAN_NOT_DELETE);
        }
        orgUnitRepo.deleteById(origin.getId());
        // 查询组织业务关联表
        List<OrgBizTypeLinkPO> orgBizTypeLinkPOS = orgBizTypeLinkRepo.queryByOrgUnitId(origin.getId());
        if (!CollectionUtils.isEmpty(orgBizTypeLinkPOS)) {
            Set<Long> ids = orgBizTypeLinkPOS.stream().map(OrgBizTypeLinkPO::getId).collect(Collectors.toSet());
            orgBizTypeLinkRepo.deleteBatchIds(ids);
            // 删除对应的扩展数据
            for (OrgBizTypeLinkPO orgBizTypeLinkPO : orgBizTypeLinkPOS) {
                BizTypePO bizTypePO = bizTypeRepo.selectById(orgBizTypeLinkPO.getBizTypeId());
                bizTypeExtraService.deleteByOrgUnitId(bizTypePO.getModelKey(), id);
            }
        }
        // 判断是否有上级
        if (Objects.nonNull(origin.getPid())) {
            OrgUnitPO parent = orgUnitRepo.selectById(origin.getPid());
            // 查询是否还有其他的下级
            Long count = orgUnitRepo.queryCountByPid(parent.getId(), null);
            if (count == 0) {
                parent.setHasChild(Boolean.FALSE);
                orgUnitRepo.updateById(parent);
            }
        }
        // 删除用户组
        if (Objects.nonNull(origin.getUserGroup())) {
            UserGroupParams userGroupParams = new UserGroupParams();
            userGroupParams.setId(origin.getUserGroup());
            try {
                IAMClient iamClient = iamClientFactory.getIamClient();
                iamClient.userGroups().delete(userGroupParams).execute();
            } catch (Exception e) {
                throw new BusinessException(e.getMessage());
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 启用组织
     */
    public Boolean enable(Long id) {
        // 查询组织单元是否存在
        OrgUnitPO origin = orgUnitRepo.selectById(id);
        if (Objects.isNull(origin)) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_IS_NOT_EXIST);
        }
        if (!origin.getStatus().equals(OrgStatusDict.DRAFT) && !origin.getStatus().equals(OrgStatusDict.DISABLED)) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_STATUS_IS_NOT_DRAFT_OR_DISABLE);
        }
        if (Objects.nonNull(origin.getPid())) {
            OrgUnitPO parent = orgUnitRepo.selectById(origin.getPid());
            if (!OrgStatusDict.ENABLED.equals(parent.getStatus())) {
                throw new BusinessException(OrgMsg.ORG_ORG_UNIT_PARENT_STATUS_IS_NOT_ENABLE);
            }
        }
        origin.setStatus(OrgStatusDict.ENABLED);
        orgUnitRepo.updateById(origin);
        // 查询组织单元关联关系
        handleBizTypeExtraStatus(origin.getId(), OrgStatusDict.ENABLED);
        return Boolean.TRUE;
    }

    public Boolean disable(Long id) {
        // 查询组织单元是否存在
        OrgUnitPO origin = orgUnitRepo.selectById(id);
        if (Objects.isNull(origin)) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_IS_NOT_EXIST);
        }
        if (!origin.getStatus().equals(OrgStatusDict.ENABLED)) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_STATUS_IS_NOT_ENABLE);
        }
        // 查询是否有下级节点
        Long count = orgUnitRepo.queryCountByPid(origin.getId(), OrgStatusDict.ENABLED);
        if (count > 0) {
            throw new BusinessException(OrgMsg.ORG_ORG_UNIT_HAS_ENABLE_CHILDREN_CAN_NOT_DISABLE);
        }
        origin.setStatus(OrgStatusDict.DISABLED);
        orgUnitRepo.updateById(origin);
        // 查询组织单元关联关系
        handleBizTypeExtraStatus(origin.getId(), OrgStatusDict.DISABLED);
        return Boolean.TRUE;
    }

    public List<OrgUnitDTO> queryByPid(OrgUnitQueryDTO request) {
        if (OrgConstants.ORG_VERSION_V1.equals(version)) {
            if (CollectionUtils.isEmpty(request.getBizTypeCodes())) {
                List<OrgUnitPO> orgUnitPOS = orgUnitRepo.queryByPid(request.getId());
                return orgUnitConverter.convert(orgUnitPOS);
            } else {
                List<BizTypePO> bizTypePOList = bizTypeRepo.findByCodes(request.getBizTypeCodes());
                if (CollectionUtils.isEmpty(bizTypePOList)) {
                    return Collections.emptyList();
                }
                Set<Long> bizTypeIdSet = bizTypePOList.stream().map(BizTypePO::getId).collect(Collectors.toSet());
                // 查询组织单元关联表
                List<OrgBizTypeLinkPO> orgBizTypeLinkPOS = orgBizTypeLinkRepo.queryByBizTypeIds(bizTypeIdSet);
                if (CollectionUtils.isEmpty(orgBizTypeLinkPOS)) {
                    return Collections.emptyList();
                }
                Set<Long> orgUnitIdSet = orgBizTypeLinkPOS.stream().map(OrgBizTypeLinkPO::getOrgUnitId).collect(Collectors.toSet());
                List<OrgUnitPO> orgUnitPOS = orgUnitRepo.queryByPid(request.getId(), orgUnitIdSet);
                return orgUnitConverter.convert(orgUnitPOS);
            }
        }
        if (CollectionUtils.isEmpty(request.getBizTypeCodes())) {
            return Collections.emptyList();
        }
        OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(new ArrayList<>(request.getBizTypeCodes()).get(0));
        if (Objects.isNull(orgDimensionCfPO)) {
            return Collections.emptyList();
        }
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryByPid(request.getId(), orgDimensionCfPO.getId());
        if (CollectionUtils.isEmpty(orgStructMdPOS)) {
            return Collections.emptyList();
        }
        return orgStructMdPOS.stream().map(t -> {
            OrgUnitDTO orgUnitDTO = new OrgUnitDTO();
            orgUnitDTO.setName(t.getOrgName());
            orgUnitDTO.setId(t.getId());
            orgUnitDTO.setCode(t.getOrgCode());
            orgUnitDTO.setPid(t.getOrgParentId());
            orgUnitDTO.setHasChild(!t.getLeaf());
            orgUnitDTO.setStatus(t.getOrgStatus());
            return orgUnitDTO;
        }).collect(Collectors.toList());
    }

    public void handleBizTypeExtraStatus(Long orgUnitId, String status) {
        // 查询组织业务关联表
        List<OrgBizTypeLinkPO> orgBizTypeLinkPOS = orgBizTypeLinkRepo.queryByOrgUnitId(orgUnitId);
        if (!CollectionUtils.isEmpty(orgBizTypeLinkPOS)) {
            for (OrgBizTypeLinkPO orgBizTypeLinkPO : orgBizTypeLinkPOS) {
                BizTypePO bizTypePO = bizTypeRepo.selectById(orgBizTypeLinkPO.getBizTypeId());
                Map<String, Object> bizTypeExtraParams = bizTypeExtraService.queryByOrgUnitId(bizTypePO.getModelKey(), orgUnitId);
                if (!CollectionUtils.isEmpty(bizTypeExtraParams)) {
                    if (bizTypeExtraParams.containsKey("status")) {
                        bizTypeExtraParams.put("status", status);
                        bizTypeExtraService.update(bizTypePO.getModelKey(), bizTypeExtraParams);
                    }
                }
                // 如果创建的是公司组织，则发送消息
                if (OrgStatusDict.ENABLED.equals(status)) {
                    OrgUnitEnableEventDTO orgUnitEnableEventDTO = new OrgUnitEnableEventDTO();
                    orgUnitEnableEventDTO.setId(orgUnitId);
                    orgUnitEnableEventDTO.setModelKey(bizTypePO.getModelKey());
                    TerminusMessage terminusMessage = new TerminusMessage();
                    terminusMessage.setTags(OrgUnitEnableEventDTO.TAG);
                    terminusMessage.setBody(orgUnitEnableEventDTO);
                    eventProducer.send(terminusMessage);
                }
            }
        }
    }

    private UserGroup createUserGroup(OrgUnitPO orgUnitPO) {
        // 创建用户组
        UserGroupParams userGroupParams = new UserGroupParams();
        userGroupParams.setName(orgUnitPO.getName());
        userGroupParams.setKey(orgUnitPO.getCode());
        userGroupParams.setDesc(orgUnitPO.getName());
        try {
            IAMClient iamClient = iamClientFactory.getIamClient();
            return iamClient.userGroups().create(userGroupParams).execute();
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    private void updateOrgBizTypeLink(OrgUnitPO orgUnitPO, OrgUnitSaveDTO request) {
        List<OrgBizTypeLinkPO> orgBizTypeLinkPOS = orgBizTypeLinkRepo.queryByOrgUnitId(orgUnitPO.getId());
        if (CollectionUtils.isEmpty(request.getParams())) {
            // 如果没有传，但是数据库有，则需要删除关联关系
            if (!CollectionUtils.isEmpty(orgBizTypeLinkPOS)) {
                Set<Long> idSet = orgBizTypeLinkPOS.stream().map(OrgBizTypeLinkPO::getId).collect(Collectors.toSet());
                orgBizTypeLinkRepo.deleteBatchIds(idSet);
                for (OrgBizTypeLinkPO orgBizTypeLinkPO : orgBizTypeLinkPOS) {
                    BizTypePO bizTypePO = bizTypeRepo.selectById(orgBizTypeLinkPO.getBizTypeId());
                    bizTypeExtraService.deleteByOrgUnitId(bizTypePO.getModelKey(), orgBizTypeLinkPO.getOrgUnitId());
                }
            }
        } else {
            if (CollectionUtils.isEmpty(orgBizTypeLinkPOS)) {
                createOrgBizLink(orgUnitPO, request.getParams());
            } else {
                Set<Long> originBizTypeIdSet = orgBizTypeLinkPOS.stream().map(OrgBizTypeLinkPO::getBizTypeId).collect(Collectors.toSet());
                Set<Long> newBizTypeIdSet = request.getParams().stream().map(BizTypeParamDTO::getBizTypeId).collect(Collectors.toSet());
                List<BizTypeParamDTO> createList = request.getParams().stream().filter(t -> !originBizTypeIdSet.contains(t.getBizTypeId())).collect(Collectors.toList());
                List<BizTypeParamDTO> updateList = request.getParams().stream().filter(t -> originBizTypeIdSet.contains(t.getBizTypeId())).collect(Collectors.toList());
                List<Long> deleteBizTypeIdList = originBizTypeIdSet.stream().filter(t -> !newBizTypeIdSet.contains(t)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(createList)) {
                    createOrgBizLink(orgUnitPO, createList);
                }
                if (!CollectionUtils.isEmpty(updateList)) {
                    for (BizTypeParamDTO bizTypeParam : updateList) {
                        BizTypePO bizTypePO = bizTypeRepo.selectById(bizTypeParam.getBizTypeId());
                        bizTypeParam.getParam().put("name", orgUnitPO.getName());
                        bizTypeParam.getParam().put("code", orgUnitPO.getCode());
                        if (bizTypeParam.getParam().containsKey("status")) {
                            bizTypeParam.getParam().put("status", orgUnitPO.getStatus());
                        }
                        if (bizTypeParam.getParam().containsKey("org_id")) {
                            bizTypeParam.getParam().put("org_id", orgUnitPO.getPid());
                        }
                        bizTypeParam.getParam().put("id", orgUnitPO.getId());
                        bizTypeExtraService.update(bizTypePO.getModelKey(), bizTypeParam.getParam());
                    }
                }
                if (!CollectionUtils.isEmpty(deleteBizTypeIdList)) {
                    for (Long id : deleteBizTypeIdList) {
                        BizTypePO bizTypePO = bizTypeRepo.selectById(id);
                        bizTypeExtraService.deleteByOrgUnitId(bizTypePO.getModelKey(), null);
                    }
                    List<Long> deleteIds = orgBizTypeLinkPOS.stream().filter(t -> deleteBizTypeIdList.contains(t.getBizTypeId())).map(OrgBizTypeLinkPO::getId).collect(Collectors.toList());
                    orgBizTypeLinkRepo.deleteBatchIds(deleteIds);
                }
            }
        }
    }

    private void createUserGroupRole(OrgUnitSaveDTO request, Boolean isCreate, OrgUnitPO orgUnitPO) {
        try {
            // 查询组织下的用户
            Set<Long> employeeIdSet = queryEmployeeByOrgUnit(orgUnitPO.getId());
            // 用户组绑定角色
            RoleRelationCreateParams roleRelationCreateParams = new RoleRelationCreateParams();
            roleRelationCreateParams.setBizType(RoleRelationBizType.USER_GROUP_ROLE);
            roleRelationCreateParams.setBizId(String.valueOf(orgUnitPO.getUserGroup()));
            IAMClient iamClient = iamClientFactory.getIamClient();
            if (!CollectionUtils.isEmpty(request.getOrgRoleLinkList())) {
                Set<Long> roleIdSet = request.getOrgRoleLinkList().stream().map(OrgRoleLinkDTO::getRoleId).collect(Collectors.toSet());
                roleRelationCreateParams.setRoleIds(Lists.newArrayList(roleIdSet));
                iamClient.rolerelationClient().flushToNewRoleRelation(roleRelationCreateParams).execute();
            } else {
                if (!isCreate) {
                    roleRelationCreateParams.setRoleIds(Lists.newArrayList());
                    iamClient.rolerelationClient().flushToNewRoleRelation(roleRelationCreateParams).execute();
                }
            }
            if (!CollectionUtils.isEmpty(employeeIdSet)) {
                List<EmployeePO> employeePOS = employeeRepo.selectBatchIds(employeeIdSet);
                for (EmployeePO employeePO : employeePOS) {
                    permissionCacheCleanser.invalidateUserCache(employeePO.getUserId());
                }
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 查询当前组织单元下级所有单元的用户信息
     */
    private Set<Long> queryEmployeeByOrgUnit(Long orgUnitId) {
        List<OrgUnitPO> orgUnitPOS = orgUnitRepo.queryContainOrgUnitByOrgUnitId(String.valueOf(orgUnitId));
        if (CollectionUtils.isEmpty(orgUnitPOS)) {
            return Collections.emptySet();
        }
        Set<Long> orgUnitIdSet = orgUnitPOS.stream().map(OrgUnitPO::getId).collect(Collectors.toSet());
        List<EmployeeOrgLinkPO> employeeOrgLinkPOS = employeeOrgLinkRepo.queryByOrgUnitIds(orgUnitIdSet);
        if (CollectionUtils.isEmpty(employeeOrgLinkPOS)) {
            return Collections.emptySet();
        }
        return employeeOrgLinkPOS.stream().map(EmployeeOrgLinkPO::getEmployeeId).collect(Collectors.toSet());
    }


    public List<OrgSearchListDTO> search(OrgSearchDTO request) {
        if (CollectionUtils.isEmpty(request.getBizTypeCodes())) {
            List<OrgUnitPO> orgUnitPOList = orgUnitRepo.search(request, Collections.emptySet());
            return pathToList(orgUnitPOList);
        }
        List<BizTypePO> bizTypePOList = bizTypeRepo.findByCodes(request.getBizTypeCodes());
        if (CollectionUtils.isEmpty(bizTypePOList)) {
            return null;
        }
        Set<Long> bizTypeIdSet = bizTypePOList.stream().map(BizTypePO::getId).collect(Collectors.toSet());
        // 查询组织单元关联表
        List<OrgBizTypeLinkPO> orgBizTypeLinkPOS = orgBizTypeLinkRepo.queryByBizTypeIds(bizTypeIdSet);
        if (CollectionUtils.isEmpty(orgBizTypeLinkPOS)) {
            return null;
        }
        Set<Long> orgUnitIdSet = orgBizTypeLinkPOS.stream().map(OrgBizTypeLinkPO::getOrgUnitId).collect(Collectors.toSet());
        List<OrgUnitPO> orgUnitPOList = orgUnitRepo.search(request, orgUnitIdSet);
        return pathToList(orgUnitPOList);
    }

    public List<OrgUnitDTO> findAll(OrgQueryDTO request) {
        if (OrgConstants.ORG_VERSION_V1.equals(version)) {
            if (CollectionUtils.isEmpty(request.getBizTypeCodes())) {
                List<OrgUnitPO> orgUnitPOList = orgUnitRepo.findAll(request.getStatus(), Collections.emptySet());
                if (CollectionUtils.isEmpty(orgUnitPOList)) {
                    return Collections.emptyList();
                }
                return orgUnitConverter.convert(orgUnitPOList);
            }
            List<BizTypePO> bizTypePOList = bizTypeRepo.findByCodes(request.getBizTypeCodes());
            if (CollectionUtils.isEmpty(bizTypePOList)) {
                return Collections.emptyList();
            }
            Set<Long> bizTypeIdSet = bizTypePOList.stream().map(BizTypePO::getId).collect(Collectors.toSet());
            // 查询组织单元关联表
            List<OrgBizTypeLinkPO> orgBizTypeLinkPOS = orgBizTypeLinkRepo.queryByBizTypeIds(bizTypeIdSet);
            if (CollectionUtils.isEmpty(orgBizTypeLinkPOS)) {
                return Collections.emptyList();
            }
            Set<Long> orgUnitIdSet = orgBizTypeLinkPOS.stream().map(OrgBizTypeLinkPO::getOrgUnitId).collect(Collectors.toSet());
            List<OrgUnitPO> orgUnitPOList = orgUnitRepo.findAll(request.getStatus(), orgUnitIdSet);
            if (CollectionUtils.isEmpty(orgUnitPOList)) {
                return Collections.emptyList();
            }
            return orgUnitConverter.convert(orgUnitPOList);
        }
        if (CollectionUtils.isEmpty(request.getBizTypeCodes())) {
            return Collections.emptyList();
        }
        OrgDimensionCfPO orgDimensionCfPO = orgDimensionCfRepo.findByCode(new ArrayList<>(request.getBizTypeCodes()).get(0));
        if (Objects.isNull(orgDimensionCfPO)) {
            return Collections.emptyList();
        }
        List<OrgStructMdPO> orgStructMdPOS = orgStructMdRepo.queryByDimensionId(request.getId(), request.getStatus());
        if (CollectionUtils.isEmpty(orgStructMdPOS)) {
            return Collections.emptyList();
        }
        return orgStructMdPOS.stream().map(t -> {
            OrgUnitDTO orgUnitDTO = new OrgUnitDTO();
            orgUnitDTO.setName(t.getOrgName());
            orgUnitDTO.setId(t.getId());
            orgUnitDTO.setCode(t.getOrgCode());
            orgUnitDTO.setPid(t.getOrgParentId());
            orgUnitDTO.setHasChild(!t.getLeaf());
            orgUnitDTO.setStatus(t.getOrgStatus());
            return orgUnitDTO;
        }).collect(Collectors.toList());
    }

    private List<OrgSearchListDTO> pathToList(List<OrgUnitPO> orgUnitPOList) {
        if (CollectionUtils.isEmpty(orgUnitPOList)) {
            return null;
        }
        List<OrgSearchListDTO> resultList = new ArrayList<>();

        //获取path里对应id
        Set<Long> orgUnitSet = new HashSet<>();
        for (OrgUnitPO orgUnitPO : orgUnitPOList) {
            if (StringUtils.hasText(orgUnitPO.getPath())) {
                orgUnitSet.addAll(Objects.requireNonNull(JSON.parseArray(orgUnitPO.getPath(), Long.class)));
            }
        }
        //查出所有id对应数据 在转为map
        List<OrgUnitPO> orgUnitPOS = orgUnitRepo.selectBatchIds(orgUnitSet);
        Map<Long, String> pathMap = orgUnitPOS.stream().collect(Collectors.toMap(OrgUnitPO::getId, OrgUnitPO::getName, (value1, value2) -> value1));

        for (OrgUnitPO orgUnitPO : orgUnitPOList) {
            OrgSearchListDTO dto = new OrgSearchListDTO();

            if (StringUtils.hasText(orgUnitPO.getPath())) {
                List<Long> pathsList = Objects.requireNonNull(JSON.parseArray(orgUnitPO.getPath(), Long.class));
                ArrayList<Long> idList = new ArrayList<>();
                ArrayList<String> nameList = new ArrayList<>();
                for (Long path : pathsList) {
                    idList.add(path);
                    nameList.add(pathMap.get(path));
                }
                dto.setName(orgUnitPO.getName());
                dto.setPath(orgUnitPO.getPath());
                dto.setPathIdList(idList);
                dto.setPathNameList(nameList);
                resultList.add(dto);
            }
        }
        return resultList;
    }

    public OrgUnitInfoDTO queryOrgUnitById(IdRequest request) {
        if (Objects.isNull(request.getId())) {
            return null;
        }
        return orgUnitConverter.convertInfo(orgUnitRepo.selectById(request.getId()));
    }
}

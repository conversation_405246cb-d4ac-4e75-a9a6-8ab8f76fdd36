<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>trantor-org</artifactId>
        <groupId>io.terminus.trantor</groupId>
        <version>1.0.1.JASOLAR.DEV-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>trantor-org-infrastructure</artifactId>

    <dependencies>
        <dependency>
            <groupId>io.terminus.trantor</groupId>
            <artifactId>trantor-org-spi</artifactId>
        </dependency>
        <!-- 以下组件按需选用 -->
        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-spring-boot-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.erp</groupId>
            <artifactId>erp-framework-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.iam</groupId>
            <artifactId>iam-sdk</artifactId>
        </dependency>
    </dependencies>
</project>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.terminus.trantor.org.infrastructure.repo.OrgUnitRepo">
    <select id="parentPaging" resultType="io.terminus.trantor.org.spi.model.po.OrgUnitPO">
        select ou.* from org_org_unit_cf ou left join org_org_biz_type_link_cf btl on ou.id = btl.org_unit_id
        <where>
            btl.biz_type_id in
            <foreach collection="bizTypes" index="item" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="dto.name != null">
                and ou.name like CONCAT('%',#{dto.name},'%')
            </if>
            <if test="dto.status != null">
                and ou.status = #{dto.status}
            </if>
        </where>
    </select>
</mapper>
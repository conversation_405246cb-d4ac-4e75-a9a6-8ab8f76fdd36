<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.terminus.trantor.org.infrastructure.repo.EmployeeRepo">
    <select id="pageByOrg" resultType="io.terminus.trantor.org.spi.model.po.EmployeePO">
        select * from org_employee_md em left join org_employee_org_link_cf eol on em.id = eol.employee_id
        <where>
            eol.org_unit_id = #{dto.orgUnitId}
            <if test="dto.name != null">
                and dto.name like CONCAT('%',#{dto.name},'%')
            </if>
            <if test="dto.status != null">
                and dto.status = #{dto.status}
            </if>
            <if test="dto.mobile != null">
                and dto.mobile = #{dto.mobile}
            </if>
            <if test="dto.type != null">
                and dto.type = #{dto.type}
            </if>
            <if test="dto.userId != null">
                and dto.user_id = #{dto.userId}
            </if>
            <if test="dto.email != null">
                and dto.email = #{dto.email}
            </if>
            and em.deleted = 0
        </where>
    </select>

    <select id="pageByUserOrgEmployee" resultType="io.terminus.trantor.org.spi.model.po.EmployeePO">
        select DISTINCT em.* from org_employee_md em left join org_employee_org_link_cf eol on em.id = eol.employee_id
        <where>
            eol.org_unit_id in
            <foreach item="item" collection="orgUnitIds" open="(" separator="," close=")" index="index">
                #{item}
            </foreach>
            <if test="dto.name != null">
                and em.name like CONCAT('%',#{dto.name},'%')
            </if>
            <if test="dto.code != null">
                and em.code like CONCAT('%',#{dto.code},'%')
            </if>
            <if test="dto.status != null">
                and em.status = #{dto.status}
            </if>
            <if test="dto.mobile != null">
                and em.mobile = #{dto.mobile}
            </if>
            <if test="dto.type != null">
                and em.type = #{dto.type}
            </if>
            <if test="dto.email != null">
                and em.email = #{dto.email}
            </if>
            and em.deleted = 0 and eol.deleted = 0
        </where>
    </select>
</mapper>
package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.OrgDimensionBusinessLinkPO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-14 14:48:14
 */
@Repository
public interface OrgDimensionBusinessLinkRepo extends BaseRepository<OrgDimensionBusinessLinkPO> {

    default List<OrgDimensionBusinessLinkPO> queryGroupIdLinkByDimensionId(Long dimensionId) {
        LambdaQueryWrapper<OrgDimensionBusinessLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgDimensionBusinessLinkPO::getOrgDimensionId, dimensionId);
        return this.selectList(queryWrapper);
    }

    default OrgDimensionBusinessLinkPO selectByTypeId(Long orgBusinessTypeId, Long orgDimensionId){
        LambdaQueryWrapper<OrgDimensionBusinessLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgDimensionBusinessLinkPO::getOrgDimensionId, orgDimensionId);
        queryWrapper.eq(OrgDimensionBusinessLinkPO::getOrgBusinessTypeId, orgBusinessTypeId);
        return this.selectOne(queryWrapper);
    }
}

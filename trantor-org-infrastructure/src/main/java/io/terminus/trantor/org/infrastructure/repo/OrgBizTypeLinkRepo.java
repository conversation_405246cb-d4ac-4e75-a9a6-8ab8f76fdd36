package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.OrgBizTypeLinkPO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 组织业务类型关联表(BizTypeLink)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-27 20:42:05
 */
@Repository
public interface OrgBizTypeLinkRepo extends BaseRepository<OrgBizTypeLinkPO> {

    default List<OrgBizTypeLinkPO> queryCountByBizTypeIds(Set<Long> bizTypeIdSet) {
        QueryWrapper<OrgBizTypeLinkPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("biz_type_id", bizTypeIdSet);
        queryWrapper.groupBy("biz_type_id");
        queryWrapper.select("biz_type_id as bizTypeId, count(*) as total");
        return this.selectList(queryWrapper);
    }

    default List<OrgBizTypeLinkPO> queryByOrgUnitId(Long orgUnitId) {
        LambdaQueryWrapper<OrgBizTypeLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgBizTypeLinkPO::getOrgUnitId, orgUnitId);
        return this.selectList(queryWrapper);
    }

    default OrgBizTypeLinkPO queryByBizTypeId(Long bizTypeId) {
        LambdaQueryWrapper<OrgBizTypeLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgBizTypeLinkPO::getBizTypeId, bizTypeId);
        queryWrapper.last("limit 1");
        return this.selectOne(queryWrapper);
    }

    default List<OrgBizTypeLinkPO> queryByBizTypeIds(Set<Long> bizTypeIds) {
        LambdaQueryWrapper<OrgBizTypeLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrgBizTypeLinkPO::getBizTypeId, bizTypeIds);
        return this.selectList(queryWrapper);
    }

    default Set<Long> queryOrgUnitIdsByBizTypeId(Long bizTypeId) {
        LambdaQueryWrapper<OrgBizTypeLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgBizTypeLinkPO::getBizTypeId, bizTypeId);
        List<OrgBizTypeLinkPO> orgBizTypeLinkPOS = this.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(orgBizTypeLinkPOS)) {
            return Collections.emptySet();
        }
        return orgBizTypeLinkPOS.stream().map(OrgBizTypeLinkPO::getOrgUnitId).collect(Collectors.toSet());
    }
}


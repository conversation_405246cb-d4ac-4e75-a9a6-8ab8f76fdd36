package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.OrgBizTypeLimitLinkPO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 业务类型上级单元类型限制关联表(OrgBizTypeLimitLinkPO)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-27 20:42:05
 */
@Repository
public interface OrgBizTypeLimitLinkRepo extends BaseRepository<OrgBizTypeLimitLinkPO> {

    default List<OrgBizTypeLimitLinkPO> queryByBizTypeId(Long bizTypeId) {
        LambdaQueryWrapper<OrgBizTypeLimitLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgBizTypeLimitLinkPO::getBizTypeId, bizTypeId);
        return this.selectList(queryWrapper);
    }

    default void deleteByBizTypeId(Long bizTypeId) {
        LambdaQueryWrapper<OrgBizTypeLimitLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgBizTypeLimitLinkPO::getBizTypeId, bizTypeId);
        this.delete(queryWrapper);
    }
}


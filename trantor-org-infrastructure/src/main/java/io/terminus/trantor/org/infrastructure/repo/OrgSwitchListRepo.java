package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.dict.OrgSwitchListStatusDict;
import io.terminus.trantor.org.spi.model.po.OrgSwitchListCfPO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface OrgSwitchListRepo extends BaseRepository<OrgSwitchListCfPO> {

    default List<OrgSwitchListCfPO> queryByOrgIds(Set<Long> orgIds) {
        LambdaQueryWrapper<OrgSwitchListCfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrgSwitchListCfPO::getSwitchOrgId, orgIds);
        queryWrapper.eq(OrgSwitchListCfPO::getSwitchStatus, OrgSwitchListStatusDict.ENABLED);
        return this.selectList(queryWrapper);
    }
}

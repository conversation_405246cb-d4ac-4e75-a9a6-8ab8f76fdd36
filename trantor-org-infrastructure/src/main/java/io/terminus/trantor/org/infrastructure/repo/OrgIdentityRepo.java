package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.OrgIdentityPO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023-11-14 14:48:14
 */
@Repository
public interface OrgIdentityRepo extends BaseRepository<OrgIdentityPO> {

    default OrgIdentityPO findByCode(String code) {
        LambdaQueryWrapper<OrgIdentityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgIdentityPO::getCode, code);
        return selectOne(queryWrapper);
    }

    default List<OrgIdentityPO> queryByNames(Set<String> names) {
        LambdaQueryWrapper<OrgIdentityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrgIdentityPO::getName, names);
        return selectList(queryWrapper);
    }

    default OrgIdentityPO queryCharge() {
        LambdaQueryWrapper<OrgIdentityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgIdentityPO::getName, "主管");
        return selectOne(queryWrapper);
    }

    default OrgIdentityPO queryNormal() {
        LambdaQueryWrapper<OrgIdentityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgIdentityPO::getName, "普通员工");
        return selectOne(queryWrapper);
    }
}

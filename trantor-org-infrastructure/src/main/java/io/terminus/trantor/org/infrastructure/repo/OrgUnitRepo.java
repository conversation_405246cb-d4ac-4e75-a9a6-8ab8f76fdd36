package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.terminus.common.api.model.Paging;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.dto.OrgSearchDTO;
import io.terminus.trantor.org.spi.model.dto.OrgUnitPageDTO;
import io.terminus.trantor.org.spi.model.po.OrgUnitPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 组织单元表(Unit)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-27 20:42:06
 */
@Repository
public interface OrgUnitRepo extends BaseRepository<OrgUnitPO> {

    default OrgUnitPO checkName(String name, Long pid) {
        LambdaQueryWrapper<OrgUnitPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgUnitPO::getName, name);
        if (Objects.isNull(pid)) {
            queryWrapper.isNull(OrgUnitPO::getPid);
        } else {
            queryWrapper.eq(OrgUnitPO::getPid, pid);
        }
        return this.selectOne(queryWrapper);
    }

    default OrgUnitPO queryByCode(String code) {
        LambdaQueryWrapper<OrgUnitPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgUnitPO::getCode, code);
        return this.selectOne(queryWrapper);
    }

    default Paging<OrgUnitPO> paging(OrgUnitPageDTO request, Set<Long> ids) {
        LambdaQueryWrapper<OrgUnitPO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(request.getCode())) {
            queryWrapper.eq(OrgUnitPO::getCode, request.getCode());
        }
        if (StringUtils.hasText(request.getName())) {
            queryWrapper.like(OrgUnitPO::getName, request.getName());
        }
        if (Objects.nonNull(request.getStatus())) {
            queryWrapper.eq(OrgUnitPO::getStatus, request.getStatus());
        }
        if (Objects.nonNull(request.getPid())) {
            queryWrapper.eq(OrgUnitPO::getPid, request.getPid());
        }
        if (!CollectionUtils.isEmpty(ids)) {
            queryWrapper.in(OrgUnitPO::getId, ids);
        }
        return this.selectPage(request, queryWrapper);
    }

    default Long queryCountByPid(Long pid, String status) {
        LambdaQueryWrapper<OrgUnitPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgUnitPO::getPid, pid);
        if (StringUtils.hasText(status)) {
            queryWrapper.eq(OrgUnitPO::getStatus, status);
        }
        return this.selectCount(queryWrapper);
    }

    default List<OrgUnitPO> queryByPid(Long pid) {
        LambdaQueryWrapper<OrgUnitPO> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.isNull(pid)) {
            queryWrapper.isNull(OrgUnitPO::getPid);
        } else {
            queryWrapper.eq(OrgUnitPO::getPid, pid);
        }
        queryWrapper.orderByDesc(OrgUnitPO::getUpdatedAt);
        return this.selectList(queryWrapper);
    }

    default List<OrgUnitPO> queryByPid(Long pid, Set<Long> orgUnitIds) {
        LambdaQueryWrapper<OrgUnitPO> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.isNull(pid)) {
            queryWrapper.isNull(OrgUnitPO::getPid);
        } else {
            queryWrapper.eq(OrgUnitPO::getPid, pid);
        }
        if (!CollectionUtils.isEmpty(orgUnitIds)) {
            queryWrapper.in(OrgUnitPO::getId, orgUnitIds);
        }
        queryWrapper.orderByDesc(OrgUnitPO::getUpdatedAt);
        return this.selectList(queryWrapper);
    }

    /**
     * 查询path包含指定组织单元ID的组织单元
     */
    @Select("SELECT * FROM org_org_unit_cf WHERE deleted = 0 AND `path` LIKE CONCAT('%',#{orgUnitId},'%')")
    List<OrgUnitPO> queryContainOrgUnitByOrgUnitId(@Param("orgUnitId") String orgUnitId);

    IPage<OrgUnitPO> parentPaging(Page<OrgUnitPO> page, @Param("dto") OrgUnitPageDTO dto, @Param("bizTypes") Set<Long> bizTypes);

    default List<OrgUnitPO> search(OrgSearchDTO request, Set<Long> ids) {
        LambdaQueryWrapper<OrgUnitPO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(request.getCode())) {
            queryWrapper.eq(OrgUnitPO::getCode, request.getCode());
        }
        if (StringUtils.hasText(request.getName())) {
            queryWrapper.like(OrgUnitPO::getName, request.getName());
        }
        if (!CollectionUtils.isEmpty(ids)) {
            queryWrapper.in(OrgUnitPO::getId, ids);
        }
        return this.selectList(queryWrapper);
    }

    default List<OrgUnitPO> findAll(Set<String> status, Set<Long> orgUnitIds) {
        LambdaQueryWrapper<OrgUnitPO> queryWrapper = new LambdaQueryWrapper<>();
        if (!CollectionUtils.isEmpty(orgUnitIds)) {
            queryWrapper.in(OrgUnitPO::getId, orgUnitIds);
        }
        if (!CollectionUtils.isEmpty(status)) {
            queryWrapper.in(OrgUnitPO::getStatus, status);
        }
        queryWrapper.orderByDesc(OrgUnitPO::getUpdatedAt);
        return this.selectList(queryWrapper);
    }
}


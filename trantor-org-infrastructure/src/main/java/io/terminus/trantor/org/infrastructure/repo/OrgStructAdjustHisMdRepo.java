package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.OrgStructAdjustHisMdPO;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * 组织架构变更记录表(OrgStructAdjustHisMd)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-02 14:31:11
 */
@Repository
public interface OrgStructAdjustHisMdRepo extends BaseRepository<OrgStructAdjustHisMdPO> {

    default Long selectEditCount(LocalDateTime startOfDay, LocalDateTime endOfDay) {
        LambdaQueryWrapper<OrgStructAdjustHisMdPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(i -> i.le(OrgStructAdjustHisMdPO::getCreatedAt, endOfDay).gt(OrgStructAdjustHisMdPO::getCreatedAt, startOfDay));
        queryWrapper.or().and(t -> t.le(OrgStructAdjustHisMdPO::getUpdatedAt, endOfDay).gt(OrgStructAdjustHisMdPO::getUpdatedAt, startOfDay));
        return this.selectCount(queryWrapper);
    }
}

package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.OrgChannelPO;
import org.springframework.stereotype.Repository;

/**
 * @author: 张博
 * @date: 2024-09-02 15:02
 */
@Repository
public interface OrgChannelRepo extends BaseRepository<OrgChannelPO> {

    default OrgChannelPO findChannelByCode(String channelCode) {
        return selectOne(new LambdaQueryWrapper<OrgChannelPO>().eq(OrgChannelPO::getChannelCode, channelCode));
    }
}

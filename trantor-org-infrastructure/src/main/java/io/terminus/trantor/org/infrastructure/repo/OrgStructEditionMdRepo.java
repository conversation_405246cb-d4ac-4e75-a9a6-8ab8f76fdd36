package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.dto.OrgStructMdHistoryQueryDTO;
import io.terminus.trantor.org.spi.model.dto.OrgStructMdQueryTreeDTO;
import io.terminus.trantor.org.spi.model.po.OrgStructEditionMdPO;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 历史组织版本表(OrgStructEditionMd)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-02 14:31:52
 */
@Repository
public interface OrgStructEditionMdRepo extends BaseRepository<OrgStructEditionMdPO> {


    default OrgStructEditionMdPO selectTime(OrgStructMdHistoryQueryDTO request, Long org_dimension_id) {
        LambdaQueryWrapper<OrgStructEditionMdPO> wrapper = new LambdaQueryWrapper<>();
        if (request.getOrgParentId() != null) {
            wrapper.eq(OrgStructEditionMdPO::getOrgEditionParentId, request.getOrgParentId());
        } else {
            wrapper.isNull(OrgStructEditionMdPO::getOrgEditionParentId);
        }
        wrapper.eq(OrgStructEditionMdPO::getOrgDimensionId, org_dimension_id);
        wrapper.lt(OrgStructEditionMdPO::getOrgEditionDate, request.getOrgEditionDate());
        wrapper.orderByDesc(OrgStructEditionMdPO::getOrgEditionDate).last("limit 1");
        return this.selectOne(wrapper);
    }

    default List<OrgStructEditionMdPO> selectHistory(LocalDateTime startOfDay, LocalDateTime endOfDay, Long orgParentId, Long dimensionCfId) {
        LambdaQueryWrapper<OrgStructEditionMdPO> wrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(orgParentId)) {
            wrapper.eq(OrgStructEditionMdPO::getOrgEditionParentId, orgParentId);
        } else {
            wrapper.isNull(OrgStructEditionMdPO::getOrgEditionParentId);
        }
        wrapper.ge(OrgStructEditionMdPO::getOrgEditionDate, startOfDay);
        wrapper.le(OrgStructEditionMdPO::getOrgEditionDate, endOfDay);
        wrapper.eq(OrgStructEditionMdPO::getOrgDimensionId, dimensionCfId);
        return this.selectList(wrapper);
    }

    default List<OrgStructEditionMdPO> search(OrgStructMdQueryTreeDTO request,LocalDateTime startOfDay, LocalDateTime endOfDay){
        LambdaQueryWrapper<OrgStructEditionMdPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(OrgStructEditionMdPO::getOrgHisStructName, request.getOrgName());
        queryWrapper.ge(OrgStructEditionMdPO::getOrgEditionDate, startOfDay);
        queryWrapper.le(OrgStructEditionMdPO::getOrgEditionDate, endOfDay);
        if (Objects.nonNull(request.getOrgDimensionId())) {
            queryWrapper.eq(OrgStructEditionMdPO::getOrgDimensionId, request.getOrgDimensionId());
        }
        return this.selectList(queryWrapper);
    }

    default OrgStructEditionMdPO selectByStructId(Long orgEditionParentId,LocalDateTime start,LocalDateTime end){
        LambdaQueryWrapper<OrgStructEditionMdPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgStructEditionMdPO::getOrgStructId, orgEditionParentId);
        queryWrapper.ge(OrgStructEditionMdPO::getOrgEditionDate, start);
        queryWrapper.le(OrgStructEditionMdPO::getOrgEditionDate, end);
        return this.selectOne(queryWrapper);
    }
}

package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.EmployeeOrgLinkPO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 组织员工关联表(EmployeeLink)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-27 20:42:05
 */
@Repository
@InterceptorIgnore(tenantLine = "true")
public interface EmployeeOrgLinkOrgSwitchRepo extends BaseRepository<EmployeeOrgLinkPO> {

    default List<EmployeeOrgLinkPO> queryByEmployeeIds(Set<Long> employeeIds) {
        LambdaQueryWrapper<EmployeeOrgLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EmployeeOrgLinkPO::getEmployeeId, employeeIds);
        return this.selectList(queryWrapper);
    }
}


package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.BizTypePO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface BizTypeRepo extends BaseRepository<BizTypePO> {

    default List<BizTypePO> findOrderedList() {
        LambdaQueryWrapper<BizTypePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(BizTypePO::getUpdatedAt);
        return selectList(wrapper);
    }

    default List<BizTypePO> findByCodes(Set<String> codes) {
        LambdaQueryWrapper<BizTypePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BizTypePO::getCode, codes);
        return selectList(wrapper);
    }

    default BizTypePO findOneByName(String name) {
        LambdaQueryWrapper<BizTypePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizTypePO::getName, name);
        wrapper.last("limit 1");
        return selectOne(wrapper);
    }

    default BizTypePO findOneByCode(String code) {
        LambdaQueryWrapper<BizTypePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizTypePO::getCode, code);
        wrapper.last("limit 1");
        return selectOne(wrapper);
    }
}

package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.OrgRelationCfPO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * (OrgRelationCf)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-19 15:00:41
 */
@Repository
public interface OrgRelationCfRepo extends BaseRepository<OrgRelationCfPO> {

    default List<OrgRelationCfPO> check(Long orgHeadDimensionId, Long orgHeadUnitTypeId, Long orgRelationDimensionId, Long orgRelationUnitTypeId, Long id, String enabled) {
        LambdaQueryWrapper<OrgRelationCfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgRelationCfPO::getOrgHeadDimensionId, orgHeadDimensionId);
        queryWrapper.eq(OrgRelationCfPO::getOrgHeadUnitTypeId, orgHeadUnitTypeId);
        queryWrapper.eq(OrgRelationCfPO::getOrgRelationDimensionId, orgRelationDimensionId);
        queryWrapper.eq(OrgRelationCfPO::getOrgRelationUnitTypeId, orgRelationUnitTypeId);
        queryWrapper.eq(OrgRelationCfPO::getOrgRelationStatus, enabled);
        if (Objects.nonNull(id)) {
            queryWrapper.notIn(OrgRelationCfPO::getId, id);
        }
        return this.selectList(queryWrapper);
    }

    default OrgRelationCfPO queryOne(Long orgHeadUnitId, Long orgRelationUnitId) {
        LambdaQueryWrapper<OrgRelationCfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgRelationCfPO::getOrgHeadUnitId, orgHeadUnitId);
        queryWrapper.eq(OrgRelationCfPO::getOrgRelationUnitId, orgRelationUnitId);
        return this.selectOne(queryWrapper);
    }
}

package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.OrgLegalPersonRelationCfPO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 法人公司关联关系表(OrgLegalPersonRelationCf)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-28 19:43:09
 */
@Repository
public interface OrgLegalPersonRelationCfRepo extends BaseRepository<OrgLegalPersonRelationCfPO> {

    default List<OrgLegalPersonRelationCfPO> queryByOrgStructIds(Set<Long> orgStructIs, String status) {
        LambdaQueryWrapper<OrgLegalPersonRelationCfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrgLegalPersonRelationCfPO::getOrgRelationUnitId, orgStructIs);
        queryWrapper.eq(OrgLegalPersonRelationCfPO::getOrgLegalStatus, status);
        return this.selectList(queryWrapper);
    }
}

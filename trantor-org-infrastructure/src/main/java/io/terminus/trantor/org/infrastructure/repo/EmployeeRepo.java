package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.terminus.common.api.model.Paging;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.dto.EmployeePageQueryDTO;
import io.terminus.trantor.org.spi.model.po.EmployeePO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 员工表(Employee)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-27 20:38:16
 */
@Repository
public interface EmployeeRepo extends BaseRepository<EmployeePO> {

    default EmployeePO queryByMobile(String mobile) {
        LambdaQueryWrapper<EmployeePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeePO::getMobile, mobile);
        return this.selectOne(queryWrapper);
    }

    default EmployeePO queryByEmail(String email) {
        LambdaQueryWrapper<EmployeePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeePO::getEmail, email);
        return this.selectOne(queryWrapper);
    }

    default EmployeePO queryByUserId(Long userId) {
        LambdaQueryWrapper<EmployeePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeePO::getUserId, userId);
        return this.selectOne(queryWrapper);
    }

    default List<EmployeePO> queryByUserIds(Set<Long> userIds) {
        LambdaQueryWrapper<EmployeePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EmployeePO::getUserId, userIds);
        return this.selectList(queryWrapper);
    }

    default EmployeePO queryByCode(String code) {
        LambdaQueryWrapper<EmployeePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeePO::getCode, code);
        return this.selectOne(queryWrapper);
    }

    default List<EmployeePO> queryByCodes(Set<String> codes) {
        LambdaQueryWrapper<EmployeePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EmployeePO::getCode, codes);
        return this.selectList(queryWrapper);
    }

    default Paging<EmployeePO> page(EmployeePageQueryDTO request) {
        LambdaQueryWrapper<EmployeePO> queryWrapper = new LambdaQueryWrapper<>();
        if (!CollectionUtils.isEmpty(request.getExcludeIds())) {
            queryWrapper.notIn(EmployeePO::getId, request.getExcludeIds());
        }
        if (StringUtils.hasText(request.getCode())) {
            queryWrapper.like(EmployeePO::getCode, request.getCode());
        }
        if (StringUtils.hasText(request.getType())) {
            queryWrapper.eq(EmployeePO::getType, request.getType());
        }
        if (StringUtils.hasText(request.getName())) {
            queryWrapper.like(EmployeePO::getName, request.getName());
        }
        if (Objects.nonNull(request.getUserId())) {
            queryWrapper.eq(EmployeePO::getUserId, request.getUserId());
        }
        if (StringUtils.hasText(request.getMobile())) {
            queryWrapper.like(EmployeePO::getMobile, request.getMobile());
        }
        if (StringUtils.hasText(request.getStatus())) {
            queryWrapper.eq(EmployeePO::getStatus, request.getStatus());
        }
        if (Objects.nonNull(request.getId())) {
            queryWrapper.eq(EmployeePO::getId, request.getId());
        }
        return this.selectPage(request, queryWrapper);
    }

    IPage<EmployeePO> pageByOrg(Page<EmployeePO> page, @Param("dto") EmployeePageQueryDTO dto);

    IPage<EmployeePO> pageByUserOrgEmployee(Page<EmployeePO> page, @Param("dto") EmployeePageQueryDTO dto, @Param("orgUnitIds") Set<Long> orgUnitIds);

    default List<EmployeePO> selectByCodes(List<String> codes) {
        LambdaQueryWrapper<EmployeePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EmployeePO::getCode, codes);
        return this.selectList(queryWrapper);
    }

    default EmployeePO queryDingTalkCode(String dingTalkCode) {
        LambdaQueryWrapper<EmployeePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeePO::getDingTalkCode, dingTalkCode);
        return this.selectOne(queryWrapper);
    }

    default List<EmployeePO> queryDingTalkCodesAndStatus(List<String> dingTalkCodes, String status) {
        LambdaQueryWrapper<EmployeePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EmployeePO::getDingTalkCode, dingTalkCodes);
        if (StringUtils.hasText(status)) {
            queryWrapper.eq(EmployeePO::getStatus, status);
        }
        return this.selectList(queryWrapper);
    }
}


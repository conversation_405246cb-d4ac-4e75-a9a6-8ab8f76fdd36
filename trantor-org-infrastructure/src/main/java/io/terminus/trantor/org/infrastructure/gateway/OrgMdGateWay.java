package io.terminus.trantor.org.infrastructure.gateway;

import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.erp.api.annotation.TService;
import io.terminus.trantor.org.spi.model.GenAddrQueryDTO;
import io.terminus.trantor.org.spi.model.GenAddrTypeCfDTO;
import io.terminus.trantor.org.spi.model.GenPartnerCodesQueryDTO;
import io.terminus.trantor.org.spi.model.PartnerDetailDTO;
import io.terminus.trantor.org.spi.model.dto.GenAttrCfDTO;
import io.terminus.trantor.org.spi.model.dto.GenAttrCodesQueryDTO;
import io.terminus.trantor.org.spi.model.dto.attr.GenAttrDTO;
import io.terminus.trantor.org.spi.model.dto.attr.GenAttrGroupDTO;
import io.terminus.trantor.org.spi.model.dto.dict.DictHeadPageQueryDTO;
import io.terminus.trantor.org.spi.model.dto.dict.GenDictHeadDTO;
import io.terminus.trantor.org.spi.model.dto.dict.GenDictItemDTO;
import io.terminus.trantor2.doc.annotation.Action;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-11-21 18:10
 */
@TService
public interface OrgMdGateWay {

    @Action(name = "根据ID集合查询属性分组", value = "GEN_API_ATTR_GROUP_QUERY_BY_IDS_ACTION")
    List<GenAttrGroupDTO> findAttrGroupByIds(@RequestBody IdsRequest request);

    @Action(name = "根据ID查询属性分组", value = "GEN_API_ATTR_GROUP_QUERY_BY_ID_ACTION")
    GenAttrGroupDTO findAttrGroupById(@RequestBody IdRequest request);

    @Action(name = "根据ID集合查询属性", value = "GEN_API_ATTR_QUERY_BY_IDS_ACTION")
    List<GenAttrDTO> findAttrByIds(@RequestBody IdsRequest request);

    @Action(name = "根据code集合查询属性", value = "GEN_API_ATTR_QUERY_BY_CODES_ACTION")
    List<GenAttrCfDTO> findAttrByCodes(@RequestBody GenAttrCodesQueryDTO request);

    @Action(name = "根据属性分组ID查询属性", value = "GEN_API_ATTR_QUERY_BY_ATTR_GROUP_ID_ACTION")
    List<GenAttrDTO> findAttrByAttrGroupId(@RequestBody IdRequest request);

    @Action(name = "根据属性分组ID集合查询属性", value = "GEN_API_ATTR_QUERY_BY_ATTR_GROUP_IDS_ACTION")
    List<GenAttrDTO> findAttrByAttrGroupIds(@RequestBody IdsRequest request);

    @Action(name = "根据ID集合查询数据字典项", value = "GEN_API_DICT_ITEM_QUERY_BY_IDS_ACTION")
    List<GenDictItemDTO> findDictItemByIds(@RequestBody IdsRequest request);

    @Action(name = "根据字典类别查询数据字典项", value = "GEN_API_DICT_ITEM_QUERY_BY_DICT_HEAD_ID_ACTION")
    List<GenDictItemDTO> findDictItemByDictHeadId(@RequestBody IdRequest request);

    @Action(name = "根据字典类别和字典名称查询字典项", value = "GEN_API_DICT_ITEM_QUERY_BY_DICT_HEAD_ID_AND_NAME_ACTION")
    GenDictItemDTO findDictItemByDictHeadIdAndName(@RequestBody GenDictItemDTO request);

    @Action(name = "根据数据字典项编码查询", value = "GEN_DICT_ITEM_QUERY_BY_CODE_ACTION")
    GenDictItemDTO queryDictItemByCode(@RequestBody GenDictItemDTO genDictItemCfDTO);

    @Action(name = "数据字典分页查询", value = "GEN_DICT_HEAD_PAGE_ACTION")
    Paging<GenDictHeadDTO> dictHeadPaging(@RequestBody DictHeadPageQueryDTO genDictHeadPageQueryDTO);

    @Action(name = "查询合作伙伴详情信息", value = "GEN_COM_PARTNER_QUERY_BY_ID_ACTION")
    PartnerDetailDTO queryPartnerDetailById(@RequestBody IdRequest request);

    @Action(name = "根据合作伙伴编码批量查询合作伙伴信息", value = "GEN_COM_PARTNER_QUERY_BY_CODES_ACTION")
    List<PartnerDetailDTO> queryPartnerByCodes(@RequestBody GenPartnerCodesQueryDTO request);

    @Action(name = "根据地址名称批量查询地址库", value = "GEN_API_ADDR_TYPE_QUERY_BY_NAMES_ACTION")
    List<GenAddrTypeCfDTO> queryAddressByNames(@RequestBody GenAddrQueryDTO request);
}

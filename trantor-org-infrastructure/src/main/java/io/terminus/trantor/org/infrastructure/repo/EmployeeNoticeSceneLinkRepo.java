package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.EmployeeNoticeSceneLinkPO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 组织员工关联表(EmployeeLink)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-27 20:42:05
 */
@Repository
public interface EmployeeNoticeSceneLinkRepo extends BaseRepository<EmployeeNoticeSceneLinkPO> {

    default List<EmployeeNoticeSceneLinkPO> queryByEmployeeId(Long employeeId) {
        LambdaQueryWrapper<EmployeeNoticeSceneLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeNoticeSceneLinkPO::getEmployeeId, employeeId);
        return this.selectList(queryWrapper);
    }

    default void deleteByEmployeeId(Long employeeId) {
        LambdaQueryWrapper<EmployeeNoticeSceneLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeNoticeSceneLinkPO::getEmployeeId, employeeId);
        this.delete(queryWrapper);
    }

    default List<EmployeeNoticeSceneLinkPO> queryBySceneKey(String sceneKey) {
        LambdaQueryWrapper<EmployeeNoticeSceneLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeNoticeSceneLinkPO::getNoticeSceneKey, sceneKey);
        return this.selectList(queryWrapper);
    }
}


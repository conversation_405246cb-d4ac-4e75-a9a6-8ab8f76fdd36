package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.OrgStructBusinessTypeLinkPO;
import org.springframework.stereotype.Repository;

/**
 * @author: 张博
 * @date: 2024-06-13 16:50
 */
@Repository
public interface OrgStructBusinessTypeLinkRepo extends BaseRepository<OrgStructBusinessTypeLinkPO> {

    default void deleteByOrgStructId(Long orgStructId) {
        delete(Wrappers.lambdaQuery(OrgStructBusinessTypeLinkPO.class).eq(OrgStructBusinessTypeLinkPO::getOrgStructId, orgStructId));
    }
}

package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.dict.OrgStatusDict;
import io.terminus.trantor.org.spi.model.po.OrgDimensionCfPO;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 组织维度表(OrgDimensionCf)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-10 17:37:37
 */
@Repository
public interface OrgDimensionCfRepo extends BaseRepository<OrgDimensionCfPO> {


    default OrgDimensionCfPO findByCode(String orgDimensionCode) {
        LambdaQueryWrapper<OrgDimensionCfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgDimensionCfPO::getOrgDimensionCode, orgDimensionCode);
        return selectOne(queryWrapper);
    }

    default List<OrgDimensionCfPO> queryEnableList() {
        LambdaQueryWrapper<OrgDimensionCfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgDimensionCfPO::getOrgDimensionStatus, OrgStatusDict.ENABLED);
        return this.selectList(queryWrapper);
    }
}

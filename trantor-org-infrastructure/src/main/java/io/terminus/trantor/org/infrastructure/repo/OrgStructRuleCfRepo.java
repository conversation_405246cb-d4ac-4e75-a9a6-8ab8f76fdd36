package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.trantor.org.spi.model.po.OrgStructRuleCfPO;

import java.util.List;
import java.util.Objects;

/**
 * 组织架构规则设置(OrgStructRuleCf)表数据库访问层
 *
 * <AUTHOR>
 * @since  2024-03-19 10:50:23
 */
@Repository
public interface OrgStructRuleCfRepo extends BaseRepository<OrgStructRuleCfPO> {

    default List<OrgStructRuleCfPO> selectRuleByParentId(Long orgDimensionId, Long orgBusinessTypeId){
        LambdaQueryWrapper<OrgStructRuleCfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgStructRuleCfPO::getOrgDimensionId,orgDimensionId);
        if (Objects.nonNull(orgBusinessTypeId)){
            queryWrapper.eq(OrgStructRuleCfPO::getOrgDimensionBusinessLinkId,orgBusinessTypeId);
        }else {
            queryWrapper.isNull(OrgStructRuleCfPO::getOrgDimensionBusinessLinkId);
        }
        return this.selectList(queryWrapper);
    }
}

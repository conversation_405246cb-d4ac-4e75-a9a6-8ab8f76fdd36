package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.EmployeePO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 员工表 支持多组织切换
 *
 * <AUTHOR>
 * @since 2023-02-27 20:38:16
 */
@Repository
@InterceptorIgnore(tenantLine = "true")
public interface EmployeeOrgSwitchRepo extends BaseRepository<EmployeePO> {

    default List<EmployeePO> queryByUserId(Long userId) {
        LambdaQueryWrapper<EmployeePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeePO::getUserId, userId);
        return this.selectList(queryWrapper);
    }
}


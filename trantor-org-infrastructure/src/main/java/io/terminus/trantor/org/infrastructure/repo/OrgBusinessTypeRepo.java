package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.OrgBusinessTypePO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023-11-14 14:48:14
 */
@Repository
public interface OrgBusinessTypeRepo extends BaseRepository<OrgBusinessTypePO> {

    default List<OrgBusinessTypePO> selectByIds(Set<Long> collect, String enabled){
        LambdaQueryWrapper<OrgBusinessTypePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrgBusinessTypePO::getId,collect);
        queryWrapper.eq(OrgBusinessTypePO::getStatus,enabled);
        return this.selectList(queryWrapper);
    }

    default OrgBusinessTypePO findByCode(String code) {
        LambdaQueryWrapper<OrgBusinessTypePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgBusinessTypePO::getCode, code);
        return this.selectOne(queryWrapper);
    }
}

package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.EmployeeOrgLinkPO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 组织员工关联表(EmployeeLink)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-27 20:42:05
 */
@Repository
public interface EmployeeOrgLinkRepo extends BaseRepository<EmployeeOrgLinkPO> {

    default List<EmployeeOrgLinkPO> queryByEmployeeId(Long employeeId) {
        LambdaQueryWrapper<EmployeeOrgLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeOrgLinkPO::getEmployeeId, employeeId);
        return this.selectList(queryWrapper);
    }
    default EmployeeOrgLinkPO queryByMainEmployeeId(Long employeeId,Boolean flag) {
        LambdaQueryWrapper<EmployeeOrgLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeOrgLinkPO::getEmployeeId, employeeId);
        queryWrapper.eq(EmployeeOrgLinkPO::getIsMainOrg, flag);
        return this.selectOne(queryWrapper);
    }

    default List<EmployeeOrgLinkPO> queryByOrgUnitIds(Set<Long> orgUnitIds) {
        LambdaQueryWrapper<EmployeeOrgLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EmployeeOrgLinkPO::getOrgUnitId, orgUnitIds);
        return this.selectList(queryWrapper);
    }

    default void deleteByEmployeeId(Long employeeId) {
        LambdaQueryWrapper<EmployeeOrgLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeOrgLinkPO::getEmployeeId, employeeId);
        this.delete(queryWrapper);
    }

    default void deleteByOrgId(Long orgId) {
        LambdaQueryWrapper<EmployeeOrgLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeOrgLinkPO::getOrgUnitId, orgId);
        this.delete(queryWrapper);
    }

    default void deleteByOrgIds(Set<Long> orgIds) {
        LambdaQueryWrapper<EmployeeOrgLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EmployeeOrgLinkPO::getOrgUnitId, orgIds);
        this.delete(queryWrapper);
    }

    default EmployeeOrgLinkPO queryByEmployeeAndOrgAndRankId(Long employeeId, Long orgUnitId, Long rankId) {
        LambdaQueryWrapper<EmployeeOrgLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeOrgLinkPO::getEmployeeId, employeeId);
        queryWrapper.eq(EmployeeOrgLinkPO::getOrgUnitId, orgUnitId);
        queryWrapper.eq(EmployeeOrgLinkPO::getIdentityId, rankId);
        return this.selectOne(queryWrapper);
    }

    default List<EmployeeOrgLinkPO> queryOrgUnitAppointRankEmployee(Long orgUnitId, Set<Long> rankIdSet, Boolean isMainOrg) {
        LambdaQueryWrapper<EmployeeOrgLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeOrgLinkPO::getOrgUnitId, orgUnitId);
        queryWrapper.in(EmployeeOrgLinkPO::getIdentityId, rankIdSet);
        queryWrapper.eq(EmployeeOrgLinkPO::getIsMainOrg, isMainOrg);
        return this.selectList(queryWrapper);
    }

    default List<EmployeeOrgLinkPO> queryOrgUnitByRankEmployee(Long orgUnitId, Long rankId, Boolean isMainOrg) {
        LambdaQueryWrapper<EmployeeOrgLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeOrgLinkPO::getOrgUnitId, orgUnitId);
        queryWrapper.eq(EmployeeOrgLinkPO::getIdentityId, rankId);
        queryWrapper.eq(EmployeeOrgLinkPO::getIsMainOrg, isMainOrg);
        return this.selectList(queryWrapper);
    }

    default List<EmployeeOrgLinkPO> queryOrgUnitByUnitId(Long id){
        LambdaQueryWrapper<EmployeeOrgLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeOrgLinkPO::getOrgUnitId, id);
        return this.selectList(queryWrapper);
    }

    default List<EmployeeOrgLinkPO> queryByEmployeeIdAndRankId(Long employeeId, Long rankId) {
        LambdaQueryWrapper<EmployeeOrgLinkPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeOrgLinkPO::getEmployeeId, employeeId);
        queryWrapper.eq(EmployeeOrgLinkPO::getIdentityId, rankId);
        return this.selectList(queryWrapper);
    }
}


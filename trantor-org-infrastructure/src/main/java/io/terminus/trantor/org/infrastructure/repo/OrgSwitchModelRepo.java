package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.po.OrgSwitchModelCfPO;
import org.springframework.stereotype.Repository;

@Repository
public interface OrgSwitchModelRepo extends BaseRepository<OrgSwitchModelCfPO> {

    default OrgSwitchModelCfPO queryByModelKey(String modelKey) {
        LambdaQueryWrapper<OrgSwitchModelCfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgSwitchModelCfPO::getModelKey, modelKey);
        return this.selectOne(queryWrapper);
    }
}

package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.model.dto.OrgRankCfQueryDTO;
import io.terminus.trantor.org.spi.model.po.OrgRankCfPO;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * (OrgRankCf)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-23 16:39:46
 */
@Repository
public interface OrgRankCfRepo extends BaseRepository<OrgRankCfPO> {

    default List<OrgRankCfPO> findAll(OrgRankCfQueryDTO request){
        LambdaQueryWrapper<OrgRankCfPO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(request.getCode())) {
            queryWrapper.eq(OrgRankCfPO::getCode, request.getCode());
        }
        if (StringUtils.hasText(request.getStatus())) {
            queryWrapper.eq(OrgRankCfPO::getStatus, request.getStatus());
        }
        if (StringUtils.hasText(request.getName())) {
            queryWrapper.like(OrgRankCfPO::getName, request.getName());
        }
        return this.selectList(queryWrapper);
    };

    default OrgRankCfPO selectByCode(String rankCode){
        LambdaQueryWrapper<OrgRankCfPO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(rankCode)) {
            queryWrapper.eq(OrgRankCfPO::getCode, rankCode);
        }
        return this.selectOne(queryWrapper);
    }
}

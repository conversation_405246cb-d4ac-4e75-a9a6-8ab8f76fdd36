package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.terminus.common.mybatis.repository.BaseRepository;
import org.springframework.stereotype.Repository;
import io.terminus.trantor.org.spi.model.po.OrgRelationRuleCfPO;

import java.util.Objects;

/**
 * 组织关联规则设置(OrgRelationRuleCf)表数据库访问层
 *
 * <AUTHOR>
 * @since  2024-03-19 10:51:04
 */
@Repository
public interface OrgRelationRuleCfRepo extends BaseRepository<OrgRelationRuleCfPO> {

    default OrgRelationRuleCfPO selectRule(Long orgHeadDimensionId, Long orgHeadUnitTypeId, Long orgRelationDimensionId, Long orgRelationUnitTypeId){
        LambdaQueryWrapper<OrgRelationRuleCfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgRelationRuleCfPO::getOrgHeadDimensionId,orgHeadDimensionId);
        queryWrapper.eq(OrgRelationRuleCfPO::getOrgHeadUnitTypeId,orgHeadUnitTypeId);
        queryWrapper.eq(OrgRelationRuleCfPO::getOrgRelationDimensionId,orgRelationDimensionId);
        queryWrapper.eq(OrgRelationRuleCfPO::getOrgRelationUnitTypeId,orgRelationUnitTypeId);
        return this.selectOne(queryWrapper);
    }

    default OrgRelationRuleCfPO check(Long orgHeadDimensionId, Long orgHeadUnitTypeId, Long orgRelationDimensionId, Long orgRelationUnitTypeId, Long id){
        LambdaQueryWrapper<OrgRelationRuleCfPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgRelationRuleCfPO::getOrgHeadDimensionId,orgHeadDimensionId);
        queryWrapper.eq(OrgRelationRuleCfPO::getOrgHeadUnitTypeId,orgHeadUnitTypeId);
        queryWrapper.eq(OrgRelationRuleCfPO::getOrgRelationDimensionId,orgRelationDimensionId);
        queryWrapper.eq(OrgRelationRuleCfPO::getOrgRelationUnitTypeId,orgRelationUnitTypeId);
        if (Objects.nonNull(id)){
            queryWrapper.ne(OrgRelationRuleCfPO::getId,id);
        }
        return this.selectOne(queryWrapper);
    }
}

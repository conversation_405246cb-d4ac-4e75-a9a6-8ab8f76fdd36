package io.terminus.trantor.org.infrastructure.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.terminus.common.api.model.Paging;
import io.terminus.common.mybatis.repository.BaseRepository;
import io.terminus.trantor.org.spi.dict.OrgStatusDict;
import io.terminus.trantor.org.spi.model.dto.*;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 组织架构表(OrgStructMd)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-02 14:29:19
 */
@Repository
public interface OrgStructMdRepo extends BaseRepository<OrgStructMdPO> {

    default List<OrgStructMdPO> findByPid(OrgStructMdQueryDTO request) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        if (request.getOrgParentId() != null) {
            queryWrapper.eq(OrgStructMdPO::getOrgParentId, request.getOrgParentId());
        } else {
            queryWrapper.isNull(OrgStructMdPO::getOrgParentId);
        }
        if (!CollectionUtils.isEmpty(request.getOrgStatus())) {
            queryWrapper.in(OrgStructMdPO::getOrgStatus, request.getOrgStatus());
        }
        if (request.getOrgDimensionId() != null) {
            queryWrapper.eq(OrgStructMdPO::getOrgDimensionId, request.getOrgDimensionId());
        }
        queryWrapper.orderByAsc(OrgStructMdPO::getOrgSort).orderByDesc(OrgStructMdPO::getCreatedAt);
        return this.selectList(queryWrapper);
    }

    default List<OrgStructMdPO> queryByPid(Long pid) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgStructMdPO::getOrgParentId, pid);
        return this.selectList(queryWrapper);
    }

    default List<OrgStructMdPO> queryByPid(Long pid, Long dimensionId) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.isNull(pid)) {
            queryWrapper.isNull(OrgStructMdPO::getOrgParentId);
        } else {
            queryWrapper.eq(OrgStructMdPO::getOrgParentId, pid);
        }
        queryWrapper.eq(OrgStructMdPO::getOrgDimensionId, dimensionId);
        return this.selectList(queryWrapper);
    }

    default List<OrgStructMdPO> queryByPid(Long pid, Long dimensionId, Set<String> statusList) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.isNull(pid)) {
            queryWrapper.isNull(OrgStructMdPO::getOrgParentId);
        } else {
            queryWrapper.eq(OrgStructMdPO::getOrgParentId, pid);
        }
        if (!CollectionUtils.isEmpty(statusList)) {
            queryWrapper.in(OrgStructMdPO::getOrgStatus, statusList);
        }
        queryWrapper.eq(OrgStructMdPO::getOrgDimensionId, dimensionId);
        return this.selectList(queryWrapper);
    }

    default List<OrgStructMdPO> queryByDimensionId(Long dimensionId, Set<String> status) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgStructMdPO::getOrgDimensionId, dimensionId);
        if (!CollectionUtils.isEmpty(status)) {
            queryWrapper.in(OrgStructMdPO::getOrgStatus, status);
        }
        return this.selectList(queryWrapper);
    }

    default List<OrgStructMdPO> findTreeByName(OrgStructMdQueryTreeDTO request, Long dimensionId) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(request.getOrgCode())) {
            queryWrapper.eq(OrgStructMdPO::getOrgCode, request.getOrgCode());
        }
        if (StringUtils.hasText(request.getOrgName())) {
            queryWrapper.like(OrgStructMdPO::getOrgName, request.getOrgName());
        }
        if (!CollectionUtils.isEmpty(request.getOrgStatus())) {
            queryWrapper.in(OrgStructMdPO::getOrgStatus, request.getOrgStatus());
        }
        if (Objects.nonNull(dimensionId)) {
            queryWrapper.eq(OrgStructMdPO::getOrgDimensionId, dimensionId);
        }
        queryWrapper.orderByAsc(OrgStructMdPO::getOrgSort).orderByDesc(OrgStructMdPO::getCreatedAt);
        return this.selectList(queryWrapper);
    }

    default OrgStructMdPO selectByDimensionId(Long dimensionId) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(dimensionId)) {
            queryWrapper.eq(OrgStructMdPO::getOrgDimensionId, dimensionId);
        }
        queryWrapper.isNull(OrgStructMdPO::getOrgParentId);
        return this.selectOne(queryWrapper);
    }

    default OrgStructMdPO queryRoot(Long dimensionId) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(dimensionId)) {
            queryWrapper.eq(OrgStructMdPO::getOrgDimensionId, dimensionId);
        }
        queryWrapper.isNull(OrgStructMdPO::getOrgParentId);
        return this.selectOne(queryWrapper);
    }

    default OrgStructMdPO selectByCode(Long dimensionId, String orgCode) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(dimensionId)) {
            queryWrapper.eq(OrgStructMdPO::getOrgDimensionId, dimensionId);
        }
        if (StringUtils.hasText(orgCode)) {
            queryWrapper.eq(OrgStructMdPO::getOrgCode, orgCode);
        }
        return this.selectOne(queryWrapper);
    }

    default List<OrgStructMdPO> selectChildren(Long searchPid) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(searchPid)) {
            queryWrapper.eq(OrgStructMdPO::getOrgParentId, searchPid);
        }
        return this.selectList(queryWrapper);
    }

    default void updateStatusById(Long id) {
        LambdaUpdateWrapper<OrgStructMdPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(OrgStructMdPO::getOrgStatus, OrgStatusDict.ENABLED);
        wrapper.eq(OrgStructMdPO::getId, id);
        this.update(null, wrapper);
    }

    default void updateEnableStatus(Set<Long> enableList) {
        LambdaUpdateWrapper<OrgStructMdPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(OrgStructMdPO::getOrgStatus, OrgStatusDict.DISABLED);
        wrapper.in(OrgStructMdPO::getId, enableList);
        this.update(null, wrapper);
    }

    default List<OrgStructMdPO> selectSaveInfo() {
        List<String> statusList = new ArrayList<>();
        statusList.add(OrgStatusDict.ENABLED);
        statusList.add(OrgStatusDict.DISABLED);
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrgStructMdPO::getOrgStatus, statusList);
        return this.selectList(queryWrapper);
    }

    default List<OrgStructMdPO> search(OrgStructMdQueryTreeDTO request) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(request.getOrgName())) {
            queryWrapper.like(OrgStructMdPO::getOrgName, request.getOrgName());
        }
        if (!CollectionUtils.isEmpty(request.getOrgStatus())) {
            queryWrapper.in(OrgStructMdPO::getOrgStatus, request.getOrgStatus());
        }
        if (StringUtils.hasText(request.getOrgCode())) {
            queryWrapper.like(OrgStructMdPO::getOrgCode, request.getOrgCode());
        }
        if (Objects.nonNull(request.getOrgDimensionId())) {
            queryWrapper.eq(OrgStructMdPO::getOrgDimensionId, request.getOrgDimensionId());
        }
        return this.selectList(queryWrapper);
    }

    default void updateNull(OrgStructMdPO orgStructMdPO) {
        LambdaUpdateWrapper<OrgStructMdPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(OrgStructMdPO::getDef1, null);
        wrapper.set(OrgStructMdPO::getDef2, null);
        wrapper.set(OrgStructMdPO::getDef3, null);
        wrapper.set(OrgStructMdPO::getDef4, null);
        wrapper.set(OrgStructMdPO::getDef5, null);
        wrapper.set(OrgStructMdPO::getDef6, null);
        wrapper.set(OrgStructMdPO::getDef7, null);
        wrapper.set(OrgStructMdPO::getDef8, null);
        wrapper.set(OrgStructMdPO::getDef9, null);
        wrapper.set(OrgStructMdPO::getDef10, null);
        wrapper.set(OrgStructMdPO::getDef11, null);
        wrapper.set(OrgStructMdPO::getDef12, null);
        wrapper.set(OrgStructMdPO::getDef13, null);
        wrapper.set(OrgStructMdPO::getDef14, null);
        wrapper.set(OrgStructMdPO::getDef15, null);
        wrapper.set(OrgStructMdPO::getDef16, null);
        wrapper.set(OrgStructMdPO::getDef17, null);
        wrapper.set(OrgStructMdPO::getDef18, null);
        wrapper.set(OrgStructMdPO::getDef19, null);
        wrapper.set(OrgStructMdPO::getDef20, null);
        wrapper.eq(OrgStructMdPO::getId, orgStructMdPO.getId());
        this.update(null, wrapper);
    }

    default void updateLeaf(Long orgParentId) {
        LambdaUpdateWrapper<OrgStructMdPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(OrgStructMdPO::getLeaf, Boolean.TRUE);
        wrapper.eq(OrgStructMdPO::getId, orgParentId);
        this.update(null, wrapper);
    }

    default Paging<OrgStructMdPO> paging(OrgStructPageQueryDTO request, Map<String, Object> queryParams) {
        QueryWrapper<OrgStructMdPO> queryWrapper = new QueryWrapper<>();
        if (!CollectionUtils.isEmpty(request.getIds())) {
            queryWrapper.in("id", request.getIds());
        }
        if (!CollectionUtils.isEmpty(request.getExcludeIds())) {
            queryWrapper.notIn("id", request.getExcludeIds());
        }
        if (StringUtils.hasText(request.getOrgCode())) {
            queryWrapper.eq("org_code", request.getOrgCode());
        }
        if (StringUtils.hasText(request.getOrgName())) {
            queryWrapper.like("org_name", request.getOrgName());
        }
        if (Objects.nonNull(request.getOrgParentId())) {
            queryWrapper.eq("org_parent_id", request.getOrgParentId());
        }
        if (Objects.nonNull(request.getOrgDimensionId())) {
            queryWrapper.eq("org_dimension_id", request.getOrgDimensionId());
        }
        if (Objects.nonNull(request.getOrgBusinessTypeId())) {
            queryWrapper.like("org_business_type_ids", request.getOrgBusinessTypeId());
        }
        if (StringUtils.hasText(request.getOrgBusinessTypeCode())) {
            queryWrapper.like("org_business_type_codes", request.getOrgBusinessTypeCode());
        }
        if (Objects.nonNull(request.getLeaf())) {
            queryWrapper.eq("is_leaf", request.getLeaf());
        }
        if (!CollectionUtils.isEmpty(request.getOrgStatusSet())) {
            queryWrapper.in("org_status", request.getOrgStatusSet());
        }
        if (!CollectionUtils.isEmpty(queryParams)) {
            for (Map.Entry<String, Object> entry : queryParams.entrySet()) {
                queryWrapper.eq(entry.getKey(), entry.getValue());
            }
        }
        return this.selectPage(request, queryWrapper);
    }

    default List<OrgStructMdPO> queryByCodes(Set<String> codes, Long orgDimensionId) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrgStructMdPO::getOrgCode, codes);
        queryWrapper.eq(OrgStructMdPO::getOrgDimensionId, orgDimensionId);
        return this.selectList(queryWrapper);
    }

    default Long countByPid(Long parentId) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgStructMdPO::getOrgParentId, parentId);
        return this.selectCount(queryWrapper);
    }

    default List<OrgStructMdPO> queryNeedEnableRecord(LocalDateTime time) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgStructMdPO::getOrgEnableDate, time);
        queryWrapper.eq(OrgStructMdPO::getOrgStatus, OrgStatusDict.INACTIVE);
        return this.selectList(queryWrapper);
    }

    default List<OrgStructMdPO> queryStructByCodes(OrgQueryCodesDTO request){
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(request.getOrgDimensionCode())) {
            queryWrapper.eq(OrgStructMdPO::getOrgDimensionCode, request.getOrgDimensionCode());
        }
        queryWrapper.in(OrgStructMdPO::getOrgCode,request.getOrgCodes());
        return this.selectList(queryWrapper);
    }

    default List<OrgStructMdPO> queryStructByNames(OrgQueryNamesDTO request) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(request.getOrgDimensionCode())) {
            queryWrapper.eq(OrgStructMdPO::getOrgDimensionCode, request.getOrgDimensionCode());
        }
        queryWrapper.in(OrgStructMdPO::getOrgName, request.getOrgNames());
        return this.selectList(queryWrapper);
    }

    default List<OrgStructMdPO> queryAllChild(Long pid) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(OrgStructMdPO::getPath, pid);
        return this.selectList(queryWrapper);
    }

    default List<OrgStructMdPO> queryInvLocByWhNumIds(Set<Long> whNumIds) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrgStructMdPO::getDef10, whNumIds);
        return this.selectList(queryWrapper);
    }
}

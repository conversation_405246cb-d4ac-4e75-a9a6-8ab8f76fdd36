spring:
  application:
    name: trantor-org
mybatis-plus:
  type-aliases-package:
    io.terminus.trantor.org.spi.model.po
  global-config:
    enable-sql-runner: true
  mapper-locations: classpath:mapper/*.xml
swagger2:
  base-package: io.terminus.trantor.org

# 通过erda部署后，下列配置通过addon注入环境变量或通过环境配置进行管理，无需在yml中声明，仅在本地启动时使用

# 接口文档
SWAGGER2_ENABLED: false
trantor:
  engine:
    host: http://trantor2-b1a7dfb306.project-190-dev.svc.cluster.local:8080
    upload-enabled: false
    user-id: ${TRANTOR_CONSOLE_USER_ID:1000001}
    team-id: ${TRANTOR_CONSOLE_TEAM_ID:54}
    apps:
      - app-id: ${TRANTOR_CONSOLE_APP_ID:85}
        action-package: io.terminus.trantor.org
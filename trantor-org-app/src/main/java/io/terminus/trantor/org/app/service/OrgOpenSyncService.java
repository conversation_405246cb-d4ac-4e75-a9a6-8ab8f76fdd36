package io.terminus.trantor.org.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.model.RootModel;
import io.terminus.common.sequence.IdGenerator;
import io.terminus.trantor.org.infrastructure.repo.*;
import io.terminus.trantor.org.spi.dict.OrgStatusDict;
import io.terminus.trantor.org.spi.model.po.EmployeeOrgLinkPO;
import io.terminus.trantor.org.spi.model.po.OrgChargeRelatePO;
import io.terminus.trantor.org.spi.model.po.OrgIdentityPO;
import io.terminus.trantor.org.spi.model.po.OrgStructMdPO;
import io.terminus.trantor.org.spi.model.req.OrgSyncDTO;
import io.terminus.trantor.org.spi.utils.AssertExUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2025/8/26 11:13
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrgOpenSyncService {

    private final OrgStructMdRepo orgStructMdRepo;
    private final IdGenerator idGenerator;
    private final EmployeeRepo srmOrgEmployeeMdRepo;
    private final OrgChargeRelateRepo orgChargeRelateRepo;
    private final EmployeeOrgLinkRepo orgEmployeeOrgLinkCfRepo;
    private final OrgIdentityRepo orgIdentityRepo;

    /**
     * 添加组织机构
     * 将OrgSyncDTO转换成OrgStructMdPO并落库
     *
     * @param orgSyncDTO 组织同步DTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdateOrg(OrgSyncDTO orgSyncDTO) {
        log.info("开始处理组织同步数据: {}", orgSyncDTO);

        // 参数校验
        validateOrgSyncDTO(orgSyncDTO);

        // 检查组织是否已存在
        OrgStructMdPO existingOrg = findOrgByUuid(orgSyncDTO.getOrganizationUuid());

        if (Objects.nonNull(existingOrg)) {
            // 更新现有组织
            OrgStructMdPO newOrg = createOrUpdate(orgSyncDTO, existingOrg.getId());
            log.info("更新组织成功, ID: {}, UUID: {}", existingOrg.getId(), orgSyncDTO.getOrganizationUuid());
        } else {
            // 创建新组织
            OrgStructMdPO newOrg = createOrUpdate(orgSyncDTO, null);
            log.info("创建组织成功, ID: {}, UUID: {}", newOrg.getId(), orgSyncDTO.getOrganizationUuid());
        }
    }

    /**
     * 参数校验
     */
    private void validateOrgSyncDTO(OrgSyncDTO orgSyncDTO) {
        if (orgSyncDTO == null) {
            throw new BusinessException("组织同步数据不能为空");
        }

        if (StringUtils.isBlank(orgSyncDTO.getOrganization())) {
            throw new BusinessException("组织名称不能为空");
        }

        if (StringUtils.isBlank(orgSyncDTO.getOrganizationUuid())) {
            throw new BusinessException("组织UUID不能为空");
        }

        // 如果不是根节点，父级UUID不能为空
        if (!Boolean.TRUE.equals(orgSyncDTO.getRootNode()) && StringUtils.isBlank(orgSyncDTO.getParentUuid())) {
            throw new BusinessException("非根节点的父级UUID不能为空");
        }
    }

    /**
     * 根据UUID查找组织
     */
    private OrgStructMdPO findOrgByUuid(String organizationUuid) {
        LambdaQueryWrapper<OrgStructMdPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgStructMdPO::getOrgCode, organizationUuid);
        return orgStructMdRepo.selectOne(queryWrapper);
    }

    /**
     * 创建新组织
     */
    private OrgStructMdPO createOrUpdate(OrgSyncDTO orgSyncDTO, Long orgId) {
        OrgStructMdPO orgStructMdPO = new OrgStructMdPO();

        // 设置基本信息
        orgStructMdPO.setId(Opt.ofNullable(orgId).orElse(idGenerator.nextId(OrgStructMdPO.class)));
        orgStructMdPO.setOrgName(orgSyncDTO.getOrganization());
        orgStructMdPO.setOrgCode(orgSyncDTO.getOrganizationUuid());
        orgStructMdPO.setOrgStatus(OrgStatusDict.ENABLED);
        orgStructMdPO.setOrgEnableDate(LocalDateTime.now());
        orgStructMdPO.setLeaf(Objects.equals(orgSyncDTO.getRootNode(), false));
        // 设置父级组织ID
        if (StringUtils.isNotBlank(orgSyncDTO.getParentUuid())) {
            orgStructMdPO.setOrgParentCode(orgSyncDTO.getParentUuid());
            Long parentId = findParentOrgId(orgSyncDTO.getParentUuid());
            orgStructMdPO.setOrgParentId(parentId);
        }

        if (Objects.isNull(orgId)) {
            orgStructMdRepo.insert(orgStructMdPO);
        } else {
            orgStructMdRepo.updateById(orgStructMdPO);
        }

        // 创建更新主管关联身份
        createOrUpdateCharge(orgStructMdPO, orgSyncDTO);

        return orgStructMdPO;
    }

    /**
     * 查找父级组织ID
     */
    private Long findParentOrgId(String parentUuid) {
        OrgStructMdPO parentOrg = findOrgByUuid(parentUuid);
        if (Objects.isNull(parentOrg)) {
            return null;
        }
        return parentOrg.getId();
    }

    /**
     * 创建主管关联数据
     */
    private void createOrUpdateCharge(OrgStructMdPO orgStructMdPO, OrgSyncDTO orgSyncDTO) {
        List<OrgSyncDTO.ExtendFieldsDTO> extendFields = orgSyncDTO.getExtendFields();
        if (CollUtil.isEmpty(extendFields)) {
            return;
        }

        HashSet<String> empCodes = new HashSet<>();
        for (OrgSyncDTO.ExtendFieldsDTO extendField : extendFields) {
            if (StrUtil.isBlank(extendField.getOrgheadid())) {
                continue;
            }

            List<String> empCodesTmp = StrUtil.split(extendField.getOrgheadid(), ",");
            empCodes.addAll(empCodesTmp);
        }


        LambdaQueryWrapper<OrgChargeRelatePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrgChargeRelatePO::getOrgCode, orgStructMdPO.getOrgCode());
        List<OrgChargeRelatePO> orgChargeRelatePOS = orgChargeRelateRepo.selectList(lambdaQueryWrapper);
        Map<String, OrgChargeRelatePO> empCodeIdMap = orgChargeRelatePOS.stream().collect(Collectors.toMap(OrgChargeRelatePO::getEmpCode, Function.identity(), (a, b) -> a));

        // 需要创建的主管
        List<OrgChargeRelatePO> orgChargeRelateToCreate = empCodes.stream()
                .filter(it -> !empCodeIdMap.containsKey(it)).map(it -> {
                    OrgChargeRelatePO orgChargeRelateNew = new OrgChargeRelatePO();
                    orgChargeRelateNew.setEmpCode(it);
                    orgChargeRelateNew.setOrgCode(orgStructMdPO.getOrgCode());
                    return orgChargeRelateNew;
                }).collect(Collectors.toList());
        orgChargeRelateRepo.insertBatch(orgChargeRelateToCreate);

        // 需要删除的主管
        Set<OrgChargeRelatePO> orgChargeRelatePOSToDelete = orgChargeRelatePOS.stream()
                .filter(it -> !empCodes.contains(it.getEmpCode())).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(orgChargeRelatePOSToDelete)) {
            orgChargeRelateRepo.deleteBatchIds(orgChargeRelatePOSToDelete.stream().map(RootModel::getId).collect(Collectors.toSet()));
        }

        // 创建主管关联关系
        createEmpLink(orgStructMdPO, orgChargeRelateToCreate);
        // 删除主管关联关系
        deleteEmpLink(orgStructMdPO, new ArrayList<>(orgChargeRelatePOSToDelete));


    }

    private void deleteEmpLink(OrgStructMdPO orgStructMdPO, List<OrgChargeRelatePO> orgChargeRelateToCreate) {
        if (CollUtil.isEmpty(orgChargeRelateToCreate)) {
            return;
        }


        Set<String> empCodes = orgChargeRelateToCreate.stream().map(OrgChargeRelatePO::getEmpCode).collect(Collectors.toSet());
        // 查询员工
        OrgIdentityPO orgIdentityPO = orgIdentityRepo.queryNormal();

        // 更新为普通员工
        EmployeeOrgLinkPO employeeOrgLinkPO = new EmployeeOrgLinkPO();
        employeeOrgLinkPO.setIdentityId(orgIdentityPO.getId());

        LambdaUpdateWrapper<EmployeeOrgLinkPO> employeeOrgLinkPOLambdaQueryWrapper = new LambdaUpdateWrapper<>();
        employeeOrgLinkPOLambdaQueryWrapper.in(EmployeeOrgLinkPO::getEmployeeCode, empCodes)
                .eq(EmployeeOrgLinkPO::getOrgUnitCode, orgStructMdPO.getOrgCode());

        orgEmployeeOrgLinkCfRepo.update(employeeOrgLinkPO, employeeOrgLinkPOLambdaQueryWrapper);
    }


    public void createEmpLink(OrgStructMdPO orgStructMdPO, List<OrgChargeRelatePO> orgChargeRelateToCreate) {
        if (CollUtil.isEmpty(orgChargeRelateToCreate)) {
            return;
        }

        // 查询主管身份
        OrgIdentityPO orgIdentityPO = orgIdentityRepo.queryCharge();

        Set<String> empCodes = orgChargeRelateToCreate.stream().map(OrgChargeRelatePO::getEmpCode).collect(Collectors.toSet());

        // 更新为主管
        EmployeeOrgLinkPO employeeOrgLinkPO = new EmployeeOrgLinkPO();
        employeeOrgLinkPO.setIdentityId(orgIdentityPO.getId());

        LambdaUpdateWrapper<EmployeeOrgLinkPO> employeeOrgLinkPOLambdaQueryWrapper = new LambdaUpdateWrapper<>();
        employeeOrgLinkPOLambdaQueryWrapper.in(EmployeeOrgLinkPO::getEmployeeCode, empCodes)
                .eq(EmployeeOrgLinkPO::getOrgUnitCode, orgStructMdPO.getOrgCode());

        orgEmployeeOrgLinkCfRepo.update(employeeOrgLinkPO, employeeOrgLinkPOLambdaQueryWrapper);
    }

    public void deleteOrg(OrgSyncDTO orgSyncDTO) {
        Assert.notNull(orgSyncDTO, AssertExUtil.create("组织编码不能为空"));
        LambdaQueryWrapper<OrgStructMdPO> orgDeleteWarp = new LambdaQueryWrapper<>();
        orgDeleteWarp.eq(OrgStructMdPO::getOrgCode, orgSyncDTO.getOrganizationUuid());
        orgStructMdRepo.delete(orgDeleteWarp);
    }
}

package io.terminus.trantor.org.app.service;

import io.terminus.trantor.org.domain.service.OrgPermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: 张博
 * @date: 2024-09-05 13:32
 */
@Service
@RequiredArgsConstructor
public class OrgPermissionAppService {

    private final OrgPermissionService orgPermissionService;

    public List<Long> queryEmployeeOrg() {
        return orgPermissionService.queryEmployeeOrg();
    }

    public List<Long> queryEmployeeOrgAndChildOrg() {
        return orgPermissionService.queryEmployeeOrgAndChildOrg();
    }

    public List<Long> queryEmployeeJurisdictionOrg() {
        return orgPermissionService.queryEmployeeJurisdictionOrg();
    }

    public List<Long> queryEmployeeJurisdictionOrgAndChildOrg() {
        return orgPermissionService.queryEmployeeJurisdictionOrgAndChildOrg();
    }
}

package io.terminus.trantor.org.app.service;

import io.terminus.common.api.model.Paging;
import io.terminus.trantor.org.domain.service.RoleService;
import io.terminus.trantor.org.spi.model.dto.PageDTO;
import io.terminus.trantor.org.spi.model.dto.RoleDTO;
import io.terminus.trantor.org.spi.model.dto.RolePageDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @author: 张博
 * @date: 2023-09-21 10:09
 */
@Service
@RequiredArgsConstructor
public class RoleAppService {

    private final RoleService roleService;

    public RoleDTO queryById(Long id) {
        return roleService.queryById(id);
    }

    public Paging<RoleDTO> rolePaging(PageDTO request) {
        return roleService.rolePaging(request);
    }

    public Paging<RoleDTO> frontRolePaging(RolePageDTO request) {
        return roleService.frontRolePaging(request);
    }
}

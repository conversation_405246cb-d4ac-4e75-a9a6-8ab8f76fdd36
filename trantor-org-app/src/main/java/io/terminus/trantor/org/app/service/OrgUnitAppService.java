package io.terminus.trantor.org.app.service;

import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdRequest;
import io.terminus.trantor.org.domain.service.OrgUnitService;
import io.terminus.trantor.org.spi.model.OrgQueryDTO;
import io.terminus.trantor.org.spi.model.dto.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @author: 张博
 * @date: 2023-07-11 10:11
 */
@Service
@RequiredArgsConstructor
public class OrgUnitAppService {

    private final OrgUnitService orgUnitService;

    public List<OrgBizTypeCountDTO> queryListAndCount() {
        return orgUnitService.queryListAndCount();
    }

    public OrgUnitDTO save(OrgUnitSaveDTO request) {
        if (Objects.nonNull(request.getId())) {
            return orgUnitService.update(request);
        } else {
            return orgUnitService.create(request);
        }
    }

    public List<OrgUnitDTO> queryOrgUnitByEmployeeCode(EmployeeQueryDTO request) {
        return orgUnitService.queryOrgUnitByEmployeeCode(request);
    }

    public List<OrgUnitDTO> queryByPid(OrgUnitQueryDTO request) {
        return orgUnitService.queryByPid(request);
    }

    public Paging<OrgUnitDTO> paging(OrgUnitPageDTO request) {
        return orgUnitService.paging(request);
    }

    public Paging<OrgUnitDTO> parentPaging(OrgUnitPageDTO request) {
        return orgUnitService.parentPaging(request);
    }

    public OrgUnitDTO queryDetail(Long id) {
        return orgUnitService.queryDetail(id);
    }

    public Boolean delete(Long id) {
        return orgUnitService.delete(id);
    }

    public Boolean enable(Long id) {
        return orgUnitService.enable(id);
    }

    public Boolean disable(Long id) {
        return orgUnitService.disable(id);
    }

    public List<OrgSearchListDTO> search(OrgSearchDTO request) {
        return orgUnitService.search(request);
    }

    public List<OrgUnitDTO> findAll(OrgQueryDTO request) {
        return orgUnitService.findAll(request);
    }

    public OrgUnitInfoDTO queryOrgUnitById(IdRequest request) {
        return orgUnitService.queryOrgUnitById(request);
    }
}

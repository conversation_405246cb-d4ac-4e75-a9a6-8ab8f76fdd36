package io.terminus.trantor.org.app.service;

import io.terminus.common.api.request.IdRequest;
import io.terminus.trantor.org.domain.service.OrgRankService;
import io.terminus.trantor.org.spi.model.dto.OrgRankCfDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;



@Service
@RequiredArgsConstructor
public class OrgRankAppService {

    private final OrgRankService orgRankService;
    public OrgRankCfDTO queryRankById(IdRequest request) {
        return orgRankService.queryRankById(request);
    }
}

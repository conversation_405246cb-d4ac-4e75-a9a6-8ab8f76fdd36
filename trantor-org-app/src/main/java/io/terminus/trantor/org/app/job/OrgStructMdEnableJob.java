package io.terminus.trantor.org.app.job;


import io.terminus.common.scheduler.annotation.Job;
import io.terminus.trantor.org.domain.service.OrgStructMdService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrgStructMdEnableJob {
    private final OrgStructMdService orgStructMdService;

    @Job(key = "ORG_STRUCT_ENABLED_JOB", name = "组织定时启用")
    public void execute() {
        log.info("组织定时启用任务开始");
        orgStructMdService.enabledJob();
        log.info("组织定时启用任务结束");
    }
}

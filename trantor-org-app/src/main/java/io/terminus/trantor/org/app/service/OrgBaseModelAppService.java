package io.terminus.trantor.org.app.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import io.terminus.common.api.exception.BusinessException;
import io.terminus.common.api.model.BaseModel;
import io.terminus.common.api.util.JsonUtils;
import io.terminus.trantor.org.spi.model.req.OrgQueryByIdReq;
import io.terminus.trantor.org.spi.model.req.OrgQueryByIdsReq;
import io.terminus.trantor.org.spi.model.req.OrgQueryByUkReq;
import lombok.AllArgsConstructor;
import org.apache.ibatis.session.SqlSession;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;
import java.util.function.Function;

@Service
@AllArgsConstructor
@SuppressWarnings({"unchecked", "rawtypes"})
public class OrgBaseModelAppService {

    public BaseModel queryById(OrgQueryByIdReq req) {
        Class<? extends BaseModel> modelClass = getModelClass(req.getModelKey());
        return execute(modelClass, baseMapper -> baseMapper.selectById(req.getId()));
    }

    public List<? extends BaseModel> queryByIds(OrgQueryByIdsReq req) {
        Class<? extends BaseModel> modelClass = getModelClass(req.getModelKey());
        return execute(modelClass, baseMapper -> baseMapper.selectBatchIds(req.getIds()));
    }

    public BaseModel queryByUk(OrgQueryByUkReq req) {
        Class<? extends BaseModel> modelClass = getModelClass(req.getModelKey());
        BaseModel model = JsonUtils.convert(req.getFields(), modelClass);
        QueryWrapper wrapper = Wrappers.query(model);
        return execute(modelClass, baseMapper -> baseMapper.selectOne(wrapper));
    }

    public List<? extends BaseModel> queryList(OrgQueryByUkReq req) {
        Class<? extends BaseModel> modelClass = getModelClass(req.getModelKey());
        BaseModel model = JsonUtils.convert(req.getFields(), modelClass);
        QueryWrapper wrapper = Wrappers.query(model);
        return execute(modelClass, baseMapper -> baseMapper.selectList(wrapper));
    }

    private Class<? extends BaseModel> getModelClass(String tableName) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(tableName);
        if (null == tableInfo) {
            throw new BusinessException("ORG::model.not.existed");
        }
        return (Class<? extends BaseModel>) tableInfo.getEntityType();
    }

    private <R> R execute(Class<? extends BaseModel> modelClass, Function<BaseMapper<? extends BaseModel>, R> function) {
        boolean active = TransactionSynchronizationManager.isActualTransactionActive();
        SqlSession sqlSession = SqlHelper.sqlSession(modelClass);
        try {
            BaseMapper<? extends BaseModel> baseMapper = SqlHelper.getMapper(modelClass, sqlSession);
            return function.apply(baseMapper);
        } finally {
            if (!active) {
                sqlSession.close();
            }
        }
    }
}

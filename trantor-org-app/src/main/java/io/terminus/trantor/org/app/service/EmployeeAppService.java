package io.terminus.trantor.org.app.service;

import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.StringRequest;
import io.terminus.gei.service.api.entity.ImportSliceData;
import io.terminus.trantor.org.domain.service.EmployeeService;
import io.terminus.trantor.org.domain.service.impt.EmployeeImportService;
import io.terminus.trantor.org.spi.model.dto.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @author: 张博
 * @date: 2023-07-11 10:11
 */
@Service
@RequiredArgsConstructor
public class EmployeeAppService {

    private final EmployeeService employeeService;
    private final EmployeeImportService employeeImportService;

    public EmployeeSaveDTO save(EmployeeSaveDTO request) {
        if (Objects.isNull(request.getId())) {
            return employeeService.create(request);
        } else {
            return employeeService.update(request);
        }
    }

    public EmployeeDTO queryDetail(Long id) {
        return employeeService.queryDetail(id);
    }

    public void delete(Long id) {
        employeeService.delete(id);
    }

    public void enable(Long id) {
        employeeService.enable(id);
    }

    public void disable(Long id) {
        employeeService.disable(id);
    }

    public Paging<EmployeeDTO> pageByOrg(EmployeePageQueryDTO request) {
        return employeeService.pageByOrg(request);
    }

    public Paging<EmployeeDTO> pageByOrgContainChildOrg(EmployeePageQueryDTO request) {
        return employeeService.pageByOrgContainChildOrg(request);
    }

    public Paging<EmployeeDTO> pageByUserOrgEmployee(EmployeePageQueryDTO request) {
        return employeeService.pageByUserOrgEmployee(request);
    }

    public List<Long> queryEmployeeOrg(UserOrgQueryDTO request) {
        return employeeService.queryUserOrgList(request);
    }

    public EmployeeDTO queryEmployeeByCode(String code) {
        return employeeService.queryEmployeeByCode(code);
    }

    public EmployeeDTO queryEmployeeById(Long id) {
        return employeeService.queryEmployeeById(id);
    }

    public List<EmployeeDTO> queryEmployeeByIds(Set<Long> ids) {
        return employeeService.queryEmployeeByIds(ids);
    }

    public EmployeeDTO queryEmployeeByUserId(Long userId) {
        return employeeService.queryEmployeeByUserId(userId);
    }

    public List<EmployeeDTO> queryEmployeeByUserIds(Set<Long> userIds) {
        return employeeService.queryEmployeeByUserIds(userIds);
    }

    public Paging<EmployeeDTO> paging(EmployeePageQueryDTO request) {
        return employeeService.paging(request);
    }

    public List<EmployeeDTO> queryOrgUnitAppointRankEmployee(OrgRankEmployeeQueryDTO request) {
        return employeeService.queryOrgUnitAppointRankEmployee(request);
    }

    public OrgAndRankDTO queryOrgUnitRoleByCode(String code) {
        return employeeService.queryOrgUnitRoleByCode(code);
    }

    public List<EmployeeRankDTO> queryEmployeeByOrgRankNew(List<EmployeeQueryOrgRoleDTO> request) {
        return employeeService.queryEmployeeByOrgRankNew(request);
    }

    public List<EmployeeDTO> queryEmployeeByOrgRank(List<EmployeeQueryOrgRoleDTO> request) {
        return employeeService.queryEmployeeByOrgRank(request);
    }

    public OrgAndRankDTO queryOrgUnitSupRankByCode(EmployeeSupQueryDTO employeeSupQueryDTO) {
        return employeeService.queryOrgUnitSupRankByCode(employeeSupQueryDTO);
    }

    public List<EmployeeDTO> queryEmployeeByCodes(EmployeeQueryCodes request) {
        return employeeService.queryEmployeeByCodes(request);
    }

    public List<EmployeeDTO> queryEmployeeByOrgUnitCode(OrgUnitCodeQueryDto request) {
        return employeeService.queryEmployeeByOrgUnitCode(request);
    }

    public List<Long> queryCurrentUserOrgAndAllChildOrg() {
        return employeeService.queryCurrentUserOrgAndAllChildOrg();
    }

    public List<Long> queryCurrentUserAllChildOrg() {
        return employeeService.queryCurrentUserAllChildOrg();
    }

    public List<Long> queryCurrentUserAllChildUser() {
        return employeeService.queryCurrentUserAllChildUser();
    }

    public String queryCurrentEmployeeForIam() {
        return employeeService.queryCurrentEmployeeForIam();
    }

    public List<Map<String, Object>> employeeImport(ImportSliceData request) {
        return employeeImportService.importData(request);
    }

    public List<EmployeeDTO> queryByNoticeScene(StringRequest request) {
        return employeeService.queryByNoticeScene(request);
    }
}

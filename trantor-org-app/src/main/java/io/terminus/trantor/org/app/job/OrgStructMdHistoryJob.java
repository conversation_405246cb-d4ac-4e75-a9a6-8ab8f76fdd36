package io.terminus.trantor.org.app.job;


import io.terminus.common.scheduler.annotation.Job;
import io.terminus.trantor.org.domain.service.OrgStructMdService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrgStructMdHistoryJob {
    private final OrgStructMdService orgStructMdService;

    @Job(key = "ORG_STRUCT_HISTORY_JOB", name = "组织定历史保存")
    public void historyJob() {
        orgStructMdService.historyJob();
    }
}

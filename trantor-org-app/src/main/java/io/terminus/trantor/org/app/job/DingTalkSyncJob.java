package io.terminus.trantor.org.app.job;

import io.terminus.common.scheduler.annotation.Job;
import io.terminus.trantor.org.domain.integration.dingtalk.service.DingTalkSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: 张博
 * @date: 2024-09-23 09:27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DingTalkSyncJob {

    private final DingTalkSyncService dingTalkSyncService;

    @Job(key = "ORG_DING_TALK_SYNC_ORG_JOB", name = "钉钉同步组织")
    public void syncOrg() {
        log.info("钉钉同步组织任务开始");
        dingTalkSyncService.syncOrg();
        log.info("钉钉同步组织任务结束");
    }

    @Job(key = "ORG_DING_TALK_SYNC_EMPLOYEE_JOB", name = "钉钉同步员工")
    public void syncEmployee() {
        log.info("钉钉同步员工任务开始");
        dingTalkSyncService.syncEmployee();
        log.info("钉钉同步员工任务结束");
    }
}

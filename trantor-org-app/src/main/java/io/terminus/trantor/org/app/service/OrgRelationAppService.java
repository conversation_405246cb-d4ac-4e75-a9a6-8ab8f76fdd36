package io.terminus.trantor.org.app.service;

import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.trantor.org.domain.service.OrgRelationService;
import io.terminus.trantor.org.spi.model.dto.OrgRelationRuleCfDTO;
import io.terminus.trantor.org.spi.model.dto.OrgRelationSaveDTO;
import io.terminus.trantor.org.spi.model.dto.attr.OrgBusinessTypeDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class OrgRelationAppService {

    private final OrgRelationService orgRelationService;

    public void save(OrgRelationSaveDTO request) {
        orgRelationService.save(request);
    }

    public void enabled(IdRequest request) {
        orgRelationService.enabled(request);
    }

    public void batchEnabled(IdsRequest request) {
        orgRelationService.batchEnabled(request);
    }

    public List<OrgBusinessTypeDTO> queryByDimensionId(IdRequest request) {
        return orgRelationService.queryByDimensionId(request);
    }

    public void OrgRelationRuleSave(OrgRelationRuleCfDTO request) {
        orgRelationService.OrgRelationRuleSave(request);
    }
}

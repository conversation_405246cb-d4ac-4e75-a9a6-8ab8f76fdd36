package io.terminus.trantor.org.app.service;

import io.terminus.trantor.org.domain.service.BizTypeService;
import io.terminus.trantor.org.spi.model.dto.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-07-18 10:27
 */
@Service
@RequiredArgsConstructor
public class BizTypeAppService {

    private final BizTypeService bizTypeService;

    public void save(BizTypeSaveDTO request) {
        bizTypeService.save(request);
    }

    public void delete(Long id) {
        bizTypeService.delete(id);
    }

    public List<BizTypeDTO> findAll(BizTypeQueryDTO request) {
        return bizTypeService.findAll(request);
    }

    public BizTypeHideFileDTO queryHiddeFiled(BizTypeHiddeFiledQueryDTO request) {
        return bizTypeService.queryHiddeFiled(request);
    }
}

package io.terminus.trantor.org.api.facade;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.model.Paging;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.request.IdsRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.spi.model.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-05-16 10:42
 */
@Api("员工信息读服务")
@FeignClient("trantor-org${TRANTOR_ORG_SERVICE_NAME:${trantor.engine.team-code:}}")
public interface EmployeeReadFacade {

    @ApiOperation("查询用户下的组织列表")
    @PostMapping("/api/trantor/org/employee/query-user-org")
    Response<List<Long>> queryEmployeeOrg(@RequestBody UserOrgQueryDTO request);

    @ApiOperation("根据员工编码查询员工信息")
    @PostMapping("/api/trantor/org/employee/query-by-code")
    Response<EmployeeDTO> queryEmployeeByCode(@RequestBody EmployeeQueryDTO request);

    @ApiOperation("根据员工ID查询员工信息")
    @PostMapping("/api/trantor/org/employee/query-by-id")
    Response<EmployeeDTO> queryEmployeeId(@RequestBody IdRequest request);

    @ApiOperation("根据员工ID集合查询员工信息")
    @PostMapping("/api/trantor/org/employee/query-by-ids")
    Response<List<EmployeeDTO>> queryEmployeeIds(@RequestBody IdsRequest request);

    @ApiOperation("根据用户ID查询员工信息")
    @PostMapping("/api/trantor/org/employee/query-by-user-id")
    Response<EmployeeDTO> queryEmployeeByUserId(@RequestBody EmployeeQueryDTO request);

    @ApiOperation("分页查询员工信息")
    @PostMapping("/api/trantor/org/employee/paging")
    Response<Paging<EmployeeDTO>> paging(@RequestBody EmployeePageQueryDTO request);

    @ApiOperation("查询指定组织单元绑定职级的员工")
    @PostMapping("/api/trantor/org/employee/org-appoint-rank-employee")
    Response<List<EmployeeDTO>> queryOrgUnitAppointRankEmployee(@RequestBody OrgRankEmployeeQueryDTO request);

    @ApiOperation("通过员工编码获取其组织角色信息")
    @PostMapping("/api/trantor/org/employee/query-org-role-by-code")
    Response<OrgAndRankDTO> queryOrgUnitRoleByCode(@RequestBody EmployeeQueryDTO request);

    @ApiOperation("通过组织角色获取员工信息")
    @PostMapping("/api/trantor/org/employee/query-employee-by-org-rank-new")
    Response<List<EmployeeRankDTO>> queryEmployeeByOrgRankNew(@RequestBody List<EmployeeQueryOrgRoleDTO> request);

    @Deprecated
    @ApiOperation("通过组织角色获取员工信息(废弃)")
    @PostMapping("/api/trantor/org/employee/query-employee-by-org-rank")
    Response<List<EmployeeDTO>> queryEmployeeByOrgRank(@RequestBody List<EmployeeQueryOrgRoleDTO> request);

    @ApiOperation("通过员工编码获取上级组织角色信息")
    @PostMapping("/api/trantor/org/employee/query-org-supRank-by-code")
    Response<OrgAndRankDTO> queryOrgUnitsupRankByCode(@RequestBody EmployeeSupQueryDTO request);

    @ApiOperation("通过批量员工编码查询员工")
    @PostMapping("/api/trantor/org/employee/query-employee-by-codes")
    Response<List<EmployeeDTO>> queryEmployeeByCodes(@RequestBody EmployeeQueryCodes request);

    @ApiOperation("通过组织查询组织下的员工")
    @PostMapping("/api/trantor/org/employee/query-employee-by-orgunit-code")
    Response<List<EmployeeDTO>> queryEmployeeByOrgUnitCode(@RequestBody OrgUnitCodeQueryDto request);

    @ApiOperation("当前用户的所在组织和下级组织id")
    @PostMapping("/api/trantor/org/employee/query-current-user-org-and-child-org")
    Response<List<Long>> queryCurrentUserOrgAndAllChildOrg();

    @ApiOperation("当前用户的下级组织id")
    @PostMapping("/api/trantor/org/employee/query-current-user-all-child-org")
    Response<List<Long>> queryCurrentUserAllChildOrg();

    @ApiOperation("当前用户的下级用户id")
    @PostMapping("/api/trantor/org/employee/query-current-user-all-child-user")
    Response<List<Long>> queryCurrentUserAllChildUser();

    @ApiOperation("获取当前的员工拼接信息")
    @PostMapping("/api/trantor/org/employee/query-current-employee-for-iam")
    Response<String> queryCurrentEmployeeForIam();
}

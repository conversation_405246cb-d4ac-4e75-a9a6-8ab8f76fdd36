package io.terminus.trantor.org.api.facade;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.spi.model.dto.EmployeeQueryDTO;
import io.terminus.trantor.org.spi.model.dto.OrgUnitDTO;
import io.terminus.trantor.org.spi.model.dto.OrgUnitInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author: 张博
 * @date: 2023-05-16 10:42
 */
@Api("组织单元信息读服务")
@FeignClient("trantor-org${TRANTOR_ORG_SERVICE_NAME:${trantor.engine.team-code:}}")
public interface OrgUnitReadFacade {

    @ApiOperation("根据员工编码查询员工的组织信息")
    @PostMapping("/api/trantor/org/unit/query-by-employee-code")
    Response<List<OrgUnitDTO>> queryOrgUnitByEmployeeCode(@RequestBody EmployeeQueryDTO request);

    @ApiOperation("通过组织id查询组织信息")
    @PostMapping("/api/trantor/org/unit/query-org-unit-by-id")
    Response<OrgUnitInfoDTO> queryOrgUnitById(@RequestBody IdRequest request);
}

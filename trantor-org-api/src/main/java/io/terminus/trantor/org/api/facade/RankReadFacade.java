package io.terminus.trantor.org.api.facade;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.api.request.IdRequest;
import io.terminus.common.api.response.Response;
import io.terminus.trantor.org.spi.model.dto.OrgRankCfDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api("职级信息读服务")
@FeignClient("trantor-org${TRANTOR_ORG_SERVICE_NAME:${trantor.engine.team-code:}}")
public interface RankReadFacade {

    @ApiOperation("通过职级ID查询职级信息")
    @PostMapping("/api/trantor/org/rank/query-rank-by-id")
    Response<OrgRankCfDTO> queryRankById(@RequestBody IdRequest request);
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>io.terminus.trantor</groupId>
        <artifactId>trantor-org</artifactId>
        <version>1.0.1.JASOLAR.DEV-SNAPSHOT</version>
    </parent>

    <artifactId>trantor-org-api</artifactId>
    <dependencies>
        <dependency>
            <groupId>io.terminus.common</groupId>
            <artifactId>terminus-common-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.trantor</groupId>
            <artifactId>trantor-org-spi</artifactId>
        </dependency>
    </dependencies>
</project>
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>io.terminus.trantor</groupId>
    <artifactId>trantor-org</artifactId>
    <version>1.0.1.JASOLAR.DEV-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>trantor-org</name>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <iam.sdk.version>0.6.0-SNAPSHOT</iam.sdk.version>
        <terminus.gei.version>24.0730.TEST-SNAPSHOT</terminus.gei.version>
        <terminus.trantor2.version>2.5.24.1130.12161458.RELEASE</terminus.trantor2.version>
    </properties>

    <modules>
        <module>trantor-org-adapter</module>
        <module>trantor-org-app</module>
        <module>trantor-org-domain</module>
        <module>trantor-org-infrastructure</module>
        <module>trantor-org-spi</module>
        <module>trantor-org-starter</module>
        <module>trantor-org-api</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.terminus.erp</groupId>
                <artifactId>erp-parent</artifactId>
                <version>2.0.0.250130.07021852.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.13.4</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.iam</groupId>
                <artifactId>iam-sdk</artifactId>
                <version>${iam.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dingtalk</artifactId>
                <version>2.1.47</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor2</groupId>
                <artifactId>trantor-permission-runtime-sdk</artifactId>
                <version>${terminus.trantor2.version}</version>
            </dependency>
            <!--redis依赖-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
            </dependency>
            <!--连接池依赖-->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor</groupId>
                <artifactId>trantor-org-adapter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor</groupId>
                <artifactId>trantor-org-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor</groupId>
                <artifactId>trantor-org-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor</groupId>
                <artifactId>trantor-org-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor</groupId>
                <artifactId>trantor-org-spi</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus.trantor</groupId>
                <artifactId>trantor-org-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.terminus</groupId>
                <artifactId>terminus-gei-service-api</artifactId>
                <version>${terminus.gei.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <distributionManagement>
        <repository>
            <id>terminus</id>
            <name>terminus release repository</name>
            <url>https://repo.terminus.io/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>terminus</id>
            <name>terminus snapshot repository</name>
            <url>https://repo.terminus.io/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>io.terminus</groupId>
                <artifactId>terminus-code-generation</artifactId>
                <version>1.0.7-RELEASE</version>
                <configuration>
                    <!-- 开发环境元数据相关信息，其他环境请自行替换 -->
                    <httpInfo>
                        <iamUrl>console-iam-dev.app.terminus.io</iamUrl>
                        <consoleUrl>trantor2-back.app.terminus.io</consoleUrl>
                        <loginApiEndpoint>/iam/api/v1/user/login/account</loginApiEndpoint>
                        <metaApiEndpoint>/api/trantor/struct-node/related-by-alias</metaApiEndpoint>
                        <account>admin</account>
                        <password>Terminus2020@</password>
                        <teamId>54</teamId>
                        <appId>85</appId>
                    </httpInfo>
                    <!-- 基本信息配置 -->
                    <generateCodeInfo>
                        <!-- 待生成代码的表信息 -->
                        <modelInfoList>
                            <modelInfo>
                                <!-- 分组名： io.terminus.trantor.org.spi.model.tc -->
                                <domain>tc</domain>
                                <modelCodeList>
                                    <value>xxx1</value>
                                    <value>xxx2</value>
                                </modelCodeList>
                            </modelInfo>
                        </modelInfoList>
                        <!-- 是否允许覆盖 -->
                        <allowOverwrite>true</allowOverwrite>
                        <!-- 是否允许 生成瞬时模型的关联模型 默认为false -->
                        <generateLinkObj>true</generateLinkObj>
                        <!-- 增加初始化DTO中link many List -->
                        <initOneToManyLink>false</initOneToManyLink>
                        <author>robot</author>
                        <!-- 生成代码的包名 -->
                        <tablePackageName>io.terminus.trantor.org</tablePackageName>
                    </generateCodeInfo>
                    <groupInfoList>
                        <!-- 按照不同的模板生成不同的目录 进行分组 -->
                        <groupInfo>
                            <savePath>trantor-org-spi</savePath>
                            <templateInfoList>
                                <templateInfo>
                                    <scope>MODEL</scope>
                                    <templateUrlList>
                                        <value>template/default/PO.java.vm</value>
                                        <value>template/default/Converter.java.vm</value>
                                    </templateUrlList>
                                </templateInfo>
                                <templateInfo>
                                    <scope>TRANSIENT_MODEL</scope>
                                    <templateUrlList>
                                        <value>template/default/DTO.java.vm</value>
                                        <value>template/default/VO.java.vm</value>
                                        <value>template/default/SO.java.vm</value>
                                        <value>template/default/CreateREQ.java.vm</value>
                                        <value>template/default/PageREQ.java.vm</value>
                                        <value>template/default/UpdateREQ.java.vm</value>
                                    </templateUrlList>
                                </templateInfo>
                                <templateInfo>
                                    <scope>DICT</scope>
                                    <templateUrlList>
                                        <value>template/default/Dict.java.vm</value>
                                    </templateUrlList>
                                </templateInfo>
                            </templateInfoList>
                        </groupInfo>
                        <groupInfo>
                            <savePath>trantor-org-infrastructure</savePath>
                            <templateInfoList>
                                <templateInfo>
                                    <scope>MODEL</scope>
                                    <templateUrlList>
                                        <value>template/default/Repo.java.vm</value>
                                    </templateUrlList>
                                </templateInfo>
                            </templateInfoList>
                        </groupInfo>
                    </groupInfoList>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

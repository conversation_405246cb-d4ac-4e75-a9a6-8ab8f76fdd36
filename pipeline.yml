version: "1.1"
name: ""
stages:
  - stage:
      - git-checkout:
          alias: git-checkout
          description: 代码仓库克隆
          version: "1.0"
          params:
            branch: feature/develop
            depth: 1
            password: ((gittar.password))
            uri: https://erda.cloud/terminus/dop/t-erp/trantor-organization
            username: ((gittar.username))
  - stage:
      - java:
          alias: mvn-compiler
          description: 编译打包构建
          params:
            build_type: maven
            container_type: spring-boot
            target: ./trantor-org-starter/target/trantor-org.jar
            workdir: ${git-checkout}
          resources:
            cpu: 0.5
  - stage:
      - git-checkout:
          alias: dice-yml
          description: dice.yml配置克隆
  - stage:
      - release:
          alias: release
          description: 用于打包完成时，向dicehub提交完整可部署的dice.yml。用户若没在pipeline.yml里定义该action，CI会自动在pipeline.yml里插入该action
          params:
            dice_yml: ${dice-yml}/dice.yml
            image:
              trantor-org: ${mvn-compiler:OUTPUT:image}
  - stage:
      - dice:
          alias: deploy
          description: dice平台应用部署
          params:
            release_id: ${release:OUTPUT:releaseID}
# 下载模块元数据配置
[[meta.download]]
name = "开发0630"
# 下载元数据的console地址
consoleUrl = "t-erp-console-dev.app.terminus.io"
# 下载元数据的iam地址
iamUrl = "t-erp-console-iam-dev.app.terminus.io"
# 模块teamId
teamId = 22
# 模块分支id
branchId = 22
# 模块key
moduleKey = "ERP_GEN"
# 同步元数据是否按照目录同步，如果目录有值，则只会同步该目录下的元数据
# 如果只需要同步多个子目录 eg:["采购/申请","采购/配置"]
folder = ["通用管理/组织"]

# 上传模块元数据配置
[[meta.upload]]
name = "开发0630"
# 上传元数据的console地址
consoleUrl = "t-erp-console-test.app.terminus.io"
# 上传元数据的iam地址
iamUrl = "t-erp-console-iam-test.app.terminus.io"
# 模块teamId
teamId = 22
# 模块分支id
branchId = 22
# 模块key
moduleKey = "ERP_GEN"
# 只会上传该目录下的元数据
folder = ["通用管理/组织"]
